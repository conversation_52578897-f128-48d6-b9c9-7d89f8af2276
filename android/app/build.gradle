apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services' // Mueve el plugin aquí

android {
  namespace "com.sanferconecta.app"
  compileSdk rootProject.ext.compileSdkVersion
  defaultConfig {
    applicationId "com.sanferconecta.app"
    minSdkVersion rootProject.ext.minSdkVersion
    targetSdkVersion rootProject.ext.targetSdkVersion
    versionCode 16
    versionName "4.2"
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    aaptOptions {
      // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
      // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
      ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
    }
  }
  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
}

repositories {
  google()
  mavenCentral()
  flatDir {
    dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
  }
}

dependencies {
  implementation fileTree(include: ['*.jar'], dir: 'libs')

  // Especifica las versiones directamente o define las variables correspondientes
  implementation 'androidx.appcompat:appcompat:1.6.1'
  implementation 'androidx.coordinatorlayout:coordinatorlayout:1.2.0'
  implementation 'androidx.core:core-splashscreen:1.0.1'

  implementation project(':capacitor-android')

  testImplementation 'junit:junit:4.13.2'
  androidTestImplementation 'androidx.test.ext:junit:1.1.5'
  androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

  implementation project(':capacitor-cordova-android-plugins')

  // Agrega el BoM de Firebase y las dependencias necesarias
  implementation platform('com.google.firebase:firebase-bom:32.0.0')
  implementation 'com.google.firebase:firebase-auth'
  implementation 'com.google.firebase:firebase-analytics'

  // Agrega Google Sign-In
  implementation 'com.google.android.gms:play-services-auth:20.7.0'
}

apply from: 'capacitor.build.gradle'

// Elimina el bloque try-catch relacionado con google-services
