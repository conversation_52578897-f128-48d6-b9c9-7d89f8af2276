// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-firebase-analytics')
    implementation project(':capacitor-firebase-authentication')
    implementation project(':capacitor-firebase-firestore')
    implementation project(':capacitor-firebase-messaging')
    implementation project(':capacitor-mlkit-barcode-scanning')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-capacitor-file-picker')
    implementation project(':capacitor-native-settings')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
