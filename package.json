{"name": "sanfer-conecta-angular-18", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/fire": "^18.0.1", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@angular/youtube-player": "^18.2.3", "@capacitor-firebase/analytics": "^6.1.0", "@capacitor-firebase/authentication": "^6.1.0", "@capacitor-firebase/firestore": "^6.1.0", "@capacitor-firebase/messaging": "^6.1.0", "@capacitor-mlkit/barcode-scanning": "^6.1.0", "@capacitor/android": "^6.1.2", "@capacitor/app": "6.0.1", "@capacitor/browser": "^6.0.2", "@capacitor/core": "6.1.2", "@capacitor/filesystem": "^6.0.1", "@capacitor/haptics": "6.0.1", "@capacitor/ios": "^6.1.2", "@capacitor/keyboard": "6.0.2", "@capacitor/preferences": "^6.0.2", "@capacitor/share": "^6.0.2", "@capacitor/splash-screen": "^6.0.2", "@capacitor/status-bar": "6.0.1", "@capawesome/capacitor-file-picker": "^6.0.1", "@ionic/angular": "^8.0.0", "@segment/analytics-next": "^1.75.0", "@vimeo/player": "^2.24.0", "angularx-qrcode": "^18.0.1", "animate.css": "^4.1.1", "capacitor-native-settings": "^6.0.1", "dompurify": "^3.1.6", "firebase": "^10.14.1", "ionicons": "^7.0.0", "marked": "^14.1.1", "ngx-pipes": "^3.2.2", "pdfmake": "^0.2.12", "rxjs": "~7.8.0", "swiper": "^11.1.12", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@angular/cli": "^18.0.0", "@angular/compiler-cli": "^18.0.0", "@angular/language-service": "^18.0.0", "@capacitor/cli": "6.1.2", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.0"}, "description": "An Ionic project"}