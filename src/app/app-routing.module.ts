import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import {authGuard, redirectIfLoggedGuard} from "./guards/auth.guard";
import {DataResolverService} from './services/data-resolver.service';


const routes: Routes = [

  {
    path: 'home',
    loadChildren: () => import('./pages/home/<USER>').then( m => m.HomePageModule)
  },
  {
    path: '',
    loadChildren: () => import('./pages/inicio/tabs/tabs.module').then(m => m.TabsPageModule),
    canActivate: [authGuard]
  },

  {
    path: 'login',
    loadChildren: () => import('./pages/login/login.module').then( m => m.LoginPageModule),
    canActivate: [redirectIfLoggedGuard]

  },
  {
    path: 'signup',
    loadChildren: () => import('./pages/signup/signup.module').then( m => m.SignupPageModule),
    canActivate: [redirectIfLoggedGuard]

  },
  {
    path: 'profile',
    loadChildren: () => import('./pages/profile/profile.module').then( m => m.ProfilePageModule)
  },
  {
    path: 'inicioalt',
    loadChildren: () => import('./pages/inicio/inicio.module').then( m => m.InicioPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'recover/:email',
    loadChildren: () => import('./pages/recover/recover.module').then( m => m.RecoverPageModule)
  },
  { path: 'vademecum',
    loadChildren: () => import('./pages/vademecum/vademecum.module').then(m => m.VademecumPageModule)
  },
  {
    path: 'vademecum-producto/:id',
    loadChildren: () => import('./pages/vademecum-producto/vademecum-producto.module').then(m => m.VademecumProductoPageModule)
  },
  {
    path: 'vademecum-producto-new/:id',
    loadChildren: () => import('./pages/vademecum-producto-new/vademecum-producto-new.module').then( m => m.VademecumProductoNewPageModule)
  },
  {
    path: 'welcome',
    loadChildren: () => import('./pages/welcome/welcome.module').then(m => m.WellcomePageModule)
  },

  {
    path: 'springer',
    loadChildren: () => import('./pages/springer/springer.module').then(m => m.SpringerPageModule)
  },
  { path: 'springer/:doi',
    loadChildren: () => import('./pages/springer-article/springer-article.module').then(m => m.SpringerArticlePageModule),
    canActivate: [authGuard]
  },
  {
    path: 'modelos-anatomicos/:category/:slug',
    resolve: { dataObtenida: DataResolverService},
    loadChildren: () => import('./pages/model3d/model3d.module').then(m => m.Model3dPageModule)
  },
  {
    path: 'modelos-anatomicos',
    loadChildren: () => import('./pages/modelos-anatomicos/modelos-anatomicos.module').then(m => m.ModelosAnatomicosPageModule) },
  {
    path: 'modelos-anatomicos/:slug',
    loadChildren: () => import('./pages/modelo-anatomico-subcategoria/modelo-anatomico-subcategoria.module').then(m => m.ModeloAnatomicoSubcategoriaPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'noticias',
    loadChildren: () => import('./pages/noticias/noticias.module').then(m => m.NoticiasPageModule)
  },
  {
    path: 'noticias/:slug',
    loadChildren: () => import('./pages/noticia/noticia.module').then(m => m.NoticiaPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'farmacovigilancia',
    loadChildren: () => import('./pages/farmacovigilancia/farmacovigilancia.module').then(m => m.FarmacovigilanciaPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'categorias/:id',
    loadChildren: () => import('./pages/noticias/categorias/categorias.module').then( m => m.CategoriasPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'productos/:slug',
    loadChildren: () => import('./pages/producto/producto.module').then( m => m.ProductoPageModule)
  },
  {
    path: 'academia',
    loadChildren: () => import('./pages/academia/academia.module').then( m => m.AcademiaPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'academia/:categoria',
    loadChildren: () => import('./pages/academia/cursos/cursos.module').then( m => m.CursosPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'academia/:categoria/:slug',
    loadChildren: () => import('./pages/academia/curso/curso.module').then( m => m.CursoPageModule),
    canActivate: [authGuard]

  },
  {
    path: 'recomendadciones',
    loadChildren: () => import('./pages/recomendadciones/recomendadciones.module').then( m => m.RecomendadcionesPageModule)
  },
  {
    path: 'notifications',
    loadChildren: () => import('./popovers/notifications/notifications.module').then( m => m.NotificationsPageModule)
  },
  {
    path: 'muestra-medica',
    loadChildren: () => import('./pages/muestra-medica/muestra-medica.module').then( m => m.MuestraMedicaPageModule),
    canActivate: [authGuard]
  },
  {
    path: 'signup-representante',
    loadChildren: () => import('./pages/signup-representante/signup-representante.module').then( m => m.SignupRepresentantePageModule)
  },
  {
    path: 'update-user',
    loadChildren: () => import('./pages/modals/update-user/update-user.module').then( m => m.UpdateUserPageModule)
  },
  { path: 'aviso-privacidad', loadChildren: () => import('./pages/aviso-privacidad/aviso-privacidad.module').then(m => m.AvisoPrivacidadPageModule) },
  {
    path: 'registrar-personal',
    loadChildren: () => import('./pages/signup-representante/signup-representante.module').then( m => m.SignupRepresentantePageModule)
  },
  {
    path: 'gracias',
    loadChildren: () => import('./pages/gracias/gracias.module').then( m => m.GraciasPageModule)
  },





];
@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {}
