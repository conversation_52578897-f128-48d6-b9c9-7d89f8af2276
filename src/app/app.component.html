<!--<ion-app>-->
<!--  <ion-router-outlet></ion-router-outlet>-->
<!--</ion-app>-->

<ion-app class="scanner-hide">
  <ion-split-pane when="(min-width: 4000px)" contentId="main" >
    <ion-menu  contentId="main" type="reveal" *ngIf="isLoggedIn" >
      <ion-header>
        <ion-toolbar color="tertiary">
          <ion-title><ion-img style="width:100px; margin: auto" src="../../assets/logo_sanfer.svg"></ion-img></ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-content color="tertiary">
        <ion-list>
          <ion-menu-toggle auto-hide="false" *ngFor="let p of appPages">
            <ion-item color="tertiary" [routerLink]="[p.url]">
              <ion-icon color="primary" slot="start" [name]="p.icon" ></ion-icon>
              <ion-label>
                {{p.title}}
              </ion-label>
            </ion-item>


          </ion-menu-toggle>

        </ion-list>
      </ion-content>
      <ion-item color="tertiary" (click)="openBrowser('aviso-privacidad')" >
        <ion-icon slot="start" name="information-circle" color="primary"></ion-icon>
        <ion-label>
          Aviso de Privacidad
        </ion-label>
      </ion-item>

      <ion-item (click)="signOut()" color="primary">
        <!--        <ion-icon slot="start" name="information-circle" color="primary"></ion-icon>-->
        <ion-label>
          Cerrar sesión
        </ion-label>
        <!--      <ion-button (click)="signOut()">Cerrar sesión</ion-button>-->

        <ion-icon name="log-out-outline"></ion-icon>

      </ion-item>

      <ion-item color="tertiary" >
        <!--        <ion-icon slot="start" name="information-circle" color="primary"></ion-icon>-->
        <ion-label class="ion-text-wrap" style="font-weight: bold; font-size: 10px">
          Laboratorios Sanfer S.A. de C.V. 2024 | Todos los derechos reservados <br>
          <div class="ion-padding"></div>

        </ion-label>
      </ion-item>
      <!--      <ion-button (click)="signOut()">Cerrar sesión</ion-button>-->
      <!--      <div class="ion-padding"></div>-->
    </ion-menu>

    <ion-router-outlet id="main"></ion-router-outlet>
  </ion-split-pane>
</ion-app>

<!-- Overlay del Scanner para los QRs -->
<!--<div  id="scanner" class="container-scanner qr-scanner-hide">-->
<!--  <div class="barcode-scanner&#45;&#45;area&#45;&#45;container">-->
<!--    <div class="relative-scanner">-->
<!--      <p>Escanea el código QR</p>-->
<!--    </div>-->
<!--    <div class="square-scanner surround-cover">-->
<!--      <div class="barcode-scanner&#45;&#45;area&#45;&#45;outer surround-cover">-->
<!--        <div class="barcode-scanner&#45;&#45;area&#45;&#45;inner"></div>-->
<!--      </div>-->
<!--    </div>-->
<!--    <div class="relative-scanner">-->
<!--      <p></p>-->
<!--    </div>-->
<!--    <ion-button (click)="closeScanner()" color="secondary" expand="block"> Cancelar</ion-button>-->
<!--  </div>-->
<!--</div>-->
