import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { StatusBar, Style } from "@capacitor/status-bar";
import { SplashScreen } from "@capacitor/splash-screen";
import { Router } from "@angular/router";
import { WidgetUtilService } from "./services/widget-util.service";
import { getAuth, onAuthStateChanged, signOut } from "@angular/fire/auth";
import { Capacitor } from "@capacitor/core";
import { AuthService } from "./services/auth.service";
import { initializeApp } from "firebase/app";
import { environment } from "../environments/environment";
import { BrowserService } from "./services/browser.service";
import { MenuController } from "@ionic/angular";
import { Firestore, collectionData, collection } from '@angular/fire/firestore';
import { inject } from '@angular/core';

import { SegmentService } from './services/segment.service';
import { AnalyticsBrowser } from '@segment/analytics-next';


@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {

  analytics: AnalyticsBrowser;

  firestore: Firestore = inject(Firestore);

  public appPages = [
    {
      title: 'Inicio',
      url: '/inicio',
      icon: 'home-outline'
    },
    {
      title: 'Mi cuenta',
      url: '/profile',
      icon: 'person-outline'
    },
    {
      title: 'Sanfer Academic',
      url: '/springer',
      icon: 'school-outline'
    },
    {
      title: 'Vademecum',
      url: '/vademecum',
      icon: 'flask-outline'
    },
    {
      title: 'Noticias',
      url: '/noticias',
      icon: 'newspaper-outline'
    },
    {
      title: 'Educación médica continua',
      url: '/cursos-categorias',
      icon: 'book-outline'
    }
  ];

  isStatusBarLight: boolean = true;
  isLoggedIn: boolean = false;
  private authSubscription: any;

  constructor(
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private authService: AuthService,
    private browser: BrowserService,
    private menu: MenuController,
    private segmentService: SegmentService
  ) {
    this.analytics = this.segmentService.getAnalytics();

    this.initializeFirebase();
  }

  // Method to initialize Firebase only for web platforms
  public async initializeFirebase(): Promise<void> {
    if (Capacitor.isNativePlatform()) {
      // Skip Firebase initialization on native platforms
      return;
    }
    // Initialize Firebase for web platforms
    initializeApp(environment.firebaseConfig);
  }

  async ngOnInit() {
    await this.getAuthState();
    await this.statusBarSplashScreen();
  }

  ngOnDestroy() {
    if (this.authSubscription) {
      this.authSubscription(); // Unsubscribe from onAuthStateChanged
    }
  }

  // Method to handle the status bar and splash screen for native platforms
  async statusBarSplashScreen() {
    if (Capacitor.isNativePlatform()) {
      // Native platform actions
      await StatusBar.setStyle({
        style: this.isStatusBarLight ? Style.Dark : Style.Light
      });
      this.isStatusBarLight = !this.isStatusBarLight;
      await SplashScreen.hide();
    } else {
      console.log('Running on web: Splash Screen and StatusBar actions skipped.');
    }
  }

  // Method to track authentication state and set user login status
  async getAuthState() {
    try {
      const auth = getAuth();
      this.authSubscription = onAuthStateChanged(auth, (user) => {
        this.isLoggedIn = !!user;
      });
    } catch (e: any) {
      await this.widgetUtilService.presentToastError(e.message);
    }
  }

  // Open an external URL in the browser
  async openBrowser(url: string) {
    await this.menu.close(); // Close the menu before opening the browser
    console.log('URL: ', url);
    await this.browser.openBrowser(url);
  }

  // Handle navigation based on user login status
  async handleNavigation(isLogged: boolean) {
    const currentUrl = this.router.url.split('/')[1];
    if (isLogged && ['login', 'signup', 'home'].includes(currentUrl)) {
      await this.router.navigate(['/inicio']);
    } else if (!isLogged) {
      await this.router.navigate(['/login']);
    }
  }

  // Sign out method, uses AuthService for managing authentication
  async signOut() {
    await this.authService.signOut();
  }

  // Method to close the QR scanner safely
  closeScanner() {
    document.body.classList.remove('qrscanner');
    const scannerElement = document.getElementById('scanner');
    if (scannerElement && !scannerElement.classList.contains('qr-scanner-hide')) {
      scannerElement.classList.add('qr-scanner-hide');
    }
  }
}
