import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy, RouterModule } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getAuth, indexedDBLocalPersistence, initializeAuth, provideAuth } from '@angular/fire/auth';
import { getAnalytics, provideAnalytics, ScreenTrackingService, UserTrackingService } from '@angular/fire/analytics';
import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { getFunctions, provideFunctions } from '@angular/fire/functions';
import { getMessaging, provideMessaging } from '@angular/fire/messaging';
import { getRemoteConfig, provideRemoteConfig } from '@angular/fire/remote-config';
import { environment } from "../environments/environment";
import { FirebaseAuthenticationService } from "./services/firebase-authentication.service";
import { provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { Capacitor } from "@capacitor/core";
import { QRCodeModule } from "angularx-qrcode";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { AngularFirestoreModule } from '@angular/fire/compat/firestore';
import { NotificationsPage } from "./popovers/notifications/notifications.page";
import { UpdateUserPage } from "./pages/modals/update-user/update-user.page";
import { FormsModule } from "@angular/forms";

@NgModule({
  declarations: [AppComponent, NotificationsPage, UpdateUserPage],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    IonicModule.forRoot(),
    AppRoutingModule,
    QRCodeModule,
    RouterModule,
    FormsModule,
    BrowserAnimationsModule,
    AngularFirestoreModule, // Asegúrate de que esté importado correctamente
  ],
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    ScreenTrackingService,
    UserTrackingService,
    provideFirebaseApp(() => {
      const app = initializeApp(environment.firebaseConfig);
      if (Capacitor.isNativePlatform) {
        initializeAuth(app, {
          persistence: indexedDBLocalPersistence
        });
      }
      return app;
    }),
    provideAuth(() => getAuth()),
    provideAnalytics(() => getAnalytics()),
    provideFirestore(() => getFirestore()),
    provideFunctions(() => getFunctions()),
    provideMessaging(() => getMessaging()),
    provideRemoteConfig(() => getRemoteConfig()),
    provideHttpClient(withInterceptorsFromDi())
  ]
})
export class AppModule {}
