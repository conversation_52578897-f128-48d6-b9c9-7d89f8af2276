import {Component, Input, ViewChild, ElementRef, AfterViewInit, HostListener} from '@angular/core';

@Component({
  selector: 'app-youtube-player',
  templateUrl: './youtube-player.component.html',
  styleUrls: ['./youtube-player.component.scss']
})
export class YoutubePlayerComponent implements AfterViewInit {
  @Input() videoId: string = '';
  @Input() autoplay: boolean = false; // Añade esta línea
  @ViewChild('ytPlayer') ytPlayerRef: ElementRef;

  ytPlayer: YT.Player;
  isPlayerReady: boolean = false;
  showOverlay: boolean = true;

  width: number = window.innerWidth;
  height: number = this.calculateAspectRatioHeight();


  ngAfterViewInit(): void {
    if (window['YT']) {
      this.initYoutubePlayer();
    } else {
      window['onYouTubeIframeAPIReady'] = () => this.initYoutubePlayer();
    }
  }
  onPlayerReady(event: any): void {
    console.log("Player is ready");
    this.isPlayerReady = true;
  }

  onPlayerStateChange(event: YT.OnStateChangeEvent): void {
    if (event.data === YT.PlayerState.PAUSED || event.data === YT.PlayerState.ENDED) {
      this.showOverlay = true;
    } else if (event.data === YT.PlayerState.PLAYING) {
      this.showOverlay = false;
    }
  }

  togglePlay(): void {
    if (this.showOverlay) {
      this.ytPlayer.playVideo();
      this.showOverlay = false;
    } else {
      this.ytPlayer.pauseVideo();
      this.showOverlay = true;
    }
  }


  initYoutubePlayer() {
    this.ytPlayer = new YT.Player(this.ytPlayerRef.nativeElement, {
      videoId: this.videoId,
      playerVars: {
        autoplay: this.autoplay ? 1 : 0,
        controls: 0,
        modestbranding: 1,
        rel: 0,
        showinfo: 0,
        fs: 1,
      },
      events: {
        'onReady': (event) => this.onPlayerReady(event),
        'onStateChange': (event) => this.onPlayerStateChange(event)
      }
    });
  }

  calculateAspectRatioHeight() {
    return (this.width / 16) * 9;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: Event) {
    this.width = window.innerWidth;
    this.height = this.calculateAspectRatioHeight();
  }
}
