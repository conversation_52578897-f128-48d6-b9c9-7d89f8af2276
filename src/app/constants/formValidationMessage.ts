export const LOGIN = {

    email: {

        required: 'Email es requerido',
        email: 'El correo no es valido'


    },
    password: {
        required: 'Contrasñea es requerida',
        minlength: 'La contraseña debe de contener almenos 5 caracteres'


    }

};
export const RECOVER = {

    email: {

        required: 'Email es requerido',
        email: 'El correo no es valido'


    }

};

export const FARMACO = {

    nombrePaciente: {

        required: 'Nombre del paciente o sus inciales son requeridas'

    },
    fechaDeNaciemiento: {
        required: 'Requerimos la recha de nacimiento del paciente'


    },
    genero: {
        required: 'Por favor seleciona el genero del paciente'
    },
    peso: '',
    talla: '',
    descripcionDeSospecha: {
        required: 'Describa la sospecha de reacción adversa al medicamento (SRAM)'
    },
    indicarSiEs: '',
    fechadDeInicio: {
        required: 'Informenos la fecha de inicio del tratamiento'
    },
    fechaDeTermino: {
        required: 'Informenos la fecha de termino del tratamiento'
    },
    disminuyoLaDosis: {
        required: '¿Se disminuyo la dosis?'
    },
    readministroMedicamento: {
        required: '¿Se readministro medicamento?'
    },
    recuperoDeLaSRAM: {
        required: '¿Se recupero el paciente de la SRAM?'
    },
    cambioFarmacoterapia: {
        required: '¿Se cambió la farmacoterapia?'
    },
    suspendioMedicamento: {
        required: '¿Suspendió el medicamento debido a la SRAM?'
    },
    mejoriaAlSuspender: {
        required: '¿Hubo mejoría al suspender el medicamento?'
    },
    medicamentoParaTratar: {
        required: 'Es importante que nos informe si se uso un medicamento adicional para tratar la SRAM'
    },
    presentoNuevementeLaSRAM: {
        required: 'Cuando se readministro el medicamento, ¿se presentó nuevamente la SRAM?'
    },
    nombreComercial: {
        required: 'Seleccione el producto usado del listado'
    },
    formaFarmaceutica: '',
    nombreGenerico: {
        required: 'Requerido'
    },
    numLote: {
        required: 'Requerido'
    },
    viaAdministracion: {
        required: 'Requerido'
    },
    caducidad: {
        required: 'Fecha de caducidad'
    },
    paraQueEnfermedad: {
        required: 'Requerido'
    },
    dosis: {
        required: 'Requerido'
    },
    horario: {
        required: 'Requerido'
    },
    otroMedicamento: {
        required: '¿Tenía tratamiento con algún otro medicamento?'
    },
    antecedentesAlergias: {
        required: 'Requerido'
    },
    alergias: '',
    datosRelevantes: {
        required: 'Datos relevantes de la historia clínica del paciente'
    },
    condicionMedica: {
        required: 'Seleccione una de las opciones listadas a continuación'
    },
    conacto: {
        required: 'Díganos si gustaría que lo contactará un miembro del equipo de Farmacovigilancia'
    },
    telefonoAlterno: '',
    correoAlterno: '',
    avisoPrivacidad:  {
        required: 'Por favor acepta la política de privacidad',
    },

};



export const SIGNUPRECOMENDADO = {
    nombre: {

        required: 'Requerimos su nombre'


    },
    apellido: {
        required: 'Requerimos su nombre'

    },


    email: {

        required: 'Email es requerido',
        email: 'El correo no es valido'


    },

    password: {
        required: 'Contrasñea es requerida',
        minlength: 'La contraseña debe de contener almenos 8 caracteres'


    },
    confirmPassword: {
        required: 'Requerimos que confirmes tu Contraseña',
        minlength: 'La contraseña debe de contener almenos 8 caracteres',

        matching: 'Los passwords no coinciden'
    },
    representante: {


    },
    aceptar: {
        required: 'Aceptas haber guardado tu password'

    }



};

export const COMPLETAR = {
    nombre: {

        required: 'Requerimos tu nombre'


    },
    apellido: {
        required: 'Requerimos tu nombre'

    },


    cedula: {

        required: 'Requerimos tu cédula profesional',
        // email: 'El correo no es valido'


    },

    telefono: {
        required: 'Requerimos nos proporciones un número celular a 10 digitos',
        minlength: 'El telefono debe de contener 10 caracteres',
        maxlength: 'El telefono no pruede tener más de 10 caracteres'


    }

};



export const SIGNUP = {
    nombre: {

        required: 'Requerimos su nombre'


    },
    apellido: {
        required: 'Requerimos su nombre'

    },
    cedula: {

        required: 'Su Cédula profesional es requerida'


    },
    titulo: {
        required: 'VERIFIQUE SU NOMBRE COMPLETO Y CEDULA PROFESIONAL',
        noMedico: 'Sanfer conecta es una aplicación especializada para profesionales de la salud, ' +
            'por favor ingrese su nombre completo y su cédula profesional, despues presione el boton de ' +
            '"Verificar cédula profesional"'



    },

    email: {

        required: 'Email es requerido',
        email: 'El correo no es valido'


    },
    telefono: {
        required: 'El teléfono es requerido',
        minlength: 'Ingrese su telefono a 10 digitos'

    },
    password: {
        required: 'Contrasñea es requerida',
        minlength: 'La contraseña debe de contener almenos 8 caracteres'


    }



};


export const SIGNUPREPRE = {
  passcode: {
    required: 'Ingresa el codígo de validación proporcionado',
    invalidPasscode: 'El código proporcionado no es valido',
  },
  nombre: {
    required: 'Requerimos su nombre'
  },
  apellido: {
    required: 'Requerimos su nombre'
  },
  email: {
    required: 'Email es requerido',
    email: 'El correo no es valido'
  },
  telefono: {
    required: 'El teléfono es requerido',
    minlength: 'Ingrese su telefono a 10 digitos'
  },
  password: {
    required: 'Contrasñea es requerida',
    minlength: 'La contraseña debe de contener almenos 8 caracteres'
  },
  nEmpleado: {
    required: 'Escribe tu numero de empleado sin la letra M',
  }
};


export const ADDPRODUCT = {

    nombre: {
        required: 'Requerimos el nombre del producto'
    },
    cantidad: {
        required: 'La Candidad es requerida'
    },
    sku: {
        required: 'Requerimos el identificador del producto'
    },

    costo: {
        required: 'Ingresa un costo del producto, si no lo tienes agrega un 0(cero)'
    },
    nota: {
        required: 'Agrega una anotación del producto'
    }



};

export const ADDEVENT = {
    nombre: {
        required: 'Nombre es requerido'
    },
    representante: {
        required: 'Candidad es requerida'
    },
    linea: {
        required: 'Linea es requerida'
    },

    fechaInicio: {
        required: 'Selecciona la fecha en la que inicia el evento'
    },
    fechaFinal: {
        required: 'Selecciona la fecha en la que finaliza el evento'
    }
};

