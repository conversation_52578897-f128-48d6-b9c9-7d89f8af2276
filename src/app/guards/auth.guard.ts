import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, from, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { getAuth, onAuthStateChanged } from '@angular/fire/auth';
import { Capacitor } from '@capacitor/core';
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';
import { Preferences } from "@capacitor/preferences";

export const authGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Observable<boolean | UrlTree> => {
  const router = inject(Router);
  const auth = getAuth();
  // console.log('Guardia Init 1')

  const storeReturnUrl = (url: string): Promise<void> => {
    return Preferences.set({
      key: 'returnUrl',
      value: url
    });
  };

  if (Capacitor.isNativePlatform()) {
    return from(FirebaseAuthentication.getCurrentUser()).pipe(
      switchMap(response => {
        const user = response.user;
        if (user && user.uid) {
          return of(true);
        } else {
          // Guardar la URL de retorno en las preferencias
          return from(storeReturnUrl(state.url)).pipe(
            map(() => router.createUrlTree(['/home']))
          );
        }
      })
    );
  } else {
    return new Observable<boolean | UrlTree>(observer => {
      onAuthStateChanged(auth, (user) => {
        if (user) {
          observer.next(true);
        } else {
          // Guardar la URL de retorno en las preferencias
          storeReturnUrl(state.url).then(() => {
            observer.next(router.createUrlTree(['/home']));
            observer.complete();
          });
        }
      });
    });
  }
};

export const redirectIfLoggedGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
): Observable<boolean | UrlTree> => {
  const router = inject(Router);
  const auth = getAuth();

  if (Capacitor.isNativePlatform()) {
    return from(FirebaseAuthentication.getCurrentUser()).pipe(
      map(response => {
        const user = response.user;
        if (user && user.uid) {
          return router.createUrlTree(['/inicio']); // Redirige a la página de inicio si está autenticado
        }
        return true;
      })
    );
  } else {
    return new Observable<boolean | UrlTree>(observer => {
      onAuthStateChanged(auth, (user) => {
        if (user) {
          observer.next(router.createUrlTree(['/inicio'])); // Redirige a la página de inicio si está autenticado
        } else {
          observer.next(true);
        }
        observer.complete();
      });
    });
  }
};
