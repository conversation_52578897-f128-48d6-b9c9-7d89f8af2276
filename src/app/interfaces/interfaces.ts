export interface RespuestaSpringer {
    apiMessage: string;
    query: string;
    apiKey: string;
    result: Result[];
    records: Records[];
    facets: Facets[];
}
export interface Result {
  total: string;
  start: string;
  pageLength: string;
  recordsDisplayed: string;
}

export interface Url {
  format: string;
  platform: string;
  value: string;
}

export interface Creators {
  creator: string;
}

export interface Records {
  contentType: string;
  identifier: string;
  url: Url[];
  title: string;
  creators: Creators[];
  publicationName: string;
  openaccess: string;
  doi: string;
  publisher: string;
  publicationDate: string;
  publicationType: string;
  issn: string;
  eIssn: string;
  volume: string;
  number: string;
  issueType: string;
  topicalCollection: string;
  genre: string;
  startingPage: string;
  endingPage: string;
  journalId: string;
  printDate: string;
  onlineDate: string;
  coverDate: string;
  copyright: string;
  abstract: string;
  conferenceInfo: any[];
}

export interface Values {
  value: string;
  count: string;
}

export interface Facets {
  name: string;
  values: Values[];
}


