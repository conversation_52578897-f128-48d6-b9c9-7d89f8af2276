    export interface RespuestaModelos3D {
        id: number;
        NombreCategoria: string;
        created_at: Date;
        updated_at: Date;
        Subcategoria: Subcategoria[];
        ImagenCategoria: ImagenCategoria;
        slug: string;
    }
    export interface Modelo3D {
        id: number;
        Nombre: string;
        Identificador: string;
        Descripcion?: any;
        Imagen_principal?: any;
    }

    export interface Small {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        path?: any;
        size: number;
        width: number;
        height: number;
    }

    export interface Thumbnail {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        path?: any;
        size: number;
        width: number;
        height: number;
    }

    export interface Formats {
        small: Small;
        thumbnail: Thumbnail;
    }

    export interface ImagenSubcategoria {
        id: number;
        name: string;
        alternativeText: string;
        caption: string;
        width: number;
        height: number;
        formats: Formats;
        hash: string;
        ext: string;
        mime: string;
        size: number;
        url: string;
        previewUrl?: any;
        provider: string;
        provider_metadata?: any;
        created_at: Date;
        updated_at: Date;
    }

    export interface Subcategoria {
      id: number;
      Identificador: string
      Subcategoria: string;
      Modelo3D: Modelo3D[];
      ImagenSubcategoria: ImagenSubcategoria;
      url: string
      Nombre: string,
      Imagen_principal: any
    }

    export interface Small2 {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        path?: any;
        size: number;
        width: number;
        height: number;
    }

    export interface Thumbnail2 {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        path?: any;
        size: number;
        width: number;
        height: number;
    }

    export interface Formats2 {
        small: Small2;
        thumbnail: Thumbnail2;
    }

    export interface ImagenCategoria {
        id: number;
        name: string;
        alternativeText: string;
        caption: string;
        width: number;
        height: number;
        formats: Formats2;
        hash: string;
        ext: string;
        mime: string;
        size: number;
        url: string;
        previewUrl?: any;
        provider: string;
        provider_metadata?: any;
        created_at: Date;
        updated_at: Date;
    }

