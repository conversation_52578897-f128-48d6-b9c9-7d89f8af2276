
export interface RespuestaStrapi {
    id: number;
    Titulo: string;
    Noticia: string;
    created_at: Date;
    updated_at: Date;
    Abstracto: string;
    Imagen_principal: ImagenPrincipal;
    Categoria: string;
  Activado: boolean;
  Slides: any;
  Signup: any;
  titulos: any;






    Nombre: string;
    Descripcion: string;
    lineaNegocio?: any;
    Contenido?: any;
    PrincipioActivo: string;
    SKU: string;
    Registro_sanitario: string;
    Precio?: any;
    Concentracion: string;
    Presentacion: string;
    Unidad_de_negocio: string;
    Laboratorio: string;
    Tipo_de_producto: string;
    Imagen_de_producto: ImagenDeProducto[];





}



export interface Small {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    path?: any;
    size: number;
    width: number;
    height: number;
}

export interface Medium {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    path?: any;
    size: number;
    width: number;
    height: number;
}

export interface Thumbnail {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    path?: any;
    size: number;
    width: number;
    height: number;
}

export interface Large {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    path?: any;
    size: number;
    width: number;
    height: number;
}

export interface Formats {
    small: Small;
    medium: Medium;
    thumbnail: Thumbnail;
    large: Large;
}

export interface ImagenPrincipal {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    width: number;
    height: number;
    formats: Formats;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl?: any;
    provider: string;
    provider_metadata?: any;
    created_at: Date;
    updated_at: Date;
}






export interface ImagenDeProducto {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    width: number;
    height: number;
    formats: Formats;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl?: any;
    provider: string;
    provider_metadata?: any;
    created_at: Date;
    updated_at: Date;
}



