export interface MedicoResponseAPI {
  onekey_id:               string;
  id_usuario:              number;
  customer_type:           string;
  customer_id:             string;
  tipo_cliente:            string;
  apellidos:               string;
  nombres:                 string;
  nombre_completo:         string;
  sucursal:                string;
  external_id_2:           string;
  disan_id:                string;
  calle:                   string;
  colonia:                 string;
  ciudad:                  string;
  estado:                  string;
  pais:                    string;
  cpostal:                 string;
  cedula_gral:             string;
  cedula_esp:              string;
  cod_esp1:                string;
  especialidad_1:          string;
  cod_esp2:                string;
  especialidad_2:          string;
  sexo:                    string;
  fecha_nac:               string;
  cod_audiencia:           string;
  cadena:                  string;
  cufa:                    string;
  num_determinante:        string;
  tipo_organizacion:       string;
  region:                  string;
  rutas:                   string;
  affiliation_id:          string;
  address_id:              string;
  email_address:           string;
  phones:                  string;
  status:                  string;
  validation_status:       string;
  codigo_sub_especialidad: string;
  sub_especialidad:        string;
  especialidad_sanfer:     string;
  birth_year:              string;
  birth_month:             string;
  birth_day:               string;
  direcciones:             Direccione[];
}

export interface Direccione {
  calle:   string;
  colonia: string;
  ciudad:  string;
  estado:  string;
  pais:    string;
  cpostal: string;
}



export interface RepresentanteResponseAPI {
  id_oce:                        number;
  territory_name:                string;
  user_oce_id:                   string;
  alias:                         string;
  unique_integration_id:         string;
  user_role_name:                string;
  user_id:                       string;
  full_name:                     string;
  name:                          string;
  lastname:                      string;
  username:                      string;
  email_address:                 string;
  profile_name:                  string;
  manager_id:                    string;
  manager_unique_integration_id: string;
  manager_full_name:             string;
  manager_name:                  string;
  manager_lastname:              string;
  manager_username:              string;
  manager_email_address:         string;
  manager_profile_name:          string;
  manager_division:              string;
  is_active:                     string;
  territory_id:                  string;
  ruta:                          string;
  fuerza:                        string;
  visita_medicos:                string;
  visita_farmacias:              string;
  visita_hospitales:             string;
  parent_territory_id:           string;
  parent_territory_name:         string;
}
