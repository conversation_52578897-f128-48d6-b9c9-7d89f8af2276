export interface Medico {

//Data del usurio principal
  cedula: string;
  nombre: string;
  titulo: string
  paterno: string
  materno: string
  institucion: string
  tipo: string
  anioRegistro: string,
  genero: string
//Role del usuario: siempre es medico
  role: string


//Datos de contacto
  email: string
  telefono: string

//Time Stamps
  fechaFinal: string
  Registro: string,
  fechaMs: string,

//Datos de la nueva base de datos

  ONEKEY_ID: string,
  CUSTOMER_TYPE: string,
  CUSTOMER_ID: string,
  SUCURSAL: string,
  CALLE: string,
  COLONIA: string,
  CIUDAD: string,
  ESTADO: string,
  PAIS: string,
  CPOSTAL: string,
  CEDULA_GRAL: string,
  CEDULA_ESP: string,
  COD_ESP1: string,
  ESPECIALIDAD_1: string,
  COD_ESP2: string,
  ESPECIALIDAD_2: string,
  SEXO: string,
  FECHA_NAC: string,
  REGION: string,
  RUTAS: string,
  AFFILIATION_ID: string,
  ADDRESS_ID: string,
  EMAIL_ADDRESS: string,
  PHONES: string,
  STATUS: string,
  VALIDATION_STATUS: string,
  CODIGO_SUB_ESPECIALIDAD: string,
  SUB_ESPECIALIDAD: string,
  ESPECIALIDAD_SANFER: string,
  BIRTH_YEAR: string,
  BIRTH_MONTH: string,
  BIRTH_DAY: string

}
