export interface Publication {
  id:           number;
  title:        string;
  type:         string;
  videoURL:     null;
  imageURL:     null;
  pdfURL:       null | string;
  modelURL:     null;
  textButton:   null | string;
  textContent:  null | string;
  link:         null | string;
  switchButton: boolean | null;
  sourceInfo:   null;
  sourceLink:   null;
  modalColor:   null | string;
  validity:     null;
  published_at: Date;
  created_at:   Date;
  updated_at:   Date;
  image:        Image | null;
  gallery:      Image[];
}



export interface Image {
  id:                number;
  name:              string;
  alternativeText:   string;
  caption:           string;
  width:             number;
  height:            number;
  formats:           ImageFormats;
  hash:              string;
  ext:               EXT;
  mime:              MIME;
  size:              number;
  url:               string;
  previewUrl:        null;
  provider:          Provider;
  provider_metadata: null;
  created_at:        Date;
  updated_at:        Date;
}

export enum EXT {
  JPEG = ".jpeg",
  Jpg = ".jpg",
  PNG = ".png",
}

export interface ImageFormats {
  large?:    Large;
  small:     Large;
  medium?:   Large;
  thumbnail: Large;
}

export interface Large {
  ext:    EXT;
  url:    string;
  hash:   string;
  mime:   MIME;
  name:   string;
  path:   null;
  size:   number;
  width:  number;
  height: number;
}

export enum MIME {
  ImageJPEG = "image/jpeg",
  ImagePNG = "image/png",
}

export enum Provider {
  Local = "local",
}
