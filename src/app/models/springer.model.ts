export interface SpringerResponse {
  contentType:       string;
  identifier:        string;
  language:          string;
  url:               URL[];
  title:             string;
  creators:          Creator[];
  publicationName:   string;
  openaccess:        string;
  doi:               string;
  publisher:         string;
  publisherName:     string;
  publicationDate:   Date;
  publicationType:   string;
  issn:              string;
  eIssn:             string;
  volume:            string;
  number:            string;
  issueType:         string;
  topicalCollection: string;
  genre:             string[];
  startingPage:      string;
  endingPage:        string;
  journalId:         string;
  printDate:         Date;
  onlineDate:        Date;
  coverDate:         string;
  copyright:         string;
  abstract:          string;
  conferenceInfo:    any[];
  keyword:           string[];
  subjects:          string[];
  disciplines:       Discipline[];
}

export interface Creator {
  creator: string;
  ORCID?:  string;
}

export interface Discipline {
  id:   string;
  term: string;
}

export interface URL {
  format:   string;
  platform: string;
  value:    string;
}
