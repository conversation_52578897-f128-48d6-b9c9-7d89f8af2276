export interface VademecumsResponse {
  id:                 number;
  Nombre:             string;
  Descripcion:        string;
  lineaNegocio:       string;
  Contenido:          string;
  PrincipioActivo:    string;
  SKU:                string;
  Registro_sanitario: string;
  Concentracion:      string;
  Precio:             null;
  Unidad_de_negocio:  null;
  Laboratorio:        string;
  Tipo_de_producto:   null;
  URLBP:              null;
  BP:                 boolean;
  precio_max:         null;
  published_at:       Date;
  created_at:         Date;
  updated_at:         Date;
  ippSwitch:          boolean;
  ean:                string;
  udn:                string;
  promocionesSwitch:  null;
  presentacionSwitch: null;
  Promociones:        any[];
  ipp:                IPP[];
  presentacion:       Presentacion[];
  Imagen_de_producto: ImagenDeProducto[];
  logo:               null;
}

export interface ImagenDeProducto {
  id:                number;
  name:              string;
  alternativeText:   string;
  caption:           string;
  width:             number;
  height:            number;
  formats:           Formats;
  hash:              string;
  ext:               string;
  mime:              string;
  size:              number;
  url:               string;
  previewUrl:        null;
  provider:          string;
  provider_metadata: null;
  created_at:        Date;
  updated_at:        Date;
}

export interface Formats {
  small:     Small;
  thumbnail: Small;
}

export interface Small {
  ext:    string;
  url:    string;
  hash:   string;
  mime:   string;
  name:   string;
  path:   null;
  size:   number;
  width:  number;
  height: number;
}

export interface IPP {
  id:          number;
  title:       string;
  description: string;
  color:       null;
  image:       null;
}

export interface Presentacion {
  id:                 number;
  Nombre:             null;
  Presentacion:       string;
  Descripcion:        null;
  SKU:                null;
  Registro_sanitario: null;
  URLBP:              null;
  adicional:          null;
  tituloAdicional:    null;
  ean:                null;
  Imagen_de_producto: null;
}
