import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { AcademiaPage } from './academia.page';

const routes: Routes = [
  {
    path: '',
    component: AcademiaPage
  },
  {
    path: 'cursos',
    loadChildren: () => import('./cursos/cursos.module').then( m => m.CursosPageModule)
  },
  {
    path: 'curso',
    loadChildren: () => import('./curso/curso.module').then( m => m.CursoPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AcademiaPageRoutingModule {}
