import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AcademiaPageRoutingModule } from './academia-routing.module';

import { AcademiaPage } from './academia.page';
import { NgPipesModule} from "ngx-pipes";
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    AcademiaPageRoutingModule,
    NgPipesModule,
    SharedComponentsModule
  ],
  declarations: [AcademiaPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class AcademiaPageModule {}
