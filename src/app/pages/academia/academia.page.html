<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Academia Sanfer</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="cursosRefresh($event)" *ngIf="userInStorage.role === 'medico'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>


  <ion-grid [fixed]="true">


    <ion-row>
      <ion-col>
        <swiper-container pagination="true" slides-per-view="1"  class="ion-no-padding ion-no-margin swiperStyleSanfer" >
          <swiper-slide *ngFor="let slide of bannerAcademia">
            <ion-card class="slide-academia">
              <ion-img *ngIf="slide.type === 'image'" (click)="openBrowser(slide.link)" [src]="strapiUrl + slide.image.url"></ion-img>
              <app-youtube-player *ngIf="slide.type === 'video'" [videoId]="slide.videoURL" [autoplay]="false"></app-youtube-player>

            </ion-card>
          </swiper-slide>
        </swiper-container>
      </ion-col>

    </ion-row>
    <ion-row>
      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>

      <ion-col size="12" size-xl="8" size-lg="8" size-md="10" size-sm="12" size-xs="12">
        <ion-row >
          <ion-col class="ion-text-center" (click)="openArticle(modelo.slug)" size="12" size-xl="2" size-lg="3" size-md="4" size-sm="4" size-xs="4" *ngFor="let modelo of categorias | orderBy: 'categoria'" >
            <ion-card   class="image-container">
              <ion-img alt="Sanfer Conecta" *ngIf="modelo.imagen[0]" src="{{strapiUrl}}{{modelo.imagen[0].url}}"> </ion-img>
              <p class="ion-text-center text-overlay"><ion-icon name="book"></ion-icon> {{modelo.webinars.length}} cursos</p>
              <!--              <ion-icon   name="arrow-forward"></ion-icon>-->
              <ion-icon class="icon-overlay" name="arrow-forward"></ion-icon>
              <!--              <ion-icon  name="arrow-redo-circle-outline"></ion-icon>-->
              <!--              <ion-icon class="icon-overlay" name="caret-forward-circle"></ion-icon>-->
            </ion-card>
            <h3 class="ion-text-center title-container" *ngIf="modelo.categoria">{{modelo.categoria}}</h3>
            <!--            <p></p>-->
          </ion-col>
        </ion-row>
      </ion-col>

      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
    </ion-row>
  </ion-grid>

</ion-content>
