ion-card{
  margin: 0;
  padding: 0;
}


.slide-academia{
  margin: 10px 10px 20px 10px;
  border-radius: 20px;
}

.youtube-player-container {
  width: 100%; /* O puedes especificar un ancho fijo como 600px */
  max-width: 800px; /* O el máximo que desees */
  aspect-ratio: 16 / 9;
  margin: auto; /* Para centrar el contenedor si es más pequeño que el espacio disponible */
}
h3.title-container {
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 11px; /* Tamaño de la fuente */
  font-weight: bold;
  line-height: 1.5; /* Interlineado */
  height: 30px; /* Altura máxima (2 líneas) */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  color: var(--ion-color-secondary);
}

.small-text-category {
  font-size: 14px;
}
.image-container {
  border-radius: 20px;
  position: relative;
  display: inline-block;
}

.image-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 30%;
  background: linear-gradient(transparent, var(--ion-color-dark));
  opacity: 0.4;
  z-index: 1; /* z-index más bajo para el degradado */
}

.text-overlay {
  position: absolute;
  bottom: -10px;
  left: 15px;
  color: white;
  font-size: 12px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 2; /* z-index más alto para el texto */
}
.icon-overlay{
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 15px;
  color: var(--ion-color-primary);
  //shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  z-index: 2; /* z-index más alto para el texto */
}



//ion-card-header {
//  margin: 0 5px 10px 5px;
//
//  //height: 32px
//}
//
//ion-card-content {
//
//  margin: 10px 0 0 0;
//}

//
//@media (max-width: 600px) {
//  .small-text-category {
//    font-size: 10px;
//  }
//  h3 {
//    margin-top: 5px;
//    font-size: 12px;
//  }
//
//  ion-card-header {
//    margin: 0 5px 10px 5px;
//
//    //height: 22px
//  }
//}
//
//@media (min-width: 600px) and (max-width: 800px)  {
//  .small-text-category {
//    font-size: 12px;
//  }
//  h3 {
//    margin-top: 5px;
//    font-size: 13px;
//  }
//
//  ion-card-header {
//    margin: 0 5px 10px 5px;
//
//    //height: 25px
//  }
//}
