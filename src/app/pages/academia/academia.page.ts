import { Component, OnInit } from '@angular/core';
import {environment} from "../../../environments/environment";
import {StrapiService} from "../../services/strapi.service";
import {Router} from "@angular/router";
import {WidgetUtilService} from "../../services/widget-util.service";
import {FirebaseAuthService} from "../../services/firebase-auth.service";
import {ContentService} from "../../services/content.service";
import {AnalyticsService} from "../../services/analytics.service";
import {BrowserService} from "../../services/browser.service";
import {Preferences} from "@capacitor/preferences";

@Component({
  selector: 'app-academia',
  templateUrl: './academia.page.html',
  styleUrls: ['./academia.page.scss'],
})
export class AcademiaPage implements OnInit {

  userContent: any;
  slideAcademiaCategorias: any
  bannerAcademia: any = []
  userInStorage: any = []
  slideAcademiaCategoriasSwitch: boolean = false;
  strapiUrl = environment.strapiURL;
  categorias: any;

  constructor( private strapi: StrapiService,
               private router: Router,
               private widgetUtilService: WidgetUtilService,
               private firebaseAuthService: FirebaseAuthService,
               private content: ContentService,
               private analyticsService: AnalyticsService,
               private browser: BrowserService,



  ) {
    // this.getAuthState();
  }

  async ngOnInit() {

    await this.widgetUtilService.presentLoading();
    await this.getUserContent();
    await this.getUserInStorage();
    await this.analyticsService.setCurrentScreen('Academia Sanfer')



  }




  async loadWebinars() {
    try {
      // Intentar obtener las categorías desde las preferencias almacenadas
      const storedCategorias = await Preferences.get({ key: 'categorias-de-cursos' });

      if (storedCategorias.value) {
        // Si se encuentra la preferencia, parsearla y asignarla
        this.categorias = JSON.parse(storedCategorias.value);
      } else {
        // Si no se encuentra la preferencia, solicitar la información a través de ContentService
        const categoriasResponse = await this.content.strapiGet('categorias-de-cursos', `_limit=300`);
        this.categorias = categoriasResponse.data;

        // // Almacenar la respuesta obtenida en las preferencias para uso futuro
        // await Preferences.set({
        //   key: 'categorias-de-cursos',
        //   value: JSON.stringify(this.categorias),
        // });
      }

    } catch (error) {
      console.error('Error cargando las categorías de cursos:', error);
    } finally {
      // Asegurarse de que el loader se dismita siempre
      await this.widgetUtilService.dismissLoader();
    }
  }

  openArticle(cursoId: string) {
    this.router.navigate(['/academia', cursoId]);
  }




  async getUserInStorage(){
    const ret = await Preferences.get({ key: 'user' });
    this.userInStorage = JSON.parse(ret.value);

  }
  async cursosRefresh(event: any) {
    // console.log('contenido actualizado en el home', event)
    await this.content.getUser(this.userInStorage);
    await this.content.getAcademy(300);
    await this.getUserContent();
    event.target.complete();
  }

  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    this.userContent = await JSON.parse(userContent.value);
    const banners = this.userContent.banners
    console.log('Cursos loaded: ', banners)

    // Slides de la academia


    const bannersOriginalArray = banners.banners
    const bannersFiltrados = bannersOriginalArray.filter((resp: any) => {
      return resp.location === 'academia';
    })

    this.bannerAcademia = bannersFiltrados
    console.log('Resultado de los banners', this.bannerAcademia)



    await this.loadWebinars();

  }

  async openBrowser(url:string) {
    await this.browser.openBrowser(url)

  }


}
