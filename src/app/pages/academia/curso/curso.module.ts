import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CursoPageRoutingModule } from './curso-routing.module';

import { CursoPage } from './curso.page';
import {YOUTUBE_PLAYER_CONFIG, YouTubePlayer, YouTubePlayerModule} from "@angular/youtube-player";
import {NgDatePipesModule, NgPipesModule} from "ngx-pipes";
import {PipesModule} from "../../../pipes/pipes.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CursoPageRoutingModule,
    YouTubePlayerModule,
    NgDatePipesModule,
    NgPipesModule,
    PipesModule
  ],
  // providers: [
  //   YouTubePlayer
  // ],
  declarations: [CursoPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class CursoPageModule {}
