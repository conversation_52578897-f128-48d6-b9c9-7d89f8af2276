<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{curso.titulo}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button style="font-size: 14px" text="Regresar" defaultHref="/academia/{{slugCategoria}}" ></ion-back-button>
    </ion-buttons>
<!--    <ion-buttons slot="end">-->
<!--      <ion-badge color="secondary">{{numberOfLikes}}</ion-badge>-->
<!--      <ion-icon name="bookmark" size="large" *ngIf="liked" color="secondary" (click)="dislike()"> </ion-icon>-->
<!--      <ion-icon name="bookmark-outline" size="large" *ngIf="!liked" (click)="like()"></ion-icon>-->

<!--    </ion-buttons>-->
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="cursosRefresh($event)">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>
  <ion-grid class="ion-no-padding ion-no-margin">

    <ion-row>
      <ion-col size="12" size-xl="2" size-lg="1" class="ion-no-padding ion-no-margin"></ion-col>
      <ion-col size="12" size-xl="8" size-lg="10" >
        <ion-row>
          <ion-col  size-xl="8" size-lg="8" size-md="12" size-sm="12" size-xs="12" class="ion-padding">

            <!--    box-shadow: none !important;-->
<!--            <div id="reproductor" [ngClass]="{'padding-bottom-thumbnail' : condition}"></div>-->

            <ion-card  *ngIf="showThumbnail" class="img-container">
              <ion-img src="{{imagenPrincipal}}"></ion-img>
            </ion-card>

            <div class="info-container">
              <p>{{slugCategoria}}</p>
              <h3 class="titulo" >{{curso.titulo}}</h3>
              <p class="info-curso">
                <ion-icon name="person"></ion-icon>Autor: <span style="color: var(--ion-color-primary)">{{curso.autor}}</span> <br>
                <ion-icon name="star"></ion-icon> {{calificacion}} <ion-icon name="hourglass"></ion-icon>{{curso.duracion}}<ion-icon name="eye"></ion-icon> {{numberOfViews}}

              </p>

            </div>

            <ion-segment value="lessons" color="primary" >
              <ion-segment-button value="lessons" (click)="selectOption('lessons')">
                <ion-icon style="padding-top: 3px" name="book-outline"></ion-icon>

                <ion-label>Lecciones</ion-label>
              </ion-segment-button>
              <ion-segment-button value="description" (click)="selectOption('description')">
                <ion-icon style="padding-top: 3px" name="newspaper-outline"></ion-icon>
                <ion-label>Descripción</ion-label>
              </ion-segment-button>
              <ion-segment-button value="comments" (click)="selectOption('comments')">
                <ion-icon style="padding-top: 3px" name="chatbox-ellipses-outline"></ion-icon>
                <ion-label>Comentarios</ion-label>
              </ion-segment-button>
            </ion-segment>


<!--            <div class="segmented-control">-->
<!--              <div class="buttons-container">-->
<!--                <div class="button-background" [style.left]="calculateBackgroundPosition(selectedIndex)"></div>-->
<!--                <button *ngFor="let option of options; let i = index"-->
<!--                        class="segment-button"-->
<!--                        (click)="selectOption( option.id)"-->
<!--                        [class.selected]="i === selectedIndex">{{ option.text }}</button>-->
<!--              </div>-->
<!--            </div>-->

<!--            <div></div>-->


            <div *ngIf="isSelected('lessons')" @fadeAnimation>
              <ion-list >

                <ion-item button="true" (click)="abrirLeccion(clase)" *ngFor="let clase of temario">
                  <ion-avatar slot="start">
                    <img src="../../assets/icon/play_btn.svg" >
                  </ion-avatar>
                  <ion-label class="ion-text-wrap">
                    <h2 *ngIf="clase.Clase">{{clase.Clase}}</h2>
                    <div *ngIf="clase.type === 'vimeo'"><ion-icon name="time-outline" color="primary" ></ion-icon>{{clase.duration || curso.duracion}}</div>
                    <div *ngIf="clase.type === 'youtube'"><ion-icon name="time-outline" color="primary" ></ion-icon>{{clase.duration || curso.duracion}}</div>
                    <div *ngIf="clase.type === 'text'"><ion-icon name="book" color="primary"></ion-icon> Lectura</div>
                    <div *ngIf="clase.type === 'pdf'"><ion-icon name="book" color="primary"></ion-icon> PDF</div>
                    <div *ngIf="clase.type === 'gallery'"><ion-icon name="image" color="primary"></ion-icon> Galeria de Imagenes</div>
                  </ion-label>
                </ion-item>


              </ion-list>
            </div>

            <div class="ion-padding" *ngIf="isSelected('description')" @fadeAnimation>

              <div *ngIf="descripcion"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="descripcion"></div>

            </div>
            <div *ngIf="isSelected('comments')" @fadeAnimation>
              <div class="comentarios-container">

                <div class="lista-comentarios">
                  <ion-list>
                    <ion-item>
                      <ion-textarea  labelPlacement="stacked" placeholder="Escribe un comentario..." [(ngModel)]="nuevoComentario">
                        <!--                            <div slot="label">Comments <ion-text color="danger">(Required)</ion-text></div>-->
                      </ion-textarea>
                      <ion-button (click)="publicarComentario()">Enviar</ion-button>

                    </ion-item>
                    <ion-item *ngFor="let comentario of comentarios | orderBy: '-timestamp'" [button]="true" detail="false">

                      <ion-label class="ion-text-wrap">
                        <strong *ngIf="comentario.comentario">{{comentario.comentario}}</strong>
                        <!--                            <ion-text>Never Gonna Give You Up</ion-text><br />-->
                        <ion-note color="medium" class="ion-text-wrap" *ngIf="comentario.user">
                          {{ comentario.user.nombre }} {{ comentario.user.apellido }}
                        </ion-note>
                      </ion-label>
                      <div class="metadata-end-wrapper" slot="end">
                        <ion-note  *ngIf="comentario.timestamp" color="medium">{{comentario.timestamp | haceTiempo}}</ion-note>
                        <ion-icon color="medium" name="chevron-forward"></ion-icon>
                      </div>
                    </ion-item>
                  </ion-list>
                </div>
              </div>
            </div>

          </ion-col>



          <ion-col size-xl="4" size-lg="4" size-md="12" size-sm="12" size-xs="12" class="ion-no-padding ion-no-margin">
            <ion-row >


              <ion-col>
                <h3 style="padding-bottom: 10px" class="ion-padding"><ion-icon name="book" color="primary"></ion-icon> Cursos relacionados:</h3>

                <swiper-container pagination="true" slides-per-view="3.5" space-between="5" slides-offset-before="15" slides-offset-after="30" pager="true" class="swiperStyleSanfer">
                  <swiper-slide *ngFor="let webinar of webinars" (click)="abrirRelacionado(webinar.slug)">
                    <div class="container">
                      <ion-card style="margin-right: 10px" color="primary" class="ion-no-margin card-image">
                        <ion-img *ngIf="webinar.imagen.url" src="{{strapiUrl}}{{webinar.imagen.url}}"></ion-img>
                        <div class="overlay">
                          <h3 class="webinar-title" *ngIf="webinar.titulo">{{webinar.titulo | slice: 0:55}}<span *ngIf="webinar.titulo" [ngClass]="{'dont-display-dots' : webinar.titulo.length <= 55}">...</span></h3>
                          <p class="webinar-duration"><ion-icon name="time"></ion-icon> Duración: {{webinar.duracion}}</p>
                        </div>
                      </ion-card>
                    </div>
                  </swiper-slide>
                </swiper-container>

              </ion-col>
            </ion-row>

          </ion-col>
          <ion-col >


          </ion-col>

        </ion-row>
      </ion-col>
      <ion-col  size="12" size-xl="2" size-lg="1" class="ion-no-padding ion-no-margin"></ion-col>

    </ion-row>

  </ion-grid>


  <ion-modal
    #leasonModal
  >
    <ng-template>

      <ion-fab vertical="bottom" horizontal="center" *ngIf="closeBottomBtn"  @fadeAnimation>
        <ion-fab-button (click)="closeLeasonModal()">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <ion-fab vertical="top" horizontal="center" *ngIf="closeTopBtn" @fadeAnimation>
        <ion-fab-button (click)="closeLeasonModal()">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <!--      Contenido del modal para vimeo-->
      <ion-content color="dark" *ngIf="postType === 'vimeo'">
        <div class="ion-text-center vimeo-modal-style" >
          <div id="reproductor"></div>
        </div>
      </ion-content>

      <!--      Contenido del modal para Youtube-->
      <ion-content color="dark" *ngIf="postType === 'youtube'">
        <div class="ion-text-center vimeo-modal-style" >
          <div class="youtube-player-container">
            <!-- High quality image that should be present for most videos from the past few years. -->
            <youtube-player [width]="windowWidth" [videoId]="youtubeId" placeholderImageQuality="high"/>
          </div>
        </div>
      </ion-content>

      <!--      Contenido del modal para Texto-->
      <ion-content  *ngIf="postType === 'text'">
        <div class="text-modal-style ion-padding" >
          <h1>{{modalContent.Clase}}</h1>
          <div *ngIf="modalContent.description"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="modalContent.description"></div>
        </div>
      </ion-content>

      <!--      Contenido del modal para Galeria de imagenes-->
      <ion-content  *ngIf="postType === 'gallery'">
        <swiper-container pagination="true" zoom="true" slides-per-view="1"  pager="true" class="swiperStyleSanfer">
          <swiper-slide style="padding-bottom: 30px"  *ngFor="let image of modalContent.gallery">
              <ion-card class="ion-no-padding ion-no-margin" >
                <ion-img src="{{strapiUrl}}{{image.url}}"></ion-img>
              </ion-card>
          </swiper-slide>
        </swiper-container>
        <h1 class="text-modal-style ion-padding ion-no-margin" >{{modalContent.Clase}}</h1>

        <div class="text-modal-style ion-padding no_padding_top" >

          <div *ngIf="modalContent.description"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="modalContent.description"></div>
        </div>
      </ion-content>

    </ng-template>
  </ion-modal>

</ion-content>
