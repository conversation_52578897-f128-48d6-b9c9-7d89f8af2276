
.unread-indicator {
  background: var(--ion-color-primary);

  width: 10px;
  height: 10px;

  border-radius: 100%;

  position: absolute;

  inset-inline-start: 12px;
  top: 12px;
}

.metadata-end-wrapper {
  position: absolute;

  top: 10px;
  inset-inline-end: 10px;

  font-size: 0.8rem;

  display: flex;
  align-items: center;
}

ion-label strong {
  display: block;

  max-width: calc(100% - 90px);

  overflow: hidden;

  text-overflow: ellipsis;
}

ion-label ion-note {
  font-size: 0.6rem;
}

ion-slides {
  height: 100%;
}

.segmented-control {
  .buttons-container {
    background: #f4f4f4;
    position: relative;
    display: flex;
    border-radius: 20px;
    padding: 15px 0; /* Ajusta este valor si cambias el padding */
    overflow: hidden;

    .button-background {
      position: absolute;
      background-color: red;
      height: 70%;
      /* Ajusta el ancho para incluir el padding y los márgenes */
      width: calc((100% - 2 * 10px - 2 * 15px) / 3); /* Restamos padding y márgenes en ambos lados y dividimos por 3 */
      z-index: 0;
      transition: left 0.3s ease;
      border-radius: 15px;
      margin-left: 10px; /* Asegúrate de que este valor coincida con los márgenes que deseas */
      margin-right: 10px; /* Asegúrate de que este valor coincida con los márgenes que deseas */
      top: 10px; /* Igual al padding superior */
      transition: left 0.3s ease;
    }

    .segment-button {
      flex: 1;
      z-index: 1;
      background-color: transparent;
      border: none;
      outline: none;
      padding: 10px 20px; /* Ajusta según sea necesario */
      cursor: pointer;
      //transition: background-color 0.3s ease;

      color: black; // Color inicial del texto
      //mix-blend-mode: difference; // Esto hará que el color del texto se mezcle con el fondo
      transition: color 0.3s ease;
      //font-weight: bold;

      &.selected {
        color: white; // Color del texto cuando está seleccionado
        transition: color 0.3s ease;      }
    }
  }
}



.padding-bottom-thumbnail {
  padding-bottom: 10px
}



.rating {
  border: none;
  float: left;
}

.rating > input { display: none; }
.rating > label:before {
  margin: 5px;
  font-size: 1.25em;
  font-family: "Font Awesome 5 Free";
  display: inline-block;
  content: "\f005";
}

.rating > .check:before {
  margin: 5px;
  font-size: 1.25em;
  font-family: "Font Awesome 5 Free";
  display: inline-block;
  content: "\f058";
}

.rating > .half:before {
  content: "\f089";
  position: absolute;
}
.rating > label {
  color: #ddd;
  float: right;
}




.rating > input:checked ~ label,
.rating:not(:checked) > label:hover,
.rating:not(:checked) > label:hover ~ label { color: #FFD700;  }

.rating > input:checked + label:hover,
.rating > input:checked ~ label:hover,
.rating > label:hover ~ input:checked ~ label,  selection
.rating > input:checked ~ label:hover ~ label { color: #FFED85;  }




.img-container{
  margin: 0 0 0 0;
  border-radius: 20px;
}

.info-container{

}

.titulo{
  font-size: 16px;
  font-weight: bold;
  color: var(--ion-color-primary);
}
.info-curso{
  font-size: 12px;
}
.calificacion{

}

.descripcion{
  font-size: 12px;

}


.comentarios-container {
  /* Estilos del contenedor principal */
}

.nuevo-comentario {
  margin-bottom: 20px;
}

.nuevo-comentario textarea {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.nuevo-comentario button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.lista-comentarios {
  margin-top: 20px;
}

.comentario {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.comentario-autor {
  font-weight: bold;
}

.comentario-fecha {
  font-size: 0.8em;
  color: #666;
}

.comentario-contenido {
  margin-top: 5px;
}





.container {
  padding-bottom: 40px;
}

.card-image {
  border-radius: 20px;
  position: relative;
  width: auto;
  aspect-ratio: 9 / 13; /* Formato vertical 9:16 */
}

.card-image ion-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4); /* Fondo oscuro para mejorar la visibilidad del texto */
  color: white;
  padding: 5px;
}

.webinar-title {
  //font-size: 12px;
  //position: absolute;
  //top: 5px;
  //right: 5px;
  //max-height: 3.6em; /* Limitar a tres líneas */
  //overflow: hidden;
  //text-overflow: ellipsis;
  //display: -webkit-box;
  //-webkit-line-clamp: 3; /* Limitar a tres líneas */
  //-webkit-box-orient: vertical;

  font-size: 12px;
  position: absolute;
  font-weight: bold;
  top: 5px;
  left: 5px;
  right: 5px;
  max-height: 4.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.webinar-duration {
  font-size: 8px;
  position: absolute;
  bottom: 5px;
  left: 5px;
}
ion-modal {
  --width: 100%;
  --height: 100%;

  --max-width: 100%;
}




.vimeo-modal-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}


