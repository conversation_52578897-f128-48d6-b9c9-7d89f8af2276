import {Component, HostListener, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {Preferences} from "@capacitor/preferences";
import {environment} from "../../../../environments/environment";
import {IonModal, ModalController} from "@ionic/angular";
import Player from '@vimeo/player';
import {BrowserService} from "../../../services/browser.service";
import { trigger, state, style, transition, animate } from '@angular/animations';
import {marked} from "marked";
import {WidgetUtilService} from "../../../services/widget-util.service";
import {ContentService} from "../../../services/content.service";
// import {YouTubePlayer} from '@angular/youtube-player';
import {YouTubePlayer, YOUTUBE_PLAYER_CONFIG} from '@angular/youtube-player';
import {InteractionService} from "../../../services/interaction.service";
import {getAuth, onAuthStateChanged} from "@angular/fire/auth";
import {AcademyCommentService} from "../../../services/academy-comment.service";
import {AnalyticsService} from "../../../services/analytics.service";
import {StrapiService} from "../../../services/strapi.service";

@Component({
  selector: 'app-curso',
  templateUrl: './curso.page.html',
  styleUrls: ['./curso.page.scss'],
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.3s 0.3s ease-in', style({ opacity: 1 })) // Agregar un retraso de 0.3s
      ]),
      transition(':leave', [
        animate('0.3s ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class CursoPage implements OnInit {
  @ViewChild('youtubeModal') youtubeModal: IonModal;
  @ViewChild('leasonModal') leasonModal: IonModal;
  @ViewChild('textModal') textModal: IonModal;
  @ViewChild('galleryModal') galleryModal: IonModal;



  // datos de los modales para las clases:
  modalcolor: string = 'white'
  modalContent: any = {}
  // textContent: any
  closeBottomBtn: boolean = false;
  closeTopBtn: boolean = false;

  postType: string = 'vimeo'

  // Modal de vimeo
  videoId: any
  vimeoPlayer: Player;


  // Modal de Youtube
  windowWidth: number;
  youtubeId: string;




  // Opciones del menu
  options: any = [];

  strapiUrl = environment.strapiURL;
  user: any;
  uuid: string;
  numberOfLikes: number;
  numberOfViews: number;
  calificacion: number;
  liked: boolean = false
  slug = '';
  slugCategoria = '';

  webinars: any;

  curso: any = {};
  descripcion: any;
  imagenPrincipal: string;
  categoria: any;
  // thumbnailImg: string;
  showThumbnail = true;

  player: any;
  claseReproducida: any;
  condition: boolean = false;

  categorias: any;


  temario: any;
  contentType: string
  // temarioLength: number = 0;
  // multimedia: any;
  // titulo: string = '';
  // autor: string;
  // descripcion: string = '';


  idVimeo: string = '';

  selectedIndex = 0;
  selectedOption: string = 'lessons';


  comentarios: any = []


  nuevoComentario: string = ''; // El texto vinculado al campo de entrada para nuevos comentarios


  constructor(
    private activatedRoute: ActivatedRoute,
    private modalCtrl: ModalController,
    private browser: BrowserService,
    private wus: WidgetUtilService,
    private content: ContentService,
    private interaction: InteractionService,
    private comment: AcademyCommentService,
    private analyticsService: AnalyticsService,

    private strapiService: StrapiService




  ) {
    // Toma el parametro de la url y lo usa pra definir el slug
    this.activatedRoute.params.subscribe((result: any) => {
      console.log('resultado de id', result);
      this.slugCategoria = result.categoria;
      this.slug = result.slug;
      // https://sanferconecta.xyz/webinars?slug=que-son-las-pruebas-funcionales-hepaticas

    });
  }

  async ngOnInit() {
    await this.getUserContent();
    await this.getUserInStorage();
    this.cargarComentarios();
    this.windowWidth = window.innerWidth;
    await this.analyticsService.setCurrentScreen(`${this.slug}`)

  }


  like(){

  }
  dislike() {

  }


  selectOption(optionId: string) {

    this.selectedOption = optionId;
  }


  // Esta función comprueba si la opción dada está seleccionada
  isSelected(optionId: string): boolean {
    return this.selectedOption === optionId;
  }

  calculateBackgroundPosition(index: number): string {
    return `${index * (100 / this.options.length)}%`;
  }

  async videoVimeoEmbeded(id: string, clase: string) {
    if (id.includes('https://')) {
      const idVimeo = id.split('/');
      this.idVimeo = idVimeo[3];
    } else {
      this.idVimeo = id;
    }

    // anida el nombre de la clase en la variable this.claseReproducida

    this.claseReproducida = clase;
    // Desactiva el Thumbnail del curso

    this.showThumbnail = false;
    // Activa la clase que remueve el margen de el thumbnail

    this.condition = true;

    // Si no detecta la variable player
    if (this.player === undefined) {
      console.log('No hay un player', this.player);

      // Crea el reproductor
      this.player = new Player('reproductor', {
        // le pasa el id de vimeo
        id: this.idVimeo,
        // width: 400,
        responsive: true,
        // autoplay: true,
        color: '#f32d36'
      });

      this.player.on('play', async (play: any) => {
        // await this.validateLastVisit(id, 'play');
        console.log('El usuario dio play en el primer video', play);
        // await this.handleVideoInteraction('play', id, clase, play);
      });

      this.player.on('pause', async (pause: any) => {
        // this.validateLastVisit(id, 'pause');
        console.log('El usuario dio pausa al primer video', pause);
        // await this.handleVideoInteraction('pause', id, clase, pause);
      });

    } else {
      this.player.off('play');
      this.player.loadVideo(this.idVimeo).then((idNew: any) => {
        console.log('Nuevo video cargado al reproductor', idNew);
        this.player.off('pause');

        this.player.on('play', async (play: any) => {
          // this.validateLastVisit(id, 'play');
          console.log('El usuario dio play en el video nuevo', play);
          // await this.handleVideoInteraction('play', id, clase, play);
        });

        this.player.on('pause', async (pause: any) => {
          // this.validateLastVisit(id, 'pause');
          console.log('El usuario dio pausa al video nuevo', pause);
          // await this.handleVideoInteraction('pause', id, clase, pause);
        });

      }).catch((error: any) => {
        // Handle the error for video loading
      });
    }
  }

  async openBrowser(url:string) {
    console.log('url click:', url)
    console.log('Webinars full:', this.webinars)

    await this.browser.openBrowser(url)
  }

  async abrirRelacionado(slug: string) {

    const url = '/academia/' +this.slugCategoria + '/' + slug
    console.log('url:', url)

    await this.browser.openBrowser(url)
  }


  esAutor(comentario: any): boolean {
    // Reemplaza 'usuarioActualId' con la forma en que identificas al usuario actual en tu aplicación
    return comentario.id === this.uuid;
    // return true
  }

  publicarComentario() {
    if (this.nuevoComentario.trim()) {
      // Aquí llamarías a tu servicio para agregar el comentario al backend
      // Y luego recargar o actualizar la lista de comentarios
      this.comment.agregarComentario(this.slugCategoria, this.slug, this.uuid, this.user, this.nuevoComentario )
      // this.comentarioService.agregarComentario({ contenido: this.nuevoComentario }).subscribe(
      //   comentario => {
      //     this.comentarios.push(comentario);
          this.nuevoComentario = ''; // Limpia el campo de entrada después de enviar
      //   }
      // );
    }
  }


  cargarComentarios() {
    this.comment.obtenerComentarios(this.slugCategoria, this.slug).subscribe(data => {
      this.comentarios = data.map(e => {
        return {
          id: e.payload.doc.id,
          ...e.payload.doc.data() as any
        };
      });
    });
  }



  async cursosRefresh(event: any) {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);

    await this.content.getUser(userInStorage);
    // await this.content.getAcademy(300);
    await this.getUserContent();
    event.target.complete();
  }

  async getUserInStorage() {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    this.user = userInStorage;
    console.log('Usuario en el storage', this.user)

  }

  async loadWebinars() {
    try {
      // Intentar obtener las categorías desde las preferencias almacenadas
      const storedCategorias = await Preferences.get({ key: 'categorias-de-cursos' });

      if (storedCategorias.value) {
        // Si se encuentra la preferencia, parsearla y asignarla
        this.categorias = JSON.parse(storedCategorias.value);
      } else {
        // Si no se encuentra la preferencia, solicitar la información a través de ContentService
        const categoriasResponse = await this.content.strapiGet('categorias-de-cursos', `_limit=300`);
        this.categorias = categoriasResponse.data;

      }

    } catch (error) {
      console.error('Error cargando las categorías de cursos:', error);
    } finally {
      // Asegurarse de que el loader se dismita siempre
      await this.wus.dismissLoader();
    }
  }

  async getUserContent () {
    try {

      // await this.content.getAcademy(300);


      // await this.loadWebinars();
      // const categoriasCursosResponse = await Preferences.get({ key: 'categorias-de-cursos' });
      // const categorias = JSON.parse(categoriasCursosResponse.value);

      // console.log('Contenido de las categorias: ', this.categorias)
      const categoriasResponse = await this.content.strapiGet('categorias-de-cursos', `_limit=300`);
      this.categorias = categoriasResponse.data;
      const resultadoPrimerFiltro = this.categorias.find((resp) => resp.slug === this.slugCategoria);
      console.log('Resultado primer filtro: ', resultadoPrimerFiltro)

      if (!resultadoPrimerFiltro) {
        console.error('No se encontró la categoría.');
        return;
      }

      this.webinars = resultadoPrimerFiltro.webinars;
      const cursoEncontrado = this.webinars.find((resp) => resp.slug === this.slug);
      console.log('Curso encontrado: ', cursoEncontrado)

      if (!cursoEncontrado) {
        console.error('No se encontró el webinar.');
        return;
      }



      this.strapiService.getCurso(this.slug).subscribe(resp => {
        console.log('Slug', this.slug, 'Prueba Strapi contant:', resp[0])
        this.curso = resp[0]

        // this.curso = cursoEncontrado;
        // console.log('Prueba Curso Encontrado:', this.curso)
        //
        console.log('Curso completo: ', this.curso)
        if (this.curso.descripcion) {
          this.descripcion = marked(this.curso.descripcion)

        } else {
          this.descripcion = 'Sin descripcion'

        }

        this.imagenPrincipal = this.strapiUrl + this.curso.imagen.url;
        // this.titulo = this.curso.titulo;
        // this.autor = this.curso.autor;
        //
        // console.log('Titulo', this.titulo )
        //
        // this.descripcion = this.curso.descripcion;
        this.categoria = this.curso.categorias_de_curso.categoria;
        // this.thumbnailImg = this.curso.imagen.url;
        this.temario = this.curso.temario;


        console.log('Temario', this.temario)
        // this.temarioLength = this.temario.length
        // this.options = [
        //   { text: `Lecciones (${this.temario.length})`, id: 'lessons' },
        //   { text: 'Descripción', id: 'description' },
        //   { text: 'Comentarios', id: 'comments' }
        // ];
        // console.log('Opciones', this.options)

        const auth = getAuth();
        onAuthStateChanged(auth, async (user) => {
          console.log(user.uid);
          this.uuid = user.uid
          this.numberOfViews = await this.interaction.academyViews(this.slugCategoria, this.slug,  user.uid);
          const data = {
            titulo: this.curso.titulo,
            autor: this.curso.autor,
            duracion: this.curso.duracion,
            imagen: this.curso.imagen.url,
            slug: this.slug,
            categoria: this.slugCategoria,
            type: 'curso'
          }
          await this.interaction.registrarInteraccionAcademia(user.uid, this.slugCategoria, this.slug, data);

        });
      });





    } catch (error) {
      console.error('Error al cargar los datos:', error);
    }

  }




  async abrirLeccion(leccion: any) {


    console.log('Leccion', leccion);
    await this.wus.presentLoadingMessage('Cargando...')
    this.modalContent = leccion;
    console.log('Temario', this.temario)

    console.log('Contenido del modal', this.modalContent)

    this.modalcolor = leccion.modalColor;

    // this.textContent = undefined
    // if (this.modalContent.textContent !== null && this.modalContent.textContent !== undefined) {
    //   this.textContent = marked(this.modalContent.textContent)
    // }

    this.postType = leccion.type
    if(leccion.type === undefined || leccion.type === null) {
      this.postType = 'vimeo'
    }

    const detallesLeccion = this.modalContent;


    // await this.interaction.manejarAccesoCursoYLeccion(this.uuid, this.slugCategoria, this.slug, detallesLeccion, this.curso);

    console.log(this.postType)

    switch (this.postType) {
      case 'vimeo':
        // console.log('Abrir video')

        await this.leasonModal.present();
        this.closeBottomBtn = true;
        await this.vimeoVideo(this.modalContent.urlVimeo, 'reproductor');
        await this.wus.dismissLoader();

        // this.equipoAsignado = 'onco';
        break;
      case 'youtube':
        await this.leasonModal.present();
        this.closeTopBtn = true;
        this.youtubeId = this.modalContent.urlYouTube;
        await this.wus.dismissLoader();
        break;
      case 'gallery':
        // console.log('Abrir photo')
        await this.leasonModal.present();
        this.closeBottomBtn = true;
        await this.wus.dismissLoader();

        break;
      case 'pdf':
        // console.log('Abrir pdf')
        await this.openBrowser(this.modalContent.pdf);
        await this.wus.dismissLoader();

        break;
      case 'text':
        console.log('Abrir text')
        this.closeBottomBtn = true;
        await this.leasonModal.present();
        await this.wus.dismissLoader();

        // this.equipoAsignado = 'hermes';
        break;
      case 'quiz':
        console.log('Abrir model')

        // await this.openBrowser(this.postContent.modelURL)


        // this.equipoAsignado = 'hermes';
        break;
    }

  }


  async vimeoVideo(videoURL: string, playerId: string) {
    console.log(videoURL, playerId);

    // Extrae el ID del video de Vimeo
    const vimeo = videoURL;
    if (vimeo.includes('https://')) {
      const idVimeo = vimeo.split('/');
      this.videoId = idVimeo[3];
    } else {
      this.videoId = vimeo;
    }

    // console.log(this.videoId);

    // Elimina el reproductor existente
    if (this.vimeoPlayer !== undefined) {
      this.vimeoPlayer.destroy();
    }

    // Crea un nuevo reproductor
    this.vimeoPlayer = new Player(playerId, {
      id: this.videoId,
      responsive: true,
      color: '#f32d36',
      // autoplay: true // Habilita autoplay aquí

    });

    // Eventos del reproductor
    await this.vimeoPlayer.on('play', async (play: any) => {
      console.log('El usuario dio play en el video', play);
    });

    await this.vimeoPlayer.on('pause', async (pause: any) => {
      console.log('El usuario dio pausa al video', pause);
    });

    await this.vimeoPlayer.on('ended', async (end: any) => {
      console.log('El video ha terminado', end);
    });

    // Manejo de errores
    await this.vimeoPlayer.on('error', (error: any) => {
      console.error('Error en el reproductor de Vimeo', error);
    });
  }

  // Cierra el modal del contenido
  async closeLeasonModal() {
    this.closeBottomBtn = false
    this.closeTopBtn = false

    if (this.postType === 'vimeo') {
      if (this.vimeoPlayer) {
        try {
          await this.vimeoPlayer.pause();
        } catch (error) {
          console.error("Error al pausar el video", error);
        }
      }
    }

    await this.leasonModal.dismiss();
  }

  // async publicarComentario() {
  //
  // }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.windowWidth = event.target.innerWidth;
  }
}
