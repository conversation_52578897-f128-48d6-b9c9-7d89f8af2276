import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CursosPageRoutingModule } from './cursos-routing.module';

import { CursosPage } from './cursos.page';
import { NgPipesModule} from "ngx-pipes";
import {PipesModule} from "../../../pipes/pipes.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CursosPageRoutingModule,
    PipesModule,
    NgPipesModule,
  ],
  declarations: [CursosPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class CursosPageModule {}
