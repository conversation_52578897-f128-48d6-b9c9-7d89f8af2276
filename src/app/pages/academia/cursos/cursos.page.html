<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{categoria}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/academia"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="openSearchModal()">
        <ion-icon name="search" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

</ion-header>
<ion-content>
  <!--  <ion-searchbar animated placeholder="Buscar curso" (ionChange)="buscar( $event )"></ion-searchbar>-->

  <!--  <ion-row>-->
  <!--    <ion-col>-->
  <!--      <ion-chip (click)="orderBy()">-->
  <!--        <ion-icon name="menu-outline" color="primary"></ion-icon>-->
  <!--        <ion-label>Ordenado por: {{ordenadoPor}}</ion-label>-->
  <!--      </ion-chip>-->
  <!--    </ion-col>-->
  <!--    <ion-col class="ion-text-right">-->
  <!--      <ion-chip>-->
  <!--        <ion-toggle [(ngModel)]="upDown" (click)="upDownOperator()" (ionChange)="upDownOperator()"></ion-toggle>-->

  <!--        <ion-label *ngIf="upDown"><ion-icon name="caret-down-outline"></ion-icon> Descendente </ion-label>-->
  <!--        <ion-label *ngIf="!upDown"><ion-icon name="caret-up-outline"></ion-icon> Ascendente </ion-label>-->
  <!--      </ion-chip>-->
  <!--    </ion-col>-->
  <!--  </ion-row>-->

  <swiper-container   pagination="true" class="recomended-courses swiperStyleSanfer" slides-per-view="3.5" space-between="10" slides-offset-before="15" slides-offset-after="15">
    <swiper-slide  *ngFor="let recomendado of recomendados" >
      <ion-card class="banner-container">
        <ion-img  (click)="openBrowser(recomendado.url)" src="{{strapiUrl}}{{recomendado.portada.url}}"></ion-img>
      </ion-card>
      <h3></h3>
    </swiper-slide>
  </swiper-container>
  <ion-button *ngIf="searchInProgress" (click)="limpiarBusqueda();"><ion-icon name="close"></ion-icon> Borrar resultados</ion-button>

  <ion-grid >
    <ion-row class="ion-no-padding">
      <ion-col size="12" size-lg="1" size-xl="2" class="invisible-col"></ion-col>

      <ion-col size="12" size-lg="10" size-xl="8">





        <ion-row>
          <ion-col (click)="openCourse(curso.slug)" size="12" size-xl="3" size-lg="3" size-md="4" size-sm="6" size-xs="6" *ngFor="let curso of cursos | filterBy: ['titulo', 'descripcion', 'autor']: textoBuscar: false | orderBy: orderBy ">

            <ion-card button="true" class="image-container">
              <ion-img src="{{strapiUrl}}{{curso.imagen.url}}"> </ion-img>

              <p  class="ion-text-center text-overlay"><ion-icon name="time-outline"></ion-icon> {{curso.duracion}} </p>
              <p class="ion-text-center icon-overlay" ><ion-icon  name="play-circle"></ion-icon>{{curso.temario.length}}</p>
              <!--              <ion-icon class="icon-overlay" name="caret-forward-circle"></ion-icon>-->


<!--              <p class="icon-overlay"><ion-icon  name="play-circle-outline"></ion-icon>{{curso.temario.length}}</p>-->
            </ion-card>
            <p class="autor-curso ion-text-center">
              <ion-icon color="primary" name="person" ></ion-icon> {{curso.autor | slice: 0:30}}<span *ngIf="curso.autor" [ngClass]="{'dont-display-dots' : curso.autor.length <= 30}">...</span>
            </p>

            <h3 class="title-container">{{curso.titulo | slice: 0:55}}<span [ngClass]="{'dont-display-dots' : curso.titulo.length <= 55}">...</span></h3>
<!--            <p class="ion-text-center likes-views icon-views-overlay">-->
<!--              <ion-chip>-->
<!--                <ion-icon color="primary" name="heart"></ion-icon>-->
<!--                <ion-label>{{curso.likes || 0}}</ion-label>-->
<!--              </ion-chip>-->
<!--              <br>-->
<!--              <ion-chip>-->
<!--                <ion-icon color="primary" name="eye"></ion-icon>-->
<!--                <ion-label>{{curso.views || 0}}</ion-label>-->
<!--              </ion-chip>-->


<!--            </p>-->
          </ion-col>
        </ion-row>
      </ion-col>
      <ion-col size="12" size-lg="1" size-xl="2" class="invisible-col"></ion-col>

    </ion-row>

  </ion-grid>



  <ion-modal
    #searchModal
    [isOpen]="false"
    [initialBreakpoint]="0.25"
    [breakpoints]="[0, 0.25]"

  >
    <ng-template>
      <ion-content class="ion-padding" color="primary">
        <!--        <ion-searchbar placeholder="Search" (click)="searchModal.setCurrentBreakpoint(0.5)"></ion-searchbar>-->
        <ion-searchbar #searchBar color="light" placeholder="Buscar curso" (click)="searchModal.setCurrentBreakpoint(0.25)" (ionInput)="buscar( $event )"></ion-searchbar>

        <div class="ion-text-center">
          <ion-chip class="chipWhite">
            <ion-label>Ordenar por:</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('-updated_at')" [ngStyle]="{'color':orderBy === '-updated_at' ? 'white' : '', 'background-color':orderBy === '-updated_at' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === '-updated_at' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas recientes</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('updated_at')" [ngStyle]="{'color':orderBy === 'updated_at' ? 'white' : '', 'background-color':orderBy === 'updated_at' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'updated_at' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas antiguas</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('titulo')" [ngStyle]="{'color':orderBy === 'titulo' ? 'white' : '', 'background-color':orderBy === 'titulo' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'titulo' ? 'red' : ''}"></ion-icon>
            <ion-label>Alfabeticamente</ion-label>
          </ion-chip>
          <ion-chip  (click)="selecionado('duracion')" [ngStyle]="{'color':orderBy === 'duracion' ? 'white' : '', 'background-color':orderBy === 'duracion' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'duracion' ? 'red' : ''}"></ion-icon>
            <ion-label>Duracion</ion-label>
          </ion-chip>
        </div>

      </ion-content>
    </ng-template>
  </ion-modal>



</ion-content>
