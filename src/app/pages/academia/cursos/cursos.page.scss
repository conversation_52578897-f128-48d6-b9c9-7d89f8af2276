
ion-card{
  margin: 0;
  padding: 0;
}



.recomended-courses {
  padding-top: 10px;
}
.banner-container {
  width: 100%; /* O un tamaño fijo, por ejemplo, 200px */
  height: 0;
  margin-bottom: 25px;
  padding-bottom: 140%; /* Esto crea un cuadrado */
  position: relative;
}

.banner-container ion-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Esto asegura que la imagen cubra el contenedor, recortándose si es necesario */
}


.slide-academia{
  margin: 10px 10px 20px 10px;
  border-radius: 20px;
}

h3.title-container {
  padding: 0 5px 0 5px;
  margin-top: 0;
  margin-bottom: 0;
  text-align: center;
  font-size: 11px; /* Tamaño de la fuente */
  font-weight: bold;
  line-height: 1.5; /* Interlineado */
  height: 30px; /* Altura máxima (2 líneas) */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  color: #2e2f2f;
}

.autor-curso {
  margin:  5px 0 ;
  font-size: 10px;
}

.small-text-category {
  font-size: 14px;
}
//.image-container {
//  position: relative;
//  display: inline-block;
//}

.image-container {
  border-radius: 20px;

  width: 100%; /* O un tamaño fijo, por ejemplo, 200px */
  height: 0;
  padding-bottom: 70%; /* Esto crea un cuadrado */
  position: relative;
}

.image-container ion-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Esto asegura que la imagen cubra el contenedor, recortándose si es necesario */
}


.image-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 30%;
  background: linear-gradient(transparent, var(--ion-color-dark));
  opacity: 0.8;
  z-index: 1; /* z-index más bajo para el degradado */
}

.text-overlay {
  position: absolute;
  bottom: -10px;
  left: 15px;
  color: white;
  font-size: 14px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 2; /* z-index más alto para el texto */
}
.text-overlay span {
  right: 15px;

}

.icon-overlay{
  position: absolute;
  bottom: -10px;
  right: 15px;
  font-size: 14px;
  color: white;
  //shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  z-index: 2; /* z-index más alto para el texto */
}
.icon-views-overlay{
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 20px;
  color: var(--ion-color-primary);
  //shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  z-index: 2; /* z-index más alto para el texto */
}

.likes-views {
  margin: 5px 0 0 0;
}
.likes-views ion-chip {
  background: white;
  font-size: 12px;
  margin: 0 4px;
  min-height: 20px;
  padding-top: 3px;
  padding-bottom: 3px;
  padding-inline:10px




}

.likes-views ion-chip ion-icon {
  margin-right: 2px;
}


//ion-card{
//  margin: 0;
//  padding: 0;
//}
//
//ion-slides .cursos {
//  height: 100%;
//}
//h1 {
//  margin-top: 5px;
//  font-size: 18px;
//}
//
//ion-card-header h3 {
//  margin-top: 5px;
//  font-weight: bold;
//  font-size: 15px;
//}
//
//ion-card-header {
//  //height: 45px;
//  padding-left: 10px;
//  padding-right: 10px;
//}
//
//.autor-curso {
//  font-size: 12px;
//}
//ion-card.sp{
//  margin: 5px;
//  padding: 0;
//}
//
//.recomendamos{
//
//}
//.recomendamos h1 {
//   font-weight: bold;
// }
//@media (max-width: 600px) {
//  ion-card-header h3 {
//    font-weight: bold;
//    font-size: 12px;
//  }
//  ion-card-header {
//    //height: 40px;
//    padding-left: 15px;
//    padding-right: 15px;
//  }
//
//  .autor-curso {
//    font-size: 10px;
//  }
//
//  .recomendamos{
//
//    padding-top: 0px;
//    padding-bottom: 0px;
//
//  }
//  .recomendamos h1 {
//    font-size: 12px;
//  }
//
//}
//
//@media (min-width: 600px) and (max-width: 990px)  {
//  ion-card-header h3 {
//    font-weight: bold;
//    font-size: 14px;
//  }
//  ion-card-header {
//    height: 40px;
//    padding-left: 15px;
//    padding-right: 15px;
//  }
//  //.description-class {
//  //  display: none;
//  //}
//
//}
//
//
//@media (min-width: 991px) and (max-width: 1600px)  {
//  ion-card-header h3 {
//    font-weight: bold;
//    font-size: 12px;
//  }
//  .autor-curso {
//    font-size: 10px;
//  }
//  ion-card-header {
//    //height: 40px;
//    padding-left: 15px;
//    padding-right: 15px;
//  }
//  //.description-class {
//  //  display: none;
//  //}
//
//}
//
//
//ion-toggle {
//  height: 20px;
//}
//
