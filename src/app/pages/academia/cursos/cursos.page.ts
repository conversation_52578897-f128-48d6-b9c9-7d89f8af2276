import {Component, OnInit, ViewChild} from '@angular/core';
import {ActionSheetController, IonInput, IonModal} from "@ionic/angular";
import {environment} from "../../../../environments/environment";
import {StrapiService} from "../../../services/strapi.service";
import {ActivatedRoute, Router} from "@angular/router";
import {AnalyticsService} from "../../../services/analytics.service";
import {BrowserService} from "../../../services/browser.service";
import {Preferences} from "@capacitor/preferences";
import {FirebaseAnalytics} from "@capacitor-firebase/analytics";
import {WidgetUtilService} from "../../../services/widget-util.service";
import {ContentService} from "../../../services/content.service";

@Component({
  selector: 'app-cursos',
  templateUrl: './cursos.page.html',
  styleUrls: ['./cursos.page.scss'],
})
export class CursosPage implements OnInit {
  @ViewChild('searchModal') searchModal: IonModal;
  @ViewChild('searchBar') searchBar!: IonInput;

  orderBy: string = 'titulo';
  searchInProgress: boolean = false;
  textoBuscar = '';
  searchBy = 'titulo';


  slugCategoria = '';
  categorias: any;
  categoria: any;
  cursos: any;
  recomendados:any;


  strapiUrl = environment.strapiURL;
  constructor(private strapi: StrapiService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              public actionSheetController: ActionSheetController,
              private analyticsService: AnalyticsService,
              private browser: BrowserService,
              private widgetUtilService: WidgetUtilService,
              private content: ContentService,

  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.slugCategoria = result.categoria;
    });

  }




  async ngOnInit() {
    //
    // const categorias: any = await Preferences.get({ key: 'categorias-de-cursos' });
    // this.categorias = JSON.parse(categorias.value);

    await this.loadWebinars();
    // console.log('Categorias storage: ', this.categorias)

    const filtro = this.categorias.filter((resp: any) => {
      return resp.slug === this.slugCategoria;
    });

    const categoria = filtro[0];


    this.categoria = categoria.categoria;
    this.cursos = categoria.webinars
    this.recomendados = categoria.recomendados
    console.log('Categoria filtrada: ', categoria)


    // Anaytics
    await this.analyticsService.setCurrentScreen(`academia/${this.categoria}`)

  }



  async loadWebinars() {
    try {
      // Intentar obtener las categorías desde las preferencias almacenadas
      const storedCategorias = await Preferences.get({ key: 'categorias-de-cursos' });

      if (storedCategorias.value) {
        // Si se encuentra la preferencia, parsearla y asignarla
        this.categorias = JSON.parse(storedCategorias.value);
      } else {
        // Si no se encuentra la preferencia, solicitar la información a través de ContentService
        const categoriasResponse = await this.content.strapiGet('categorias-de-cursos', `_limit=300`);
        this.categorias = categoriasResponse.data;

      }

    } catch (error) {
      console.error('Error cargando las categorías de cursos:', error);
    } finally {
      // Asegurarse de que el loader se dismita siempre
      await this.widgetUtilService.dismissLoader();
    }
  }


  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }
  openCourse(slug: string) {
    // console.log('Slug', slug)
    // this.strapi.setData(id, contenido);
    this.router.navigateByUrl(`/academia/${this.slugCategoria}/${slug}`);
  }
  buscar( event: any ) {
    if (event.detail.value == '' || null) {
      // console.log('busqueda no realizada')
      this.searchInProgress = false
    } else {
      this.searchInProgress = true
      // console.log(event, 'Busqueda iniciada searchInProgress', this.searchInProgress);
      this.textoBuscar = event.detail.value;
    }

  }
  async openSearchModal() {
    await this.searchModal.present();
    await this.searchBar.setFocus();

  }
  limpiarBusqueda () {
    this.searchInProgress = false
    this.textoBuscar = ''

  }
  selecionado(event: string) {
    this.orderBy = event;

  }
}
