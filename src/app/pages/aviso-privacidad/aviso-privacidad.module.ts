import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { AvisoPrivacidadPage } from './aviso-privacidad.page';

const routes: Routes = [
  {
    path: '',
    component: AvisoPrivacidadPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [AvisoPrivacidadPage]
})
export class AvisoPrivacidadPageModule {}
