import { Component, OnInit } from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {marked} from "marked";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-aviso-privacidad',
  templateUrl: './aviso-privacidad.page.html',
  styleUrls: ['./aviso-privacidad.page.scss'],
})
export class AvisoPrivacidadPage implements OnInit {

  aviso: any;
  avisoP: any;
  titulo: any;
  correo: any;
  fecha: any;
  imagen: any;
  actualizado: any;
  constructor(
    private strapi: StrapiService,
    private analyticsService: AnalyticsService

  ) { }

  async ngOnInit() {

    this.strapi.getContenido('aviso')
        .subscribe((resp: any) => {
          // console.log('Aviso: ', this.avisoP);
          this.aviso = resp.Aviso;
          this.avisoP = marked(this.aviso);


          this.titulo = resp.Titulo;
          this.correo = resp.Email;
          this.fecha = resp.Fecha;
          this.imagen = resp.Imagen.url;
          this.actualizado = resp.updated_at;


        });

    await this.analyticsService.setCurrentScreen('Aviso-privacidad' );

  }

}
