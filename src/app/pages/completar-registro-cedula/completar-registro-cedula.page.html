<ion-header (click)="closeModal()" >
  <ion-toolbar color="primary">
    <ion-title slot=""><ion-icon name="ribbon" size="small"></ion-icon> Selecciona tu cédula profesional</ion-title>
  </ion-toolbar>

</ion-header>

<ion-content>

  <ion-list>
    <ion-card class="cedula" (click)="obtenerDatos(cedula)" *ngFor="let cedula of data">
      <ion-card-content>
        <h1 style="font-weight: bold; color: #044e5e"><ion-icon name="person"></ion-icon> {{cedula.nombre}}  {{cedula.paterno}} {{cedula.materno}}</h1>
        <ion-card-subtitle color="primary"><ion-icon name="ribbon" size="small"></ion-icon> Cédula profesional: {{cedula.idCedula}}</ion-card-subtitle>
        <ion-card-subtitle><ion-icon name="school" size="small"></ion-icon> Titulo: {{cedula.titulo}}</ion-card-subtitle>
        <ion-card-subtitle class="small_text">Institución: {{cedula.desins}}
          <br>Año de registro: {{cedula.anioreg}}</ion-card-subtitle>
      </ion-card-content>

    </ion-card>
  </ion-list>




</ion-content>
