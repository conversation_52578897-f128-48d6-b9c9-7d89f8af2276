import {Component, Input, OnInit} from '@angular/core';
import {LoadingController, ModalController, Platform} from '@ionic/angular';
import {StrapiService} from '../../services/strapi.service';
import {CedulaSepService} from '../../services/cedula-sep.service';
import {WidgetUtilService} from '../../services/widget-util.service';


// import '@capacitor-community/http';
// import { Plugins } from '@capacitor/core';
import {CompletarRegistroConfirmacionPage} from '../completar-registro-confirmacion/completar-registro-confirmacion.page';
import {CapacitorHttp, HttpResponse} from "@capacitor/core";
// const { Http } = Plugins;

// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
// import { Storage } from '@capacitor/storage';
// import { Browser } from '@capacitor/browser';
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';



@Component({
  selector: 'app-completar-registro-cedula',
  templateUrl: './completar-registro-cedula.page.html',
  styleUrls: ['./completar-registro-cedula.page.scss'],
})
export class CompletarRegistroCedulaPage implements OnInit {


  @Input() doctorName: string = '';
  @Input() doctorLastName: string = '';
  @Input() doctorCedula: string = '';

  data: any = [];


  constructor(private strapi: StrapiService,
              private plt: Platform,
              private loadingCtrl: LoadingController,
              private cedulaService: CedulaSepService,
              private modalCtrl: ModalController,
              private widgetUtilService: WidgetUtilService
  ) { }



  async ngOnInit() {

    const loading = await this.loadingCtrl.create();
    await loading.present();
    const apellidoDividido =  this.doctorLastName.split(' ');

    console.log('Apeelido Dividido: ', apellidoDividido);

    const rutaSep = `https://api.allorigins.win/raw?&url=https://www.cedulaprofesional.sep.gob.mx/cedula/buscaCedulaJson.action?json=%7B%22maxResult%22%3A%2250%22%2C%22nombre%22%3A%22${this.doctorName
        .replace(/\s/g, '+')}%22%2C%22paterno%22%3A%22${apellidoDividido[0]}%22%2C%22materno%22%3A%22${apellidoDividido[1]}%22%2C%22idCedula%22%3A%22${this.doctorCedula}%22%7D`;

    console.log('URL de la SEP', rutaSep);
    // Intenta contactar con la SEP
    try {
      // Request de la cedula en la SEP
      // // const ret = await Http.request({
      // //   method: 'POST',
      // //   url: 'https://sanferconecta.live/sep/new',
      // //   headers: {
      // //     'Content-Type': 'application/json'
      // //   },
      // //   data: {
      // //     url: rutaSep
      // //   }
      // // });
      //
      // console.log('Retorno RAW: ', ret);

      const options = {
        url: 'https://sanferconecta.live/sep/new',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          url: rutaSep
        }

      };

      const response: HttpResponse = await CapacitorHttp.post(options);
      this.data = response.data.items;



      await loading.dismiss();
      // const parsed = JSON.parse(ret.data.contents);
      // const jsonData = encodeURI(ret.data.items);
      // const jsonParse = JSON.stringify(jsonData);
      /*this.data = ret.data.items;
      console.log('Retorno en forma de arreglo: ', this.data);

      console.log('Headers ', ret.headers);*/

      await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa! <br><h3>Selecciona tu cedula</h3><br>I');
    } catch (e) {

      // Si hay un error intenta conectar nuevamente ocn la sep

      console.log('Error', e);
      // this.widgetUtilService.presentToastError('Eror de conexion con la SEP, intentando verificar nuevamente');
      /* try {
        const second = await Http.request({
          method: 'POST',
          url: 'http://167.99.226.42/sep/xml',
          data: {url: rutaSep},
          headers: {
            'Content-Type': 'application/json'
          },
        });

        console.log('Retorno de la SEP: ', second.status);
        await loading.dismiss();
        const parsed = second.data;
        this.data = parsed.response.docs;
        this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa!,<br><h3>Selecciona tu cedula</h3><br>II');
      } catch (e) {

        // Si regresa otro error termina el proceso y di que lo intente nuevamente

        this.widgetUtilService.presentToastError('No ha sido posible conectar con la SEP, <br> intenta nuevamente dando click en verificar cedula');
        await loading.dismiss();
        this.closeModal();

      }*/
    }

    // console.log('This Data PARSE', this.data);


    /*// Returns a promise, need to convert with of() to Observable (if want)!
    from (this.http.get(`http://search.sep.gob.mx/solr/cedulasCore/select?&q=${this.doctorName
        .replace(/\s/g, '+')}+${this.doctorLastName
        .replace(/\s/g, '+')}+${this.doctorCedula}&rows=10&wt=json`, {}, {'Content-Type': 'application/json'}))
        .pipe(
            finalize(() => loading.dismiss())
        ).subscribe(resp => {
        const parsed = JSON.parse(resp.data);
        // console.log(typeof parsed);
        // console.log('Data con PARSE', parsed.responseHeader.params.q);
        this.data = parsed.response.docs;
        // console.log('This Data PARSE', this.data);
    }, err => {
        console.log('Native Call error: ', err);
    });*/
  }


  async obtenerDatos(cedula: any) {

    const modal  = await this.modalCtrl.create({
      component: CompletarRegistroConfirmacionPage,
      cssClass: 'qr',
      componentProps: {
        Nombre: cedula.nombre,
        Paterno: cedula.paterno,
        Materno: cedula.materno,
        Cedula: cedula.idCedula,
        Titulo: cedula.titulo,
        Institucion: cedula.desins,
        AnioRegistro: cedula.anioRegistro,
        Tipo: cedula.tipo,
        Genero: cedula.genero
      }
    });




    modal.onWillDismiss().then(() => {
      // this.fab.nativeElement.classList.remove('animated', 'bounceOutLeft');
    });

    await modal.present();
    /*
    await this.modalCtrl.dismiss({
      Nombre: cedula.nombre,
      Paterno: cedula.paterno,
      Materno: cedula.materno,
      Cedula: cedula.idCedula,
      Titulo: cedula.titulo,
      Institucion: cedula.institucion,
      AnioRegistro: cedula.anioRegistro,
      Tipo: cedula.tipo,
      Genero: cedula.genero
    });
    this.modalCtrl.dismiss();
    */
  }



  closeModal() {

    this.modalCtrl.dismiss();
  }

}
