import {Component, Input, OnInit} from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {LoadingController, ModalController, Platform} from '@ionic/angular';
import {CedulaSepService} from '../../services/cedula-sep.service';
import {WidgetUtilService} from '../../services/widget-util.service';

@Component({
  selector: 'app-completar-registro-confirmacion',
  templateUrl: './completar-registro-confirmacion.page.html',
  styleUrls: ['./completar-registro-confirmacion.page.scss'],
})
export class CompletarRegistroConfirmacionPage implements OnInit {
  @Input() Nombre: string = '';
  @Input() Paterno: string = '';
  @Input() Materno: string = '';
  @Input() Cedula: string = '';
  @Input() Titulo: string = '';
  @Input() Institucion: string = '';
  // @Input() AnioRegistro: number;
  @Input() Tipo: string = '';
  // @Input() Genero: number;
  @Input() AnioRegistro!: number;
  @Input() Genero!: number;


  constructor(
      private strapi: StrapiService,
      private plt: Platform,
      private loadingCtrl: LoadingController,
      private cedulaService: CedulaSepService,
      private modalCtrl: ModalController,
      private widgetUtilService: WidgetUtilService
  ) { }

  ngOnInit() {

    console.log(
        'Nombre ', this.Nombre,
        'Apellido: ', this.Paterno,
        'Apellido Materno: ', this.Materno,
        'Cedula: ', this.Cedula,
        'Titulo: ', this.Titulo,
        'Institución: ', this.Institucion,
        'Año de registro: ', this.AnioRegistro,
        'Tipo de cedula: ', this.Tipo,
        'Genero: ', this.Genero,

    );

  }

}
