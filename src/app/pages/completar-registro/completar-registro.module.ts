import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CompletarRegistroPageRoutingModule } from './completar-registro-routing.module';

import { CompletarRegistroPage } from './completar-registro.page';
// import {GenerarCodigoPage} from '../generar-codigo/generar-codigo.page';
// import {GenerarCodigoPageModule} from '../generar-codigo/generar-codigo.module';
import {CompletarRegistroCedulaPageModule} from '../completar-registro-cedula/completar-registro-cedula.module';
// import {SwiperModule} from 'swiper/angular';

@NgModule({
  imports: [

    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
      CompletarRegistroCedulaPageModule,
    // SwiperModule

  ],
  declarations: [CompletarRegistroPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CompletarRegistroPageModule {}
