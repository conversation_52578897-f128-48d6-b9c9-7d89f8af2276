<!--<ion-header>-->
<!--  <ion-toolbar>-->
<!--    <ion-title>completar-registro</ion-title>-->
<!--  </ion-toolbar>-->
<!--</ion-header>-->
<ion-content>


<!--  <ion-button (click)="cedulaV()">Cedula</ion-button>-->
  <ion-grid>
    <ion-row class="ion-justify-content-center centrar_imagen">

      <ion-col size="12"  size-md="8" size-lg="8" size-sm="12" size-xs="12">
        <ion-img class="centrar_imagen"  style="width: 30%; max-width: 250px; padding-top: 20px; padding-bottom: 20px;"  src="/assets/sanfer_conecta_oficial.svg"></ion-img>

        <swiper-container>
          <swiper-slide>
            <ion-card>
              <ion-card-content>
                <h2 *ngIf="this.fuenteLog === 'Registro SanferConecta Respaldo'" style="color: red"> Disculpa las molestias, pero requerimos verificar tu cédula profesional</h2>
                <h1 *ngIf="tituloMedico === false" style="color: red"> Su titulo profesional no es Médico, corrige tu cédula profesional</h1>
                <p *ngIf="verificado === false">Al ser una aplicación exclusiva para médicos certificados, COFEPRIS nos exige validar tu cédula prfesional para poder hacer uso de la plataforma, a continuación ingresa tu cédula</p>
                <h2  *ngIf="verificado === true" style="color: red">Haz completado la verificación de tu cédula profesional ante la SEP</h2>
                <p *ngIf="verificado === true" class="small_text"> Verifica que tus datos sean correctos y da click en completar registro</p>

                <ion-list *ngIf="verificado === true" >
                  <ion-item>
                    <ion-label>
                      <p>Nombre: </p>
                      <h3>{{Nombre}}</h3>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Apellido Paterno: </p>
                      <h3>{{Paterno}}</h3>

                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Apellido Materno:</p>
                      <h3>{{Materno}}</h3>
                    </ion-label>
                  </ion-item>

                  <ion-item>
                    <ion-label>
                      <p>Cédula profesional</p>
                      <h3>{{Cedula}}</h3>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Telefono</p>
                      <h3>{{Telefono}}</h3>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Titulo profesional</p>
                      <h3>{{Titulo}}</h3>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Institución de estudios</p>
                      <h3>{{Institucion}}</h3>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-label>
                      <p>Año de titulación</p>
                      <h3>{{AnioRegistro}}</h3>
                    </ion-label>
                  </ion-item>

                </ion-list>


                <form *ngIf="verificado === false" [formGroup]="signupForm" autocomplete="off">
                  <ion-item>
                    <ion-label position="floating">Nombre
                      <ion-text color="danger">*</ion-text>
                    </ion-label>
                    <ion-input type="text" id="nombre" formControlName="nombre"  ngModel="{{nombreLog}}"></ion-input>
                  </ion-item><div class="error-message ion-text-center" >{{formError.nombre}}</div>
                  <ion-item>
                    <ion-label position="floating">Apellidos
                      <ion-text color="danger">*</ion-text>
                    </ion-label>
                    <ion-input type="text" id="apellido" formControlName="apellido"   ngModel="{{apellidoLog}}"></ion-input>
                  </ion-item><div class="error-message ion-text-center" >{{formError.apellido}}</div>

                  <ion-item>
                    <ion-label position="floating">Cédula profesional
                      <ion-text color="danger">*</ion-text>
                    </ion-label>
                    <ion-input type="tel" id="cedula" formControlName="cedula" ngModel="{{cedulaLog}}"></ion-input>

                  </ion-item><div class="error-message ion-text-center" >{{formError.cedula}}</div>


                  <ion-item>
                    <ion-label position="floating">Teléfono movil
                      <ion-text color="danger">*</ion-text>
                    </ion-label>
                    <ion-input type="tel" formControlName="telefono" ngModel="{{telefonoLog}}"></ion-input>

                  </ion-item><div class="error-message ion-text-center">{{formError.telefono}}</div>

                  <ion-item>
                    <ion-label position="floating">Título <span class="small_text" >(Este campo se valida de forma automática <br>al verificar tu cedula profesional)</span> </ion-label>
                    <ion-input type="text" id="titulo" formControlName="titulo" readonly></ion-input>
                  </ion-item>
                  <div class="error-message ion-text-center">{{formError.titulo}}</div>

                  <ion-button *ngIf="!verificado" expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid" (click)="validarCedula()"> <ion-spinner name="dots" slot="start" *ngIf="showSignupSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Validar Cédula </ion-button>

                </form>
                <ion-button *ngIf="!verificado" expand="block" size="small" class="ion-margin-top" color="light" (click)="logout()" > <ion-icon name="log-out" slot="end" ></ion-icon> Cerrar sesión </ion-button>
                <ion-button *ngIf="verificado" expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid || clicked" (click)="signup()"> <ion-spinner name="dots" slot="start" *ngIf="showSignupSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Completar registro</ion-button>

                <p *ngIf="verificado"> En caso de que the hayas equivocado de cedula profesional o tus datos no sean correctos, da <a (click)="corregirDatos();">click aquí para coregirlos</a></p>

                <!--                  <ion-button (click)="swipeNext()">Siguiente</ion-button>-->
              </ion-card-content>
            </ion-card>
          </swiper-slide>

          <swiper-slide>
            <ion-list>
              <ion-card class="cedula" (click)="obtenerDatos(cedula)" *ngFor="let cedula of data">
                <ion-card-content>
                  <h1 style="font-weight: bold; color: #044e5e"><ion-icon name="person"></ion-icon> {{cedula.nombre}}  {{cedula.paterno}} {{cedula.materno}}</h1>
                  <ion-card-subtitle color="primary"><ion-icon name="ribbon" size="small"></ion-icon> Cédula profesional: {{cedula.idCedula}}</ion-card-subtitle>
                  <ion-card-subtitle><ion-icon name="school" size="small"></ion-icon> Titulo: {{cedula.titulo}}</ion-card-subtitle>
                  <ion-card-subtitle class="small_text">Institución: {{cedula.desins}}
                    <br>Año de registro: {{cedula.anioreg}}</ion-card-subtitle>
                  <ion-button expand="block" color="secondary" size="large">Mi cédula es: {{cedula.idCedula}} <ion-icon name="log-in" size="large"></ion-icon></ion-button>
                </ion-card-content>

              </ion-card>

              <ion-card>
                <ion-card-content>

                  Si no encuentras tu cédula puedes corregirla dando click en el siguiente botón asegurate de ingreasar correctamente tu cédula profesional, ya que esta información será validada por la SEP para tu registro
                  <!--                  <ion-button expand="block"  (click)="swipePrev()">Corregir cédula</ion-button>-->


                  <ion-button expand="block" size="small" class="ion-margin-top" color="light" (click)="logout()" > <ion-icon name="log-out" slot="end" ></ion-icon> Cerrar sesión </ion-button>

                </ion-card-content>
              </ion-card>
            </ion-list>
          </swiper-slide>

        </swiper-container>

<!--        <swiper [slidesPerView]="1" [spaceBetween]="10">-->
<!--          &lt;!&ndash; Replace with your slide content &ndash;&gt;-->
<!--          <ng-template swiperSlide>-->
<!--            &lt;!&ndash; Your slide content here &ndash;&gt;-->
<!--            -->
<!--          </ng-template>-->

<!--          <ng-template swiperSlide>-->
<!--            &lt;!&ndash; Your slide content here &ndash;&gt;-->
<!--            -->
<!--          </ng-template>-->
<!--        </swiper>-->

<!--        <ion-slides pager="true"  pager="false" #mySlider [options]="slideOpts" >-->
<!--          <ion-slide >-->


<!--              <ion-card>-->
<!--                <ion-card-content>-->

<!--                  <h2 *ngIf="this.fuenteLog === 'Registro SanferConecta Respaldo'" style="color: red"> Disculpa las molestias, pero requerimos verificar tu cédula profesional</h2>-->
<!--                  <h1 *ngIf="tituloMedico === false" style="color: red"> Su titulo profesional no es Médico, corrige tu cédula profesional</h1>-->
<!--                  <p *ngIf="verificado === false">Al ser una aplicación exclusiva para médicos certificados, COFEPRIS nos exige validar tu cédula prfesional para poder hacer uso de la plataforma, a continuación ingresa tu cédula</p>-->
<!--                  <h2  *ngIf="verificado === true" style="color: red">Haz completado la verificación de tu cédula profesional ante la SEP</h2>-->
<!--                  <p *ngIf="verificado === true" class="small_text"> Verifica que tus datos sean correctos y da click en completar registro</p>-->

<!--                      <ion-list *ngIf="verificado === true" >-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Nombre: </p>-->
<!--                            <h3>{{Nombre}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Apellido Paterno: </p>-->
<!--                            <h3>{{Paterno}}</h3>-->

<!--                          </ion-label>-->

<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Apellido Materno:</p>-->
<!--                            <h3>{{Materno}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->

<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Cédula profesional</p>-->
<!--                            <h3>{{Cedula}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Telefono</p>-->
<!--                            <h3>{{Telefono}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Titulo profesional</p>-->
<!--                            <h3>{{Titulo}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Institución de estudios</p>-->
<!--                            <h3>{{Institucion}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->
<!--                        <ion-item>-->
<!--                          <ion-label>-->
<!--                            <p>Año de titulación</p>-->
<!--                            <h3>{{AnioRegistro}}</h3>-->
<!--                          </ion-label>-->
<!--                        </ion-item>-->

<!--                      </ion-list>-->


<!--                  <form *ngIf="verificado === false" [formGroup]="signupForm" autocomplete="off">-->
<!--                    <ion-item>-->
<!--                      <ion-label position="floating">Nombre-->
<!--                        <ion-text color="danger">*</ion-text>-->
<!--                      </ion-label>-->
<!--                      <ion-input type="text" id="nombre" formControlName="nombre"  ngModel="{{nombreLog}}"></ion-input>-->
<!--                    </ion-item><div class="error-message ion-text-center" >{{formError.nombre}}</div>-->
<!--                    <ion-item>-->
<!--                      <ion-label position="floating">Apellidos-->
<!--                        <ion-text color="danger">*</ion-text>-->
<!--                      </ion-label>-->
<!--                      <ion-input type="text" id="apellido" formControlName="apellido"   ngModel="{{apellidoLog}}"></ion-input>-->
<!--                    </ion-item><div class="error-message ion-text-center" >{{formError.apellido}}</div>-->

<!--                    <ion-item>-->
<!--                      <ion-label position="floating">Cédula profesional-->
<!--                        <ion-text color="danger">*</ion-text>-->
<!--                      </ion-label>-->
<!--                      <ion-input type="tel" id="cedula" formControlName="cedula" ngModel="{{cedulaLog}}"></ion-input>-->

<!--                    </ion-item><div class="error-message ion-text-center" >{{formError.cedula}}</div>-->


<!--                    <ion-item>-->
<!--                      <ion-label position="floating">Teléfono movil-->
<!--                        <ion-text color="danger">*</ion-text>-->
<!--                      </ion-label>-->
<!--                      <ion-input type="tel" formControlName="telefono" ngModel="{{telefonoLog}}"></ion-input>-->

<!--                    </ion-item><div class="error-message ion-text-center">{{formError.telefono}}</div>-->

<!--                    <ion-item>-->
<!--                      <ion-label position="floating">Título <span class="small_text" >(Este campo se valida de forma automática <br>al verificar tu cedula profesional)</span> </ion-label>-->
<!--                      <ion-input type="text" id="titulo" formControlName="titulo" readonly></ion-input>-->
<!--                    </ion-item>-->
<!--                    <div class="error-message ion-text-center">{{formError.titulo}}</div>-->

<!--                    <ion-button *ngIf="!verificado" expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid" (click)="validarCedula()"> <ion-spinner name="dots" slot="start" *ngIf="showSignupSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Validar Cédula </ion-button>-->

<!--                  </form>-->
<!--                  <ion-button *ngIf="!verificado" expand="block" size="small" class="ion-margin-top" color="light" (click)="logout()" > <ion-icon name="log-out" slot="end" ></ion-icon> Cerrar sesión </ion-button>-->
<!--                  <ion-button *ngIf="verificado" expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid || clicked" (click)="signup()"> <ion-spinner name="dots" slot="start" *ngIf="showSignupSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Completar registro</ion-button>-->

<!--                  <p *ngIf="verificado"> En caso de que the hayas equivocado de cedula profesional o tus datos no sean correctos, da <a (click)="corregirDatos();">click aquí para coregirlos</a></p>-->

<!--                  &lt;!&ndash;                  <ion-button (click)="swipeNext()">Siguiente</ion-button>&ndash;&gt;-->
<!--                </ion-card-content>-->
<!--              </ion-card>-->
<!--          </ion-slide>-->
<!--          <ion-slide >-->

<!--            <ion-list>-->
<!--              <ion-card class="cedula" (click)="obtenerDatos(cedula)" *ngFor="let cedula of data">-->
<!--                <ion-card-content>-->
<!--                  <h1 style="font-weight: bold; color: #044e5e"><ion-icon name="person"></ion-icon> {{cedula.nombre}}  {{cedula.paterno}} {{cedula.materno}}</h1>-->
<!--                  <ion-card-subtitle color="primary"><ion-icon name="ribbon" size="small"></ion-icon> Cédula profesional: {{cedula.idCedula}}</ion-card-subtitle>-->
<!--                  <ion-card-subtitle><ion-icon name="school" size="small"></ion-icon> Titulo: {{cedula.titulo}}</ion-card-subtitle>-->
<!--                  <ion-card-subtitle class="small_text">Institución: {{cedula.desins}}-->
<!--                    <br>Año de registro: {{cedula.anioreg}}</ion-card-subtitle>-->
<!--                  <ion-button expand="block" color="secondary" size="large">Mi cédula es: {{cedula.idCedula}} <ion-icon name="log-in" size="large"></ion-icon></ion-button>-->
<!--                </ion-card-content>-->

<!--              </ion-card>-->

<!--              <ion-card>-->
<!--                <ion-card-content>-->

<!--                  Si no encuentras tu cédula puedes corregirla dando click en el siguiente botón asegurate de ingreasar correctamente tu cédula profesional, ya que esta información será validada por la SEP para tu registro-->
<!--                  <ion-button expand="block"  (click)="swipePrev()">Corregir cédula</ion-button>-->


<!--                  <ion-button expand="block" size="small" class="ion-margin-top" color="light" (click)="logout()" > <ion-icon name="log-out" slot="end" ></ion-icon> Cerrar sesión </ion-button>-->

<!--                </ion-card-content>-->
<!--              </ion-card>-->
<!--            </ion-list>-->


<!--          </ion-slide>-->



<!--        </ion-slides>-->

      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
