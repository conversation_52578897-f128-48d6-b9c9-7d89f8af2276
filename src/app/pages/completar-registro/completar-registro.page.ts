import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {AlertController, LoadingController, ModalController, Platform} from '@ionic/angular';
import {CedulaSepService} from '../../services/cedula-sep.service';
import {WidgetUtilService} from '../../services/widget-util.service';

// import {SwiperComponent, SwiperModule} from 'swiper/angular';

// import '@capacitor-community/http';
// import { Plugins } from '@capacitor/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {COMPLETAR} from '../../constants/formValidationMessage';
import {FirebaseAuthService} from '../../services/firebase-auth.service';
import {FirestoreDbService} from '../../services/firestore-db.service';
import {HelperService} from '../../services/helper.service';
import {Router} from '@angular/router';
// import {FirebaseAnalyticsService} from '../../services/firebase-analitycs.service';
// import {GenerarCodigoPage} from '../generar-codigo/generar-codigo.page';
import {CompletarRegistroCedulaPage} from '../completar-registro-cedula/completar-registro-cedula.page';
// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
// import { Browser } from '@capacitor/browser';
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import {FieldValue} from 'firebase';
// import { AngularFirestore } from '@angular/fire/firestore';
import {Firestore, doc, collection, getDoc, writeBatch, increment} from '@angular/fire/firestore';

import firebase from 'firebase/compat/app';
import {environment} from '../../../environments/environment';
import {CapacitorHttp, HttpResponse} from "@capacitor/core";
import {SwiperContainer} from "swiper/swiper-element";

// import function to register Swiper custom elements
import { register } from 'swiper/element/bundle';
// register Swiper custom elements
register();

// const { Http, Storage} = Plugins;

// const increment = firebase.firestore.FieldValue.increment(1);
// const decrement = firebase.firestore.FieldValue.increment(1);

@Component({
  selector: 'app-completar-registro',
  templateUrl: './completar-registro.page.html',
  styleUrls: ['./completar-registro.page.scss'],
})
export class CompletarRegistroPage implements OnInit {

  // @ViewChild('mySlider')  slides: IonSlides ;

  // @ViewChild(SwiperComponent, { static: false }) swiper?: SwiperComponent;

  @ViewChild('swiper') swiper!: ElementRef<SwiperContainer>;


  @Input() nombreLog: string = '';
  @Input() apellidoLog: string = '';
  @Input() uidLog: string = '';
  @Input() emailLog: string = '';
  @Input() representanteLog: string = '';
  @Input() fuenteLog: string = '';
  @Input() cedulaLog: string = '';
  @Input() telefonoLog: string = '';


  data: any = [];

  cedulaReturnData: any = {};

  fecha = new Date();
  signupForm: any = FormGroup;
  nombre: any = FormControl;
  apellido: any = FormControl;
  telefono: any = FormControl;
  cedula: any = FormControl;
  titulo: any = FormControl;


  fuenteDelRegistro: string = '';


  registroCompletado = false;

  Nombre: string = '';
  Paterno: string = '';
  Materno: string = '';
  Cedula: string = '';
  Titulo: string = '';
  Institucion: string = '';
  AnioRegistro: string = '';
  Tipo: string = '';
  Genero: string = '';
  Telefono: string = '';

  version = environment.version;

  clicked = false;

  formError: any = {
    nombre: '',
    apellido: '',
    cedula: '',
    telefono: '',
    titulo: '',

  };

  verificado: boolean = false;
  tituloMedico: boolean = false;

  validationMessage: any = COMPLETAR;
  showSignupSpinner = false;
  titulosPermitidos = [];

  slideOpts = {
    initialSlide: 0,
    speed: 400,
    allowTouchMove: false

  };
  // await this.slides.lockSwipes(false);
  // await this.slides.lockSwipeToNext(true);

  constructor(private strapi: StrapiService,
              private plt: Platform,
              private loadingCtrl: LoadingController,
              private cedulaService: CedulaSepService,
              private modalCtrl: ModalController,
              private widgetUtilService: WidgetUtilService,
              private helperService: HelperService,
              private router: Router,
              private firebaseAuthService: FirebaseAuthService,
              private firestoreDbService: FirestoreDbService,
              // private fbas: FirebaseAnalyticsService,
              public alertController: AlertController,
              // private db: AngularFirestore,
              private firestore: Firestore,


  ) {
  }



  async ngOnInit() {
    this.strapiControls();
    this.createFormControl();
    this.createForm();
  }


  async createFormControl() {
    this.nombre = new FormControl('', [
      Validators.required
    ]);
    this.apellido = new FormControl('', [
      Validators.required
    ]);
    this.cedula = new FormControl('', [
      Validators.required
      // Validators.minLength(7)
    ]);
    this.telefono = new FormControl('', [
      Validators.required,
      Validators.minLength(10),
      Validators.maxLength(10)

    ]);
    this.titulo = new FormControl('', [
    ]);
  }

  createForm() {
    this.signupForm = new FormGroup({
      nombre: this.nombre,
      apellido: this.apellido,
      cedula: this.cedula,
      telefono: this.telefono,
      titulo: this.titulo,
    });
    this.signupForm.valueChanges.subscribe((data: any) => this.onFormValueChanged(data));
  }

  onFormValueChanged(data: any) {
    this.formError = this.helperService.prepareValidationMessage(this.signupForm, this.validationMessage, this.formError);
  }

  async obtenerDatos(cedula: any) {

    this.Nombre = cedula.nombre;
    this.Paterno =  cedula.paterno;
    this.Materno = cedula.materno;
    this.Cedula = cedula.idCedula;
    this.Titulo = cedula.titulo;
    this.Institucion = cedula.desins;
    this.AnioRegistro = cedula.anioreg;
    this.Tipo = cedula.tipo;
    this.Genero = cedula.sexo;
    this.Telefono = this.telefono.value;


    // Obtiene los datos de los titulos permitidos por la aplicacion listados dentro de Strapi
    // const condiciones = this.titulosPermitidos;


    // Obtiene los datos de los titulos permitidos por la aplicacion listados dentro de Strapi
    const verificacion = this.titulosPermitidos.some(el => this.Titulo.includes(el));
    if (verificacion  === true) {
      const nombreRecabado = (<HTMLInputElement> document.getElementById('nombre'))
          .value = `${this.Nombre}`;
      const apellidoRecabado = (<HTMLInputElement> document.getElementById('apellido'))
          .value = `${this.Paterno} ${this.Materno}`;
      // const cedulaRecabado = (<HTMLInputElement> document.getElementById('cedula'))
      //     .value = `${this.Cedula}`;
      const tituloRecabado = (<HTMLInputElement> document.getElementById('titulo'))
          .value = `${this.Titulo}`;

      this.widgetUtilService.presentToast('¡Cedula verificada!<br> Puedes registrarte');
      // this.swipePrev();
      this.verificado = true;
      this.tituloMedico = true;


    } else {
      // this.swipePrev();
      // const nombreRecabado = (<HTMLInputElement> document.getElementById('nombre'))
      //     .value = `${this.Nombre}`;
      // const apellidoRecabado = (<HTMLInputElement> document.getElementById('apellido'))
      //     .value = `${this.Paterno} ${this.Materno}`;
      const tituloRecabado = (<HTMLInputElement> document.getElementById('titulo'))
          .value = `${this.Titulo}`;
      this.widgetUtilService.presentToastError('Disculpe esta aplicación es solo para los profesionales de la Salud, si se equivoco de cedula profesional intente nuevamente');
      this.verificado = false;
      this.tituloMedico = false;


    }

  }


  corregirDatos() {

    this.verificado = false;
  }



  closeModal(registro: any) {

    this.modalCtrl.dismiss({registroCompletado: registro});
  }
  async clearStorage() {
    await Preferences.clear();
  }
  async logout() {
    try {
      await this.firebaseAuthService.logout();
      // this.fbas.logEvent('logout');


      // firebase.analytics().logEvent('logout');

      this.widgetUtilService.presentToast('Sesión cerrada satisfactoriamente');
      this.router.navigate(['/home']);
      document.location.href = 'index.html';
      this.clearStorage();

    } catch (error: any) {

      console.log('Error', error);
      this.widgetUtilService.presentToastError(error.message);
    }

  }

  async validarCedula() {
    // Cambia al siguiente slide
    // this.swipeNext();

    // Esta funcion toma la información ingresada por el usuario en el formulario y la anida en las siguientes constantes

    const doctorName =  this.nombre.value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    const doctorLastName =  this.apellido.value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    const doctorCedula =  this.cedula.value.toString();


    // Esta funcion ejecuta la validacion por la sep y el retorno lo agrega en la constante datosSep, aunque es redundante
    // y la vuelve a anidar en la variable this.cedulaReturnData
    const datosSep = await this.consultarSep(doctorName, doctorLastName, doctorCedula );

    this.cedulaReturnData = datosSep;
    console.log('Retorno de la información', this.cedulaReturnData);

  }


  // swipeNext() {
  // //   this.slides.slideNext();
  // // }
  // swipePrev() {
  // //   this.slides.slidePrev();
  // // }


  // swipeNext() {
  //   if (this.swiper) {
  //     this.swiper.slideNext();
  //   }
  // }
  //
  // swipePrev() {
  //   if (this.swiper && this.swiper.swiperRef) {
  //     this.swiper.swiperRef.slidePrev();
  //   }
  // }



  // async consultarSep( doctorName: string, doctorLastName: string, doctorCedula: string) {
  //
  //   await this.widgetUtilService.presentLoadingMessage('Consultando tu cedula en la SEP, espera un momento...');
  //   // await loading.present();
  //   const apellidoDividido = doctorLastName.split(' ');
  //
  //   console.log('Apeelido Dividido: ', apellidoDividido);
  //   const rutaSep = `https://api.allorigins.win/raw?&url=https://www.cedulaprofesional.sep.gob.mx/cedula/buscaCedulaJson.action?json=%7B%22maxResult%22%3A%2250%22%2C%22nombre%22%3A%22${doctorName
  //       .replace(/\s/g, '+')}%22%2C%22paterno%22%3A%22${apellidoDividido[0]}%22%2C%22materno%22%3A%22${apellidoDividido[1]}%22%2C%22idCedula%22%3A%22${doctorCedula}%22%7D`;
  //
  //   console.log('URL de la SEP', rutaSep);
  //   // Intenta contactar con la SEP
  //   try {
  //
  //
  //     const options = {
  //       url: 'https://sanferconecta.live/sep/new',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       },
  //       data: {
  //         url: rutaSep
  //       }
  //
  //     };
  //
  //     const response: HttpResponse = await CapacitorHttp.post(options);
  //     console.log('Retorno RAW: ', response);
  //     this.data = response.data.items;
  //
  //     // await loading.dismiss();
  //
  //     await this.widgetUtilService.dismissLoader();
  //
  //     await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa!<br><h3>Selecciona tu cedula</h3>');
  //
  //     return this.data;
  //
  //   } catch (e) {
  //
  //     // Si hay un error intenta conectar nuevamente ocn la sep
  //
  //     console.log('Error', e);
  //   }
  // }


  async consultarSep(doctorName: string, doctorLastName: string, doctorCedula: string) {
    await this.widgetUtilService.presentLoadingMessage('Consultando tu cedula en la SEP, espera un momento...');
    const apellidoDividido = doctorLastName.split(' ');

    const rutaSep = `https://api.allorigins.win/raw?&url=https://www.cedulaprofesional.sep.gob.mx/cedula/buscaCedulaJson.action?json=%7B%22maxResult%22%3A%2250%22%2C%22nombre%22%3A%22${doctorName.replace(/\s/g, '+')}%22%2C%22paterno%22%3A%22${apellidoDividido[0]}%22%2C%22materno%22%3A%22${apellidoDividido[1]}%22%2C%22idCedula%22%3A%22${doctorCedula}%22%7D`;

    try {
      const options = {
        url: 'https://sanferconecta.live/sep/new',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          url: rutaSep
        }
      };

      const response = await CapacitorHttp.post(options);
      this.data = response.data.items;

      await this.widgetUtilService.dismissLoader();
      await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa!<br><h3>Selecciona tu cedula</h3>');

      return this.data;

    } catch (e) {
      console.error('Error', e);
      await this.widgetUtilService.dismissLoader();
      // Handle the error case
      // Depending on your app's logic, you might want to return an empty array, null, or propagate the error
      return null; // or [] or throw e;
    }
  }




  // Registro de medicos, validando cedula profesional
  async signup() {
    try {
      this.clicked = true;
      this.showSignupSpinner = true;
      const apellidoCompleto = this.Paterno + ' ' + this.Materno;
      console.log('Email Obtenido', this.emailLog);


      const getInfoRepresentanteRef = doc(this.firestore, `representantes/${this.representanteLog}`);

      // Get representante data
      const representanteSnapshot = await getDoc(getInfoRepresentanteRef);
      const infoRepresentante: any = representanteSnapshot.data();

      // Create document references for Firestore operations
      const updateMedicoRef = doc(this.firestore, `medicos/${this.uidLog}`);
      const createDoctorRef = doc(this.firestore, `doctores/${this.Cedula}`);
      const asignMedicoRef = doc(this.firestore, `representantes/${this.representanteLog}/medicos/${this.uidLog}`);
      const asignRepresentanteRef = doc(this.firestore, `medicos/${this.uidLog}/representantes/${this.representanteLog}`);
      const medicoProductividadRef = doc(this.firestore, `productividad/${infoRepresentante.udn}/${infoRepresentante.equipo}/medicos/listado/${this.uidLog}`);

      const actualizarContadorUdnRef = doc(this.firestore, `productividad/${infoRepresentante.udn}`);
      const actualizarContadorEquipoRef = doc(this.firestore, `productividad/${infoRepresentante.udn}/${infoRepresentante.equipo}/medicos`);


      if (this.fuenteLog === 'noMedico') {
        this.fuenteDelRegistro = `Registro a travez del QR del representante con id ${this.representanteLog} el día ${this.fecha.toDateString()}`;



        // Create batch instance
        const batch = writeBatch(this.firestore);

        // Set data to the 'medico' document within the batch
        batch.set(updateMedicoRef, {
          nombre: this.Nombre,
          apellido: apellidoCompleto,
          apellidoP: this.Paterno,
          apellidoM: this.Materno,
          cedula: this.Cedula,
          anioRegistro: this.AnioRegistro,
          institucion: this.Institucion,
          tipo: this.Tipo,
          genero: this.Genero,
          titulo: this.Titulo,
          role: 'medico',
          fechaFinal: this.fecha.valueOf(),
          horaFinal: this.fecha.toLocaleTimeString(),
          telefono: this.Telefono,
          Registro: this.fuenteDelRegistro,
          createdAt: this.fecha.valueOf(),
          representanteNombre: infoRepresentante.nombre + ' ' + infoRepresentante.apellido,
          representanteNumeroEmpleado: infoRepresentante.nEmpleado
        }, { merge: true });

        console.log('Médico Actualizado');

        batch.set(asignMedicoRef, {
          nombre: this.Nombre,
          apellido: apellidoCompleto,
          cedula: this.Cedula,
          titulo: this.Titulo,
          email: this.emailLog,
          telefono: this.Telefono,
          Registro: this.fuenteDelRegistro,
          uid: this.uidLog,
          createdAt: this.fecha.valueOf(),

        });

        console.log('Médico Asignado');


        batch.set(asignRepresentanteRef, {
          nombre: infoRepresentante.nombre,
          apellido: infoRepresentante.apellido,
          equipo: infoRepresentante.equipo,
          email: infoRepresentante.email,
          telefono: infoRepresentante.telefono,
          uid: this.representanteLog,
          fechaRegistro: this.fecha.valueOf(),
          udn: infoRepresentante.udn,
          nEmpleado: infoRepresentante.nEmpleado,
          createdAt: this.fecha.valueOf(),

        });

        console.log('Representante Asignado');

        batch.set(medicoProductividadRef, {
          nombre: this.Nombre,
          apellido: apellidoCompleto,
          cedula: this.Cedula,
          titulo: this.Titulo,
          email: this.emailLog,
          telefono: this.Telefono,
          Registro: this.fuenteDelRegistro,
          uid: this.uidLog,
          uidRepresentante: this.representanteLog,
          createdAt: this.fecha.valueOf(),
        });

        console.log('Médico en Productividad');

        batch.update(actualizarContadorUdnRef, {
          registros: increment(1),
          medicos: increment(1)
        });

        console.log('Contador de UDN Actualizado');

        batch.update(actualizarContadorEquipoRef, {
          registros: increment(1)
        });

        console.log('Contador de Equipo Actualizado');

        // ... other batch operations

        // Commit the batch
        await batch.commit();

        // ... other operations like analytics logging and storage update

        // Registro de analitys
        // this.fbas.logEvent('sign_up');
        // this.fbas.setUserProperty('genero', `${this.Genero}`);
        // this.fbas.setUserProperty('titulo_profesional', `${this.Titulo}`);





        await this.setUserDataInStorage();

      } else if (this.fuenteLog === 'Registro SanferConecta Respaldo') {
        this.fuenteDelRegistro = `Registro actualizado validado por la SEP el día ${this.fecha.toString()}`;

        // // Create document references for 'medico' and 'doctor'
        // const updateMedicoRef = doc(this.firestore, `medicos/${this.uidLog}`);
        // const createDoctorRef = doc(this.firestore, `doctores/${this.Cedula}`);

        // Create a batch instance
        const batch = writeBatch(this.firestore);

        // Set data to the 'medico' document within the batch
        batch.set(updateMedicoRef, {
          nombre: this.Nombre,
          apellido: apellidoCompleto, // assuming apellidoCompleto is defined earlier
          apellidoP: this.Paterno,
          apellidoM: this.Materno,
          cedula: this.Cedula,
          anioRegistro: this.AnioRegistro,
          institucion: this.Institucion,
          tipo: this.Tipo,
          genero: this.Genero,
          titulo: this.Titulo,
          fechaMs: this.fecha.valueOf(),
          role: 'medico',
          fechaFinal: this.fecha.valueOf(),
          horaFinal: this.fecha.toLocaleTimeString(),
          telefono: this.Telefono,
          Registro: this.fuenteDelRegistro,
          createdAt: this.fecha.valueOf(),
        }, { merge: true });

        // Uncomment the following line if you need to set data to the 'doctor' document
        // batch.set(createDoctorRef, { /* Doctor data here */ }, { merge: true });

        // Commit the batch
        await batch.commit();

        // Registro de analytics
        // this.fbas.logEvent('sign_up');
        // this.fbas.setUserProperty('genero', `${this.Genero}`);
        // this.fbas.setUserProperty('titulo_profesional', `${this.Titulo}`);

        // Call function to set user data in storage
        await this.setUserDataInStorage();

        // Additional code...
      }

      this.clicked = false;
      this.showSignupSpinner = false;
    } catch (error) {
      console.log('Error', error);
      this.showSignupSpinner = false;
      this.clicked = false;
      // Handle errors here

      await this.widgetUtilService.presentToastError(`Hubo un problema con tu registro, el servidor nos comunica este error: "${error}"`);

      const confirm = await this.alertController.create({
        cssClass: 'my-custom-class',
        message: `Hubo un problema con tu registro, el servidor nos comunica este error: "${error}"`,
        buttons: [
          {
            text: 'Confirmar',
            handler: () => {
              console.log(`Hubo un problema con tu registro, el servidor nos comunica este error: "${error}"`);
            }
          }
        ]
      });
      await confirm.present();
    }
  }


  resetForm() {
    this.signupForm.reset();
    this.formError = {
      nombre: '',
      apellido: '',
      cedula: '',
      telefono: '',
      titulo: '',

    };
  }

  async strapiControls() {
    await this.strapi.getContenido('controles')
        .subscribe(async resp => {
          // console.log('Controles', resp.titulos);
          const arreglo: any = [];
          for ( const titulo of resp.titulos) {
            arreglo.push(titulo.titulo);
          }

          // Obtiene los datos de los titulos permitidos por la aplicacion listados dentro de Strapi
          this.titulosPermitidos = await arreglo;

          // console.log('Titulos permitidos', this.titulosPermitidos);
        });
  }




  async setUserDataInStorage() {


    const solicitarUsuario: any = await this.firestoreDbService.getDataById('medicos', this.uidLog);

    await Preferences.remove({ key: 'user' });

    console.log('Usuario en Firebase', solicitarUsuario);

    await Preferences.set({
      key: 'user',
      value: JSON.stringify({
        uid: this.uidLog,
        cedula: solicitarUsuario.cedula,
        nombre: solicitarUsuario.nombre,
        titulo: solicitarUsuario.titulo,
        isAdmin: solicitarUsuario.admin,
        apellido: solicitarUsuario.apellido,
        correo: solicitarUsuario.email,
        telefono: solicitarUsuario.telefono,
        role: solicitarUsuario.role,
        nEmpleado: solicitarUsuario.nEmpleado,
        passcode: solicitarUsuario.passcode,
        institucion: solicitarUsuario.institucion,
        tipo: solicitarUsuario.tipo,
        anioRegistro: solicitarUsuario.anioRegistro,
        genero: solicitarUsuario.genero,
        fecha: solicitarUsuario.fechaMs,
        fechaFinal: solicitarUsuario.fechaFinal,
      })
    });


    this.showSignupSpinner = false;
    this.resetForm();

    this.registroCompletado = true;
    this.closeModal(this.registroCompletado);

  }





}




