import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { FarmacovigilanciaPage } from './farmacovigilancia.page';

const routes: Routes = [
  {
    path: '',
    component: FarmacovigilanciaPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,

    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [FarmacovigilanciaPage]
})
export class FarmacovigilanciaPageModule {}
