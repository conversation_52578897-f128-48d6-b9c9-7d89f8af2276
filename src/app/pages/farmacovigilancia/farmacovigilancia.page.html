<ion-header>
    <ion-toolbar color="primary">
        <ion-title>Farmacovigilancia</ion-title>
        <ion-buttons slot="start">
            <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
        </ion-buttons>
    </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">

    <ion-grid>
        <ion-row>
            <ion-col class="ion-padding">
                <h3 class="ion-text-center">Si usted prefiere puede llamar al número:</h3>
                <ion-card-title color="primary" class="ion-text-center">01 800-Sanfer1 (800 726 3371)</ion-card-title>
                <h3 class="ion-text-center">las 24 horas del día, o llenar el formulario a continuación:</h3>
                <h3 class="ion-text-center small_text">Favor de completar los datos que se requieren. Toda la
                    información del reporte es indispensable.</h3>
            </ion-col>
        </ion-row>

      <ion-item-divider>
        <ion-label text-center class="ion-text-center">
          Información del paciente
        </ion-label>
      </ion-item-divider>
        <form [formGroup]="farmacoForm" autocomplete="off">
            <ion-item>
                <ion-label position="stacked">Nombre completo o iniciales:
                    <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input required type="text" formControlName="nombrePaciente"></ion-input>
            </ion-item>
            <div class="error-message ion-text-center">{{formError.nombrePaciente}}</div>
            <ion-row>
                <ion-col size="12" size-xs="12" size-sm="6">

                    <ion-list lines="full" class="ion-no-margin ion-no-padding">


                        <ion-item>
                            <ion-label>Fecha de nacimiento
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-datetime value="1980-01-01" placeholder="Selecciona la fecha" displayFormat="YYYY-MM-DD" min="1981" max="2002"
                                          formControlName="fechaDeNaciemiento"></ion-datetime>
                        </ion-item>
                        <div class="error-message ion-text-center">{{formError.fechaDeNaciemiento}}</div>
                        <ion-item>
                            <ion-label>Genero
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-select value="genero" interface="popover" formControlName="genero">
                                <ion-select-option value="masculino">Masculino</ion-select-option>
                                <ion-select-option value="fememnino">Femenino</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.genero}}</div>

                    </ion-list>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="6">
                    <ion-item>
                        <ion-label position="floating">Peso</ion-label>
                        <ion-input formControlName="peso" ></ion-input>
                    </ion-item>
                    <ion-item>
                        <ion-label position="floating">Talla (cm)</ion-label>
                        <ion-input formControlName="talla"></ion-input>
                    </ion-item>
                </ion-col>
            </ion-row>
            <ion-item-divider>
                <ion-label>
                </ion-label>
            </ion-item-divider>
            <ion-row>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-list>

                        <ion-item>
                            <ion-label position="stacked">Descripción de la sospecha de reacción adversa al medicamento
                                (SRAM):
                                <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-textarea auto-grow="true" clearOnEdit="true" formControlName="descripcionDeSospecha"></ion-textarea>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.descripcionDeSospecha}}</div>

                      <ion-item>
                            <ion-label>Indicar si es:</ion-label>
                            <ion-select value="genero" interface="popover" formControlName="indicarSiEs">
                                <ion-select-option value="Prescripción Médica">Prescripción Médica</ion-select-option>
                                <ion-select-option value="Automedicación">Automedicación</ion-select-option>
                            </ion-select>
                        </ion-item>
                    </ion-list>
                </ion-col>
            </ion-row>

            <ion-item-divider>
                <ion-label text-center class="ion-text-center">
                    ¿Cuánto tiempo se manifestó la SRAM?
                </ion-label>
            </ion-item-divider>
            <ion-row>
                <ion-col size="12" size-xs="12" size-sm="6">
                    <ion-list>

                        <ion-item>
                            <ion-label>Fecha de inicio
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-datetime value="1990-02-19" placeholder="Select Date" formControlName="fechadDeInicio" displayFormat="YYYY-MM-DD" min="2010"></ion-datetime>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.fechadDeInicio}}</div>

                        <ion-item>
                            <ion-label>¿Se disminuyó la dosis?</ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="disminuyoLaDosis">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.disminuyoLaDosis}}</div>

                      <ion-item>
                            <ion-label>¿Se recuperó el paciente de la SRAM?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="recuperoDeLaSRAM">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.recuperoDeLaSRAM}}</div>

                    </ion-list>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="6">
                    <ion-list>
                        <ion-item>
                            <ion-label>Fecha de término
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-datetime value="1990-02-20" placeholder="Select Date" formControlName="fechaDeTermino" displayFormat="YYYY-MM-DD" min="2010" max="2030"></ion-datetime>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.fechaDeTermino}}</div>

                      <ion-item>
                            <ion-label>¿Se readministró el medicamento?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="readministroMedicamento">
                                <ion-select-option value="Si">Sí</ion-select-option>
                                <ion-select-option value="No">No</ion-select-option>
                                <ion-select-option value="No sabe">No sabe</ion-select-option>
                                <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.readministroMedicamento}}</div>

                        <ion-item>
                            <ion-label>¿Se cambió la farmacoterapia?</ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="cambioFarmacoterapia">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                        <div class="error-message ion-text-center">{{formError.cambioFarmacoterapia}}</div>


                    </ion-list>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-list>
                        <ion-item>
                            <ion-label>¿Suspendió el medicamento debido a la SRAM?</ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="suspendioMedicamento">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                        <div class="error-message ion-text-center">{{formError.suspendioMedicamento}}</div>

                        <ion-item>
                            <ion-label>¿Hubo mejoría al suspender el medicamento?</ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="mejoriaAlSuspender">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                        <div class="error-message ion-text-center">{{formError.mejoriaAlSuspender}}</div>

                        <ion-item>
                            <ion-label>¿Tomó algún medicamento para tratar la SRAM?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="medicamentoParaTratar">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.medicamentoParaTratar}}</div>

                      <ion-item>
                            <ion-label>Cuando se readministró el medicamento, ¿se presentó nuevamente la SRAM?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-select value="suspension_sram" interface="popover" formControlName="presentoNuevementeLaSRAM">
                              <ion-select-option value="Si">Sí</ion-select-option>
                              <ion-select-option value="No">No</ion-select-option>
                              <ion-select-option value="No sabe">No sabe</ion-select-option>
                              <ion-select-option value="No Aplica">No Aplica</ion-select-option>
                            </ion-select>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.presentoNuevementeLaSRAM}}</div>

                    </ion-list>
                </ion-col>

                <ion-item-divider>
                    <ion-label>Datos del medicamento sospechoso

                    </ion-label>
                </ion-item-divider>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-item>
                        <ion-label>Nombre comercial:
                          <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-select ok-text="OK" cancel-text="CANCELAR" formControlName="nombreComercial">
                            <ion-select-option
                                    *ngFor="let producto of vademecum" [value]="producto.Nombre">{{producto.Nombre}}</ion-select-option>
                        </ion-select>
                    </ion-item>
                  <div class="error-message ion-text-center">{{formError.nombreComercial}}</div>

                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="6">

                    <ion-list>

                        <ion-item>
                            <ion-label position="floating">Forma farmacéutica:
                            </ion-label>
                            <ion-input formControlName="formaFarmaceutica"></ion-input>
                        </ion-item>
                        <ion-item>
                            <ion-label position="floating">Número de lote:
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-input formControlName="numLote"></ion-input>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.numLote}}</div>

                      <ion-item>
                            <ion-label>Fecha de caducidad</ion-label>
                            <ion-datetime placeholder="Selecciona la fecha" formControlName="caducidad" displayFormat="YYYY-MM-DD" min="2010" max="2030"></ion-datetime>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.caducidad}}</div>


                    </ion-list>
                </ion-col>


                <ion-col size="12" size-xs="12" size-sm="6">

                    <ion-list>


                        <ion-item>
                            <ion-label position="floating">Nombre genérico:
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-input formControlName="nombreGenerico"></ion-input>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.nombreGenerico}}</div>

                      <ion-item>
                            <ion-label position="floating">Vía de administración:
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-input formControlName="viaAdministracion"></ion-input>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.viaAdministracion}}</div>

                    </ion-list>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-list>
                        <ion-item>
                            <ion-label position="floating">¿Para qué enfermedad lo tomó?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-input formControlName="paraQueEnfermedad"></ion-input>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.paraQueEnfermedad}}</div>

                    </ion-list>
                </ion-col>
                <ion-item-divider>
                    <ion-label>¿Qué dosis tomo y en qué horario?</ion-label>
                </ion-item-divider>
                <ion-col size="12" size-xs="12" size-sm="6">
                    <ion-item>
                        <ion-label position="floating">Dosis:
                          <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-input formControlName="dosis"></ion-input>
                    </ion-item>
                  <div class="error-message ion-text-center">{{formError.dosis}}</div>

                  <ion-item>
                        <ion-label>¿Tenía tratamiento con algún otro medicamento?</ion-label>
                        <ion-select interface="popover" formControlName="otroMedicamento">
                            <ion-select-option value="Si">Si</ion-select-option>
                            <ion-select-option value="No">No</ion-select-option>
                        </ion-select>
                    </ion-item>
                    <div class="error-message ion-text-center">{{formError.otroMedicamento}}</div>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="6">
                    <ion-item>
                        <ion-label position="floating">Horario:
                          <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-input formControlName="horario"></ion-input>
                    </ion-item>
                  <div class="error-message ion-text-center">{{formError.horario}}</div>

                  <ion-item>
                        <ion-label>¿Antecedentes de alergias?
                          <ion-text color="danger">*</ion-text>
                        </ion-label>
                        <ion-select interface="popover" formControlName="antecedentesAlergias">
                            <ion-select-option value="Si">Si</ion-select-option>
                            <ion-select-option value="No">No</ion-select-option>
                        </ion-select>
                    </ion-item>
                  <div class="error-message ion-text-center">{{formError.antecedentesAlergias}}</div>

                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-list>
                        <ion-item>
                            <ion-label position="stacked">¿Cuáles?
                                <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-textarea auto-grow="true" clearOnEdit="true" formControlName="alergias"></ion-textarea>
                        </ion-item>
                        <ion-item>
                            <ion-label position="stacked">Datos relevantes de la historia clínica del paciente:
                                <ion-text color="danger">*</ion-text>
                            </ion-label>
                            <ion-textarea auto-grow="true" clearOnEdit="true" formControlName="datosRelevantes"></ion-textarea>
                        </ion-item>
                      <div class="error-message ion-text-center">{{formError.datosRelevantes}}</div>

                        <ion-radio-group formControlName="condicionMedica">
                            <ion-list-header>
                                <ion-label>¿Existen algunas de las siguientes condiciones?
                                  <ion-text color="danger">*</ion-text>
                                </ion-label>
                              <div class="error-message ion-text-center">{{formError.condicionMedica}}</div>

                            </ion-list-header>
                            <ion-item>
                                <ion-label>Embarazo</ion-label>
                                <ion-radio slot="start" color="primary" value="Embarazo"></ion-radio>
                            </ion-item>

                            <ion-item>
                                <ion-label>Lactancia</ion-label>
                                <ion-radio slot="start" color="primary" value="Lactancia"></ion-radio>
                            </ion-item>

                            <ion-item>
                                <ion-label>Relación con la calidad</ion-label>
                                <ion-radio slot="start" color="primary" value="Relación con la calidad"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Mal uso</ion-label>
                                <ion-radio slot="start" color="primary" value="Mal uso"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Transmisión de agentes infecciosos</ion-label>
                                <ion-radio slot="start" color="primary" value="Transmisión de agentes infecciosos"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Sobredosis</ion-label>
                                <ion-radio slot="start" color="primary" value="Sobredosis"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Error de medicación</ion-label>
                                <ion-radio slot="start" color="primary" value="Error de medicación"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Abuso</ion-label>
                                <ion-radio slot="start" color="primary" value="Abuso"></ion-radio>
                            </ion-item>
                            <ion-item>
                                <ion-label>Enfermedad crónico-degenerativa</ion-label>
                                <ion-radio slot="start" color="primary" value="Enfermedad crónico-degenerativa"></ion-radio>
                            </ion-item>
                        </ion-radio-group>

                    </ion-list>
                </ion-col>
                <ion-col size="12" size-xs="12" size-sm="12">
                    <ion-radio-group  formControlName="conacto">
                        <ion-list-header>
                            <ion-label>¿Desea ser contactado por Farmacovigilancia?
                              <ion-text color="danger">*</ion-text>
                            </ion-label>
                        </ion-list-header>
                        <ion-item>
                            <ion-label>Si</ion-label>
                            <ion-radio slot="start" color="success" value="Si"></ion-radio>
                        </ion-item>

                        <ion-item>
                            <ion-label>No</ion-label>
                            <ion-radio slot="start" color="danger" value="No"></ion-radio>
                        </ion-item>

                    </ion-radio-group>
                  <div class="error-message ion-text-center">{{formError.conacto}}</div>

                  <ion-item>
                        <ion-label position="floating">Teléfono alterno:</ion-label>
                        <ion-input formControlName="telefonoAlterno"></ion-input>
                    </ion-item>
                    <ion-item>
                        <ion-label position="floating">Correo electronico alterno:</ion-label>
                        <ion-input formControlName="correoAlterno"></ion-input>
                    </ion-item>

                    <ion-item>
                      <ion-toggle color="primary" slot="start" formControlName="avisoPrivacidad"></ion-toggle>

                      <ion-label routerLink="/aviso-privacidad"> He leído y acepto el <a>Aviso de Privacidad</a>
                          <ion-text color="danger">*</ion-text>
                        </ion-label>

                    </ion-item>
                  <div class="error-message ion-text-center">{{formError.avisoPrivacidad}}</div>

                </ion-col>

            </ion-row>
            <ion-row>
            </ion-row>
            <div class="ion-padding">
                <ion-button expand="block" class="ion-no-margin" (click)="enviarReporte()"
                            [disabled]="farmacoForm.invalid">Enviar Reporte
                </ion-button>
            </div>
        </form>

      <ion-row style="padding-top: 200px"></ion-row>

    </ion-grid>

</ion-content>

