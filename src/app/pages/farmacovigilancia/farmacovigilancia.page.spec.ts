import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FarmacovigilanciaPage } from './farmacovigilancia.page';

describe('FarmacovigilanciaPage', () => {
  let component: FarmacovigilanciaPage;
  let fixture: ComponentFixture<FarmacovigilanciaPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FarmacovigilanciaPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FarmacovigilanciaPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
