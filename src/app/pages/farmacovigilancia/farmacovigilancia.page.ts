import {Component, OnInit} from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {ImagenDeProducto, RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {Router} from '@angular/router';
import {FormControl, FormGroup, Validators} from '@angular/forms';
// import {FirestoreDbService} from '../../providers/firestore-db.service';
// import {WidgetUtilService} from '../../providers/widget-util.service';
import {FARMACO} from '../../constants/formValidationMessage';
// import {HelperService} from '../../providers/helper.service';
// import { HTTP } from '@ionic-native/http/ngx';




// import { MailService } from '@sendgrid/mail';

// import { Email } from '@teamhive/capacitor-email';

// import '@capacitor-community/http';
// import {Plugins} from '@capacitor/core';
import {environment} from '../../../environments/environment';
// import {FirebaseAnalyticsService} from '../../services/firebase-analitycs.service';
import {HelperService} from "../../services/helper.service";
import {WidgetUtilService} from "../../services/widget-util.service";
import {Preferences} from "@capacitor/preferences";
import {EmailService} from "../../services/email.service";


// const {Storage, Http} = Plugins;

// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
// import { Storage } from '@capacitor/storage';
// import { Browser } from '@capacitor/browser';
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';


@Component({
    selector: 'app-farmacovigilancia',
    templateUrl: './farmacovigilancia.page.html',
    styleUrls: ['./farmacovigilancia.page.scss'],
})
export class FarmacovigilanciaPage implements OnInit {
    SendGridAPI = environment.sendGridAPI;
    fecha = new Date();


    farmacoForm: FormGroup;
    nombrePaciente: FormControl;
    fechaDeNaciemiento: FormControl;
    genero: FormControl;
    peso: FormControl;
    talla: FormControl;
    descripcionDeSospecha: FormControl;
    indicarSiEs: FormControl;
    fechadDeInicio: FormControl;
    fechaDeTermino: FormControl;
    disminuyoLaDosis: FormControl;
    readministroMedicamento: FormControl;
    recuperoDeLaSRAM: FormControl;
    cambioFarmacoterapia: FormControl;
    suspendioMedicamento: FormControl;
    mejoriaAlSuspender: FormControl;
    medicamentoParaTratar: FormControl;
    presentoNuevementeLaSRAM: FormControl;
    nombreComercial: FormControl;
    formaFarmaceutica: FormControl;
    nombreGenerico: FormControl;
    numLote: FormControl;
    viaAdministracion: FormControl;
    caducidad: FormControl;
    paraQueEnfermedad: FormControl;
    dosis: FormControl;
    horario: FormControl;
    otroMedicamento: FormControl;
    antecedentesAlergias: FormControl;
    alergias: FormControl;
    datosRelevantes: FormControl;
    condicionMedica: FormControl;
    conacto: FormControl;
    telefonoAlterno: FormControl;
    correoAlterno: FormControl;
    avisoPrivacidad: FormControl;
    formError: any = {
        nombrePaciente: '',
        fechaDeNaciemiento: '',
        genero: '',
        peso: '',
        talla: '',
        descripcionDeSospecha: '',
        indicarSiEs: '',
        fechadDeInicio: '',
        fechaDeTermino: '',
        disminuyoLaDosis: '',
        readministroMedicamento: '',
        recuperoDeLaSRAM: '',
        cambioFarmacoterapia: '',
        suspendioMedicamento: '',
        mejoriaAlSuspender: '',
        medicamentoParaTratar: '',
        presentoNuevementeLaSRAM: '',
        nombreComercial: '',
        formaFarmaceutica: '',
        nombreGenerico: '',
        numLote: '',
        viaAdministracion: '',
        caducidad: '',
        paraQueEnfermedad: '',
        dosis: '',
        horario: '',
        otroMedicamento: '',
        antecedentesAlergias: '',
        alergias: '',
        datosRelevantes: '',
        condicionMedica: '',
        conacto: '',
        telefonoAlterno: '',
        correoAlterno: '',
        avisoPrivacidad: '',
    };

    imagenesProd: ImagenDeProducto[] = [];
    vademecum: RespuestaStrapi[] = [];

    validationMessage: any = FARMACO;
    showSignupSpinner = false;


    cedulaR: any;
    nombreR: any;
    tituloR: any;
    correoR: any;
    uidR: any;
    apellidoR: any;
    telefonoR: any;

    constructor(
      private helperService: HelperService,
      private strapi: StrapiService,
      private router: Router,
      // private firestoreDbService: FirestoreDbService,
      private widgetUtilService: WidgetUtilService,
      // private http: HTTP,
      // private fbas: FirebaseAnalyticsService
      private email: EmailService

    ) {
    }

    ngOnInit() {
        this.createFormControl();
        this.createForm();
        this.strapi.getContenido('medicamentos')
            .subscribe((resp: any) => {
                // console.log('Vademecum', resp);
                this.vademecum = resp;
                this.imagenesProd = resp.Imagen_de_producto;

                // console.log('Datos API', resp);
            });
    }


    async enviarReporte() {
        try {

          this.showSignupSpinner = true;
          const ret = await Preferences.get({key: 'user'});
          const userInStorage = JSON.parse(ret.value);

          await this.email.enviarReporteFarmacovigilancia(userInStorage, this.farmacoForm.value, '<EMAIL>')

          await this.widgetUtilService.presentToast
            (`¡Reporte exitoso!<br>Muchas gracias ${this.nombreR}
            <br> Su Reporte de farmacovigilancia ha sido enviado satisfactoriamente <br> `);
          this.showSignupSpinner = false;
          this.resetForm();
        } catch (error) {
            console.log(error);
        }


    }


    resetForm() {
        this.farmacoForm.reset();
        this.formError = {
            nombrePaciente: '',
            fechaDeNaciemiento: '',
            genero: '',
            peso: '',
            talla: '',
            descripcionDeSospecha: '',
            indicarSiEs: '',
            fechadDeInicio: '',
            fechaDeTermino: '',
            disminuyoLaDosis: '',
            readministroMedicamento: '',
            recuperoDeLaSRAM: '',
            cambioFarmacoterapia: '',
            suspendioMedicamento: '',
            mejoriaAlSuspender: '',
            medicamentoParaTratar: '',
            presentoNuevementeLaSRAM: '',
            nombreComercial: '',
            formaFarmaceutica: '',
            nombreGenerico: '',
            numLote: '',
            viaAdministracion: '',
            caducidad: '',
            paraQueEnfermedad: '',
            dosis: '',
            horario: '',
            otroMedicamento: '',
            antecedentesAlergias: '',
            alergias: '',
            datosRelevantes: '',
            condicionMedica: '',
            conacto: '',
            telefonoAlterno: '',
            correoAlterno: '',
            avisoPrivacidad: '',

        };
    }

    createFormControl() {
        this.nombrePaciente = new FormControl('', [
            Validators.required
        ]);
        this.fechaDeNaciemiento = new FormControl('', [
            Validators.required
        ]);
        this.genero = new FormControl('', [
            Validators.required
        ]);
        this.peso = new FormControl('', []);


        this.talla = new FormControl('', []);
        this.descripcionDeSospecha = new FormControl('', [
            Validators.required

        ]);
        this.indicarSiEs = new FormControl('', []);
        this.fechadDeInicio = new FormControl('', [
            Validators.required,

        ]);
        this.fechaDeTermino = new FormControl('', [
            Validators.required,

        ]);
        this.disminuyoLaDosis = new FormControl('', [
            Validators.required
        ]);
        this.readministroMedicamento = new FormControl('', [
            Validators.required
        ]);
        this.recuperoDeLaSRAM = new FormControl('', [
            Validators.required
        ]);
        this.cambioFarmacoterapia = new FormControl('', [
            Validators.required
        ]);
        this.suspendioMedicamento = new FormControl('', [
            Validators.required

        ]);
        this.mejoriaAlSuspender = new FormControl('', [
            Validators.required

        ]);
        this.medicamentoParaTratar = new FormControl('', [
            Validators.required
        ]);
        this.presentoNuevementeLaSRAM = new FormControl('', [
            Validators.required
        ]);
        this.nombreComercial = new FormControl('', [
            Validators.required
        ]);
        this.formaFarmaceutica = new FormControl('', []);
        this.nombreGenerico = new FormControl('', [
            Validators.required
        ]);
        this.numLote = new FormControl('', [
            Validators.required
        ]);
        this.viaAdministracion = new FormControl('', [
            Validators.required
        ]);
        this.caducidad = new FormControl('', [
            Validators.required
        ]);
        this.paraQueEnfermedad = new FormControl('', [
            Validators.required
        ]);
        this.dosis = new FormControl('', [
            Validators.required
        ]);
        this.horario = new FormControl('', [
            Validators.required
        ]);
        this.otroMedicamento = new FormControl('', [
            Validators.required

        ]);
        this.antecedentesAlergias = new FormControl('', [
            Validators.required
        ]);
        this.alergias = new FormControl('', []);
        this.datosRelevantes = new FormControl('', [
            Validators.required
        ]);
        this.condicionMedica = new FormControl('', [
            Validators.required
        ]);
        this.conacto = new FormControl('', [
            Validators.required
        ]);
        this.telefonoAlterno = new FormControl('', []);
        this.correoAlterno = new FormControl('', []);
        this.avisoPrivacidad = new FormControl(false, [
            Validators.required,
            Validators.requiredTrue
        ]);


    }

    createForm() {
        this.farmacoForm = new FormGroup({
            nombrePaciente: this.nombrePaciente,
            fechaDeNaciemiento: this.fechaDeNaciemiento,
            genero: this.genero,
            peso: this.peso,


            talla: this.talla,
            descripcionDeSospecha: this.descripcionDeSospecha,
            indicarSiEs: this.indicarSiEs,
            fechadDeInicio: this.fechadDeInicio,
            fechaDeTermino: this.fechaDeTermino,
            disminuyoLaDosis: this.disminuyoLaDosis,
            readministroMedicamento: this.readministroMedicamento,
            recuperoDeLaSRAM: this.recuperoDeLaSRAM,
            cambioFarmacoterapia: this.cambioFarmacoterapia,
            suspendioMedicamento: this.suspendioMedicamento,
            mejoriaAlSuspender: this.mejoriaAlSuspender,
            medicamentoParaTratar: this.medicamentoParaTratar,
            presentoNuevementeLaSRAM: this.presentoNuevementeLaSRAM,
            nombreComercial: this.nombreComercial,
            formaFarmaceutica: this.formaFarmaceutica,
            nombreGenerico: this.nombreGenerico,
            numLote: this.numLote,
            viaAdministracion: this.viaAdministracion,
            caducidad: this.caducidad,
            paraQueEnfermedad: this.paraQueEnfermedad,
            dosis: this.dosis,
            horario: this.horario,
            otroMedicamento: this.otroMedicamento,
            antecedentesAlergias: this.antecedentesAlergias,
            alergias: this.alergias,
            datosRelevantes: this.datosRelevantes,
            condicionMedica: this.condicionMedica,
            conacto: this.conacto,
            telefonoAlterno: this.telefonoAlterno,
            correoAlterno: this.correoAlterno,
            avisoPrivacidad: this.avisoPrivacidad


        });
        this.farmacoForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
    }

    onFormValueChanged(data) {

        this.formError = this.helperService.prepareValidationMessage(this.farmacoForm, this.validationMessage, this.formError);


    }

}
