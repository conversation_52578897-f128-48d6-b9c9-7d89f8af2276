import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GraciasPageRoutingModule } from './gracias-routing.module';

import { GraciasPage } from './gracias.page';
import {NgArrayPipesModule, NgStringPipesModule} from "ngx-pipes";

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        GraciasPageRoutingModule,
        NgArrayPipesModule,
        NgStringPipesModule
    ],
  declarations: [GraciasPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]
})
export class GraciasPageModule {}
