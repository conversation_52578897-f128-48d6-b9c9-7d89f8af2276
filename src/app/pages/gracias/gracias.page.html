<ion-content fullscreen class="ion-padding wob" scroll-y="false" color="primary">


    <swiper-container
      #welcomeSlider
      pagination="true"
      navigation="true"
      class="ion-padding welcome-slides"
      scroll-y="false"
    >
      <!-- 1. Diapositiva de "Gracias por registrarte" -->
      <swiper-slide>
        <div class="welcome-content">
          <h2>¡Gracias por registrarte!</h2>
          <p>Te invitamos a descargar la aplicación en tu dispositivo:</p>

          <div style="margin-top: 20px;">
            <ion-button
              shape="round"
              color="tertiary"
              fill="solid"
              href="https://apps.apple.com/mx/app/sanfer-conecta/id1519372001"
              target="_blank"
            >
              <ion-icon slot="start" name="logo-apple"></ion-icon>
              Descargar iOS
            </ion-button>

            <ion-button
              shape="round"
              color="tertiary"
              fill="solid"
              href="https://play.google.com/store/apps/details?id=com.sanferconecta.app&hl=es"
              target="_blank"
            >
              <ion-icon slot="start" name="logo-google-playstore"></ion-icon>
              Descargar Android
            </ion-button>
          </div>

          <p style="margin-top: 20px;">
            Desliza a la izquierda para continuar
          </p>
          <!-- Mensaje para indicar al usuario que debe desplazarse / navegar -->
          <div style="margin-top: 20px; display: flex; align-items: center; justify-content: center;">
            <ion-icon name="arrow-back-outline" style="margin-right: 8px;"></ion-icon>
            <span>Desliza a la izquierda para continuar <br>o da click en las flechas</span>
          </div>
        </div>
      </swiper-slide>



    <!-- Diapositivas existentes -->
    <swiper-slide *ngFor="let ws of welcomeSlides | orderBy: 'id'">
      <div class="welcome-content">
        <p></p>
        <ion-img style="width: 200px;" src="{{strapiUrl}}{{ws.Imagen.url}}"></ion-img>
        <h2>{{ws.Titulo}}</h2>
        <p style="margin-bottom: 10px;">{{ws.Descripcion}}</p>
        <div class="container" *ngIf="ws.NombreBoton" style="margin-top: 0;">
          <ion-spinner name="lines-sharp" slot="start" *ngIf="showSpinner"></ion-spinner>

          <p style="margin-top: 0;" *ngIf="showSpinner">
            personalizando contenido para {{titulo | ucwords}}
          </p>
          <p style="margin-top: 0;" *ngIf="!showSpinner">
            Considerando tu especialidad médica, hemos adaptado este material específicamente para {{titulo | ucwords}}
          </p>

          <ion-chip>
            Usuario
            <ion-icon *ngIf="userVerified" name="checkmark-circle" color="success"></ion-icon>
            <ion-icon *ngIf="!userVerified" name="checkmark-circle-outline"></ion-icon>
          </ion-chip>
          <ion-chip>
            Vademecum
            <ion-icon *ngIf="vademecumVerified" name="checkmark-circle" color="success"></ion-icon>
            <ion-icon *ngIf="!vademecumVerified" name="checkmark-circle-outline"></ion-icon>
          </ion-chip>
          <ion-chip>
            Modelos 3D
            <ion-icon *ngIf="modelsVerified" name="checkmark-circle" color="success"></ion-icon>
            <ion-icon *ngIf="!modelsVerified" name="checkmark-circle-outline"></ion-icon>
          </ion-chip>
          <ion-chip>
            Cursos
            <ion-icon *ngIf="academyVerified" name="checkmark-circle" color="success"></ion-icon>
            <ion-icon *ngIf="!academyVerified" name="checkmark-circle-outline"></ion-icon>
          </ion-chip>

          <p></p>
          <ion-button
            [disabled]="isButtonDisabled()"
            color="tertiary"
            expand="full"
            shape="round"
            (click)="removeWelcome()"
          >
            {{ws.NombreBoton}}
            <ion-icon slot="end" name="{{ws.IconoBoton}}"></ion-icon>
          </ion-button>
        </div>

        <p></p>
      </div>
    </swiper-slide>
  </swiper-container>



  <ion-modal  #modalEspecialidades
              [initialBreakpoint]="0.5"
              [breakpoints]="[0.5, 1]"
              backdrop-dismiss="false">
    <ng-template>
      <ion-content color="primary">
        <ion-searchbar #searchBar color="light" placeholder="Buscar especialidad sanfer" (click)="modalEspecialidades.setCurrentBreakpoint(1)" (ionInput)="buscar( $event )"></ion-searchbar>
        <p class="ion-text-center">
          Bienvenido {{userInStorage.nombre}} {{userInStorage.apellido}}, para continuar selecciona la categoria sanfer que deseas visualizar
        </p>
        <ion-list>
          <ion-item button="true" *ngFor="let especialidad of especialidadesSanfer | filterBy: ['categoria']: textoBuscar" (click)="loadCategory(especialidad)">
            <ion-icon name="apps-outline" color="primary" slot="start"></ion-icon>
            <ion-label>
              <h2>{{especialidad.categoria}}</h2>
            </ion-label>
            <ion-icon name="chevron-forward-circle-outline" slot="end" color="primary"></ion-icon>
          </ion-item>

        </ion-list>
        <p style="height: 200px"></p>
      </ion-content>
    </ng-template>
  </ion-modal>

  <ion-modal  #modalContentLoading [initialBreakpoint]="0.25" backdrop-dismiss="false">
    <ng-template>
      <ion-content color="primary" class="ion-padding ion-text-center">

        <p style="margin-top: 0;">Estamos cargando la información de la especialidad médica, que selecciionaste: {{especialidadSeleccionada}}
        </p>
        <ion-chip>
          Usuario
          <ion-icon *ngIf="userVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!userVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Vademecum
          <ion-icon *ngIf="vademecumVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!vademecumVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Modelos 3D
          <ion-icon *ngIf="modelsVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!modelsVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <!--        <ion-chip>-->
        <!--          Noticias-->
        <!--          <ion-icon *ngIf="newsVerified" name="checkmark-circle" color="success" ></ion-icon>-->
        <!--          <ion-icon *ngIf="!newsVerified" name="checkmark-circle-outline"></ion-icon>-->
        <!--        </ion-chip>-->
        <ion-chip>
          Cursos
          <ion-icon *ngIf="academyVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!academyVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>

        <p></p>
        <p style="height: 200px"></p>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
