
.welcome-slides {
  --swiper-pagination-color: #fff;
  width: 100%;

  height: auto; /* Altura base del slider */
  position: relative;
}

.welcome-content {
  display: flex;
  flex-direction: column; /* Coloca los elementos de forma vertical */
  justify-content: center; /* Centrado vertical */
  align-items: center; /* Centrado horizontal */
  text-align: center; /* Asegura que el texto esté centrado */
  width: 100%;
  height: 700px;
}




.welcome-slides {
  --swiper-navigation-color: #fff; /* Color de las flechas (blanco) */
  --swiper-navigation-size: 54px; /* Tamaño de las flechas */
  --swiper-navigation-background-color: rgba(0, 0, 0, 0.5); /* Fondo semi-transparente */
  --swiper-navigation-border-radius: 50%; /* Fondo circular */
  --swiper-navigation-transition: background-color 0.3s ease; /* Transición suave */
}

/* Cambia el color de fondo al pasar el mouse */
.welcome-slides .swiper-button-next:hover,
.welcome-slides .swiper-button-prev:hover {
  --swiper-navigation-background-color: rgba(0, 0, 0, 0.8); /* Fondo más oscuro al pasar el mouse */
}


/* Ajustes para pantallas de 1440x900 o menos */
@media (max-width: 1440px) and (max-height: 900px) {
  .welcome-slides {
    max-width: 90%; /* Reduce el ancho del slider */
    margin: 0 auto; /* Centra el slider */
  }

  .welcome-slides .welcome-content {
    padding: 20px; /* Añade espacio interno */
  }

  .welcome-slides h2 {
    font-size: 1.5rem; /* Reduce el tamaño del título */
  }

  .welcome-slides p {
    font-size: 0.9rem; /* Reduce el tamaño del texto */
  }

  .welcome-slides .swiper-pagination {
    bottom: 10px; /* Ajusta la posición de los bullets */
  }

  .welcome-slides .swiper-button-next,
  .welcome-slides .swiper-button-prev {
    width: 30px; /* Reduce el tamaño de las flechas */
    height: 30px;
  }

  .welcome-slides .swiper-button-next svg,
  .welcome-slides .swiper-button-prev svg {
    width: 20px; /* Reduce el tamaño del SVG */
    height: 20px;
  }
}
