import {Component, OnInit, ViewChild} from '@angular/core';
import {<PERSON>ert<PERSON>ontroller, IonModal} from "@ionic/angular";
import {environment} from "../../../environments/environment";
import {Router} from "@angular/router";
import {ContentService} from "../../services/content.service";
import {WidgetUtilService} from "../../services/widget-util.service";
import {AnalyticsService} from "../../services/analytics.service";
import {Preferences} from "@capacitor/preferences";

@Component({
  selector: 'app-gracias',
  templateUrl: './gracias.page.html',
  styleUrls: ['./gracias.page.scss'],
})
export class GraciasPage implements OnInit {


  @ViewChild('modalEspecialidades') modalEspecialidades: IonModal;
  @ViewChild('modalContentLoading') modalContentLoading: IonModal;

  // Campos de busqueda
  searchInProgress: boolean = false;
  textoBuscar: string = '';


  // Solo para empleados
  especialidadSeleccionada: string;


  welcomeSlides: any = [];
  strapiUrl: string = environment.strapiURL;
  firstTime: boolean = true;
  userInStorage: any;

  showSpinner: boolean = false;

  titulo: string

  userVerified: boolean = false;
  newsVerified: boolean = false;
  academyVerified: boolean = false;
  vademecumVerified: boolean = false;
  modelsVerified: boolean = false;

  especialidadesSanfer: any;




  constructor(
    private router: Router,
    private content: ContentService,
    private widgetUtilService: WidgetUtilService,
    private alertController: AlertController,
    private analyticsService: AnalyticsService,



  ) { }

  async ngOnInit() {
    try {
      this.welcomeSlides = await this.content.getWelcomeSlides(50);
      await this.getUserData();

    } catch (error) {
      console.error('Error during initialization:', error);
    }
    await this.analyticsService.setCurrentScreen('Pantalla de bienvenida');


  }



  async redirectToOriginalUrl() {
    const { value: returnUrl } = await Preferences.get({ key: 'returnUrl' });
    if (returnUrl) {
      await Preferences.remove({ key: 'returnUrl' }); // Eliminar la URL almacenada después de redirigir
      await this.router.navigateByUrl(returnUrl);
    } else {
      await this.router.navigate(['/inicio']);
    }
  }


  async verifyRole(userInStorage: any) {
    console.log('Role del Usuario: ', userInStorage.role);
    switch (userInStorage.role) {
      case undefined:
        // this.oldUser(userInStorage);
        break;

      case null:
        // this.oldUser(userInStorage);
        break;
      case 'noMedico':
        // this.noMedico(userInStorage);
        break;
      case 'medico':
        this.titulo = userInStorage.titulo
        await this.analyticsService.logEvent('titulo_profesional', {titulo_profesional: this.titulo});
        await this.verifyMedicoData(userInStorage);
        break;
      case 'representante':
        await this.seleccionarEspecialidad()
        break;
      case 'marketing':
        await this.seleccionarEspecialidad()
        break;
      case 'capacitacion':
        await this.seleccionarEspecialidad()
        break;
      case 'productividad':
        await this.seleccionarEspecialidad()
        break;
      case 'administrador':
        await this.seleccionarEspecialidad()
        break;


    }
  }

  async getUserData() {

    try {

      const storageRawData: any = await Preferences.get({ key: 'user' });
      this.userInStorage = JSON.parse(storageRawData.value);
      console.log('UsuarioEn el Storage: ', this.userInStorage)
      await this.verifyRole(this.userInStorage);

    } catch (error) {
      console.error('Error during initialization:', error);
    }
  }

  async seleccionarEspecialidad() {
    await this.widgetUtilService.presentLoadingMessage('Detectamos que eres personal de sanfer, estamos cargando la selección de especialidades, esto puede tardar unos minutos y este proceso solo se realizará una vez...')
    this.especialidadesSanfer = await this.content.getCategory(500);
    await this.widgetUtilService.dismissLoader();
    // console.log('Especialidad Sanfer: ', this.especialidadesSanfer)
    await this.modalEspecialidades.present()


  }
  async verifySanferEmployeeData(especialidad: any, categoryId: string) {
    this.showSpinner = true;
    console.log('Especialidad en Welcome', especialidad)
    await Preferences.set({
      key: 'especialidadBanner',
      value: JSON.stringify(especialidad)
    });
    try {
      const user = await this.content.getSanferEmployeeData(categoryId);
      this.verifyAndLog('user', user);
    } catch (error) {
      console.error('Error al cargar el usuario:', error);
    }

    try {
      const vademecum = await this.content.getVademecum(350);
      this.verifyAndLog('vademecum', vademecum);
    } catch (error) {
      console.error('Error al cargar el vademecum:', error);
    }

    try {
      const models = await this.content.get3dModels(300);
      this.verifyAndLog('models', models);
    } catch (error) {
      console.error('Error al cargar los modelos 3D:', error);
    }

    try {
      const academy = await this.content.getAcademy(300);
      this.verifyAndLog('academy', academy);
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        console.warn('No se pudo cargar la academia debido a que se excedió la cuota de almacenamiento. Continuando sin almacenar academia.');
        this.academyVerified = true; // Marcar como verificado para habilitar el botón

      } else {
        console.error('Error al cargar la academia:', error);
      }
    }

    this.showSpinner = false;
    // await this.widgetUtilService.dismissLoader();
  }

  async verifyMedicoData(userInStorage: any) {
    this.showSpinner = true;
    console.log('Usuario en el storage verify', userInStorage);

    try {
      const user = await this.content.getUser(userInStorage);
      this.verifyAndLog('user', user);
    } catch (error) {
      console.error('Error al cargar el usuario:', error);
    }

    try {
      const vademecum = await this.content.getVademecum(350);
      this.verifyAndLog('vademecum', vademecum);
    } catch (error) {
      console.error('Error al cargar el vademecum:', error);
    }

    try {
      const models = await this.content.get3dModels(300);
      this.verifyAndLog('models', models);
    } catch (error) {
      console.error('Error al cargar los modelos 3D:', error);
    }

    try {
      const academy = await this.content.getAcademy(300);
      this.verifyAndLog('academy', academy);
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        console.warn('No se pudo cargar la academia debido a que se excedió la cuota de almacenamiento. Continuando sin almacenar academia.');
        this.academyVerified = true; // Marcar como verificado para habilitar el botón

      } else {
        console.error('Error al cargar la academia:', error);
      }
    }

    this.showSpinner = false;
  }

  verifyAndLog(key, value) {
    if (this.isValid(value)) {
      console.log(`${key} tiene un valor válido.`);
      this[`${key}Verified`] = true;
    } else {
      console.log(`${key} es null, undefined, o vacía.`);
    }
  }

  isValid(value) {
    return value !== null && value !== undefined && value !== '';
  }

  isButtonDisabled(): boolean {
    return !(
      this.vademecumVerified &&
      this.modelsVerified &&
      this.userVerified &&
      this.academyVerified
    );
  }

  async removeWelcome() {
    // this.firstTime = false;
    // await Preferences.set({
    //   key: 'firstTime',
    //   value: JSON.stringify({
    //     firstTime: false
    //   })
    // });
    // await this.router.navigate(['/inicio'], { replaceUrl: true });
    // console.log('First Time: ', this.firstTime);

    // Redirigir al usuario a la URL original después de cargar la información
    await this.redirectToOriginalUrl();




  }


  buscar( event: any ) {
    if(event.detail.value == '' || null) {
      console.log('busqueda no realizada')
      this.searchInProgress = false
    } else {
      this.searchInProgress = true
      console.log(event, 'Busqueda iniciada searchInProgress', this.searchInProgress);
      this.textoBuscar = event.detail.value;
    }

  }

  async loadCategory(especialidad: any) {
    console.log(especialidad);

    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: especialidad.categoria,
      // subHeader: `${email}`,
      message: `Estas por seleccionar ${especialidad.categoria}, la informacion de esta categoria sera cargada en tu perfil `,
      buttons: [
        {
          text: 'Seleecionar otra',
          handler: async () => {
            // console.log('Recuperar contraseña');
            await this.alertController.dismiss()

          }
        },
        {
          text: 'Cargar información',
          handler: async () => {
            console.log('verifySanfer: ', especialidad.id);
            this.especialidadSeleccionada = especialidad.categoria

            await this.analyticsService.logEvent('especialidad_sanfer_representante', { especialidad_sanfer: this.especialidadSeleccionada });

            await this.alertController.dismiss()
            await this.modalEspecialidades.dismiss()
            await this.modalContentLoading.present();
            await this.verifySanferEmployeeData(especialidad, especialidad.id);
            await this.modalContentLoading.dismiss();


            // Redirigir al usuario a la URL original después de cargar la información
            await this.redirectToOriginalUrl();
            // await this.router.navigate(['/inicio'], { replaceUrl: true });




          }
        }
      ]
    });
    await alert.present();

    // const { role } = await alert.onDidDismiss();


  }
}
