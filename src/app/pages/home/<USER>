<ion-header class="no-border" >
  <ion-toolbar color="primary">
    <ion-buttons slot="end">
      <ion-button expand="block" size="small" color="light" routerLink="/registrar-personal"> <ion-icon name="log-in" slot="end"></ion-icon></ion-button>
    </ion-buttons>
    <ion-buttons slot="end">


    </ion-buttons>
    <ion-title> Sanfer conecta
    </ion-title>

  </ion-toolbar>

</ion-header>
<ion-content>
  <ion-grid>
    <ion-row class="ion-justify-content-center" >
      <ion-col size="12" size-xs="12" size-sm="12" size-md="12" size-lg="12" size-xl="12" class="centrar_imagen ion-padding-bottom ion-padding-top">

        <img class="centrar_imagen"  style="width: 30%; max-width: 250px; padding-top: 20px"  src="/assets/sanfer_conecta_oficial.svg"/>

      </ion-col>
      <ion-col size="12" size-lg="10" size-md="8">
        <ion-button class="ion-margin-top"  expand="block" size="large" color="primary" routerLink="/login"  shape="round"> <ion-icon name="person-add" slot="start"></ion-icon>Ingresar</ion-button>

        <ion-button class="ion-margin-top" *ngIf="signupRespaldo === false"  expand="block" size="small" color="secondary" routerLink="/signup"  shape="round"> <ion-icon name="person-add" slot="start"></ion-icon> Registrame</ion-button>
        <ion-button class="ion-margin-top" *ngIf="signupRespaldo" expand="block" size="small" color="secondary" routerLink="/signup-respaldo" shape="round"> <ion-icon name="person-add" slot="start"></ion-icon> Registrame</ion-button>
      </ion-col>
    </ion-row>





    <swiper-container style="max-width: 800px">
      <swiper-slide *ngFor="let slide of slidesPublic">
        <ion-img style="margin-bottom: 40px" [routerLink]="slide.caption" src="{{strapiUrl}}{{slide.url}}"></ion-img>
      </swiper-slide>
    </swiper-container>


  </ion-grid>
</ion-content>






<ion-footer class="no-border" >
  <ion-toolbar color="primary">
    <p class="ion-text-center">
      Aviso de publicidad: 213300202C2720
    </p>

  </ion-toolbar>
</ion-footer>
