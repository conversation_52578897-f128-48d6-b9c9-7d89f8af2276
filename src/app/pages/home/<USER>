import {AfterViewInit, Component, ElementRef, OnInit, ViewChild} from '@angular/core';


import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';

import { SplashScreen } from '@capacitor/splash-screen';
import {SwiperOptions} from "swiper/types";
import {SwiperContainer} from "swiper/swiper-element";
import {ContentService} from "../../services/content.service";
import {FirebaseMessaging, GetTokenOptions} from "@capacitor-firebase/messaging";
import {Capacitor} from "@capacitor/core";
import {AnalyticsService} from "../../services/analytics.service";


@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})


export class HomePage implements OnInit{
  @ViewChild('swiper') swiper!: ElementRef<SwiperContainer>;

  isLoggedIn = false;

  slidesPublic: any = [];

  strapiUrl: string = environment.strapiURL;

  activated = false;
  activatedPublic = false;
  signupRespaldo = false;


  constructor(
    private strapi: StrapiService,
    private content: ContentService,
    private analyticsService: AnalyticsService

  ) {
    // FirebaseMessaging.addListener("notificationReceived", (event) => {
    //   console.log("notificationReceived: ", { event });
    // });
    // FirebaseMessaging.addListener("notificationActionPerformed", (event) => {
    //   console.log("notificationActionPerformed: ", { event });
    // });
    // firebase.analytics().setCurrentScreen('home');
  }



  async ngOnInit() {
    // SplashScreen.show({
    //   // showDuration: 1000,
    //
    //   autoHide: true
    // });

    await this.content.getWelcomeSlides(50);

    this.strapi.getContenido('slides/4').subscribe(resp => {
      this.activatedPublic = resp.Activado;
      this.slidesPublic = resp.Slides;
      // console.log('Sliders en el inicio', this.slidesPublic)

    });

    this.strapi.getContenido('controles')
      .subscribe(resp => {
        // console.log('Controles', resp);
        this.signupRespaldo = resp.Signup;
        // this.imagenesProd = resp.Imagen_de_producto;

        // console.log('Datos API', resp);
      });

    await this.analyticsService.setCurrentScreen('Home page')




  }



  index = 0;


  slideChange(swiper: any) {
    this.index = swiper.detail[0].activeIndex;
  }


}


