<ion-header>
  <ion-toolbar color="primary">
    <ion-title ><PERSON> <PERSON><PERSON></ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/profile"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div *ngIf="actividades.length > 0; else sinActividades">
    <ion-list>

      <ion-item-sliding  *ngFor="let actividad of actividades" (ionDrag)="logDrag($event, actividad)">
        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'curso'" (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="strapiUrl + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3 >
              {{ actividad.data.titulo }}
            </h3 >
            <p>Visitaste el {{ actividad.data.type }} el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}</p>
<!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>


        </ion-item>
        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'springer'"  (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="'../..' + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3 >
              <!--              {{ actividad.data.image }}-->

              Realizaste la busqueda de {{ actividad.data.titulo }}
            </h3 >
            <p>en {{ actividad.data.type }} el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}</p>
            <!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>


        </ion-item>

        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'springer-article'"  (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="'../..' + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3 >
<!--              {{ actividad.data.image }}-->

              {{ actividad.data.titulo }}
            </h3 >
            <p>Consultaste el articulo el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}</p>
            <!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>


        </ion-item>

        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'modelo-3d'"  (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="strapiUrl + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3 >
              <!--              {{ actividad.data.image }}-->

              {{ actividad.data.titulo }} de la categoría: {{ actividad.data.categoria }}
            </h3 >
            <p>Visitaste el {{ actividad.data.type }} el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}</p>
            <!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>




        </ion-item>

        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'noticia'"  (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="strapiUrl + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3 >
             {{ actividad.data.titulo }}
            </h3 >
            <p>Visitaste la {{ actividad.data.type }} de la categoria: {{actividad.data.categoria}} </p>
            <p>
              el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}
            </p>
            <!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>




        </ion-item>

        <ion-item [button]="true" detail="true" *ngIf="actividad.data.type === 'medicamento'"  (click)="openBrowser(actividad.url)">
          <ion-avatar  aria-hidden="true" slot="start">
            <img *ngIf="actividad.data" alt="" [src]="strapiUrl + actividad.data.imagen" />
          </ion-avatar>
          <ion-label >
            <h3>
              {{ actividad.data.titulo }} - {{ actividad.data.aditionalData }}
            </h3 >
            <p>Visitaste el {{ actividad.data.type }} el  {{ actividad.timestamp.seconds * 1000 | date: 'medium'}}</p>
            <!--            <p>{{ actividad.timestamp.seconds * 1000 | date}}</p>-->


          </ion-label>




        </ion-item>

        <ion-item-options slot="end">
          <ion-item-option color="tertiary">
            <ion-icon slot="icon-only" name="share"></ion-icon>
          </ion-item-option>
          <ion-item-option color="danger" expandable="true" (click)="delete(actividad.id)" >
            <ion-icon slot="icon-only" name="trash"></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>

    </ion-list>
  </div>
  <ng-template #sinActividades>
    <p>No hay actividades registradas.</p>
  </ng-template>



<!--  <ion-modal-->
<!--    #lessonModal-->
<!--    [isOpen]="false"-->
<!--    [initialBreakpoint]="0.5"-->
<!--    [breakpoints]="[0.5, 0.75]"-->
<!--    [backdropDismiss]="true"-->
<!--    [backdropBreakpoint]="0.25"-->

<!--  >-->
<!--    <ng-template>-->
<!--      <ion-content class="ion-padding" *ngIf="lecciones !== null  &&  lecciones !== undefined">-->
<!--        <ion-list>-->
<!--          <ion-item *ngFor="let leccion of lecciones" >-->
<!--            <ion-avatar slot="start">-->
<!--              <img src="../../assets/icon/play_btn.svg" >-->
<!--            </ion-avatar>-->
<!--            <ion-label>-->
<!--              <h2>{{ leccion.Clase }}</h2>-->
<!--              <p>{{ leccion.type }}</p>-->
<!--              <p>{{ leccion.timestamp.seconds * 1000 | date: 'medium'}}</p>-->

<!--            </ion-label>-->
<!--          </ion-item>-->

<!--        </ion-list>-->


<!--      </ion-content>-->
<!--    </ng-template>-->
<!--  </ion-modal>-->
</ion-content>
