import {Component, OnInit, ViewChild} from '@angular/core';
import {InteractionService} from "../../../services/interaction.service";
import {Preferences} from "@capacitor/preferences";
import {environment} from "../../../../environments/environment";
import {IonModal} from "@ionic/angular";
import {Subject, takeUntil} from "rxjs";
import {WidgetUtilService} from "../../../services/widget-util.service";
import {Router} from "@angular/router";
import {BrowserService} from "../../../services/browser.service";
import {AnalyticsService} from "../../../services/analytics.service";

@Component({
  selector: 'app-historial',
  templateUrl: 'historial.page.html',
  styleUrls: ['historial.page.scss']
})
export class HistorialPage implements OnInit {
  @ViewChild('lessonModal') lessonModal: IonModal;
  private destroy$ = new Subject<void>();


  // Variable para controlar si ya se solicitó la acción de borrar
  private borrarSolicitado = new Set<string>();

  actividades: any[] = [];
  lecciones: any[] = [];
  // uuidUsuario: string = 'uuid-del-usuario'; // Asegúrate de tener el UUID del usuario
  uuid: string;
  strapiUrl: string = environment.strapiURL;
  constructor(
    private interactionService: InteractionService,
    private wus: WidgetUtilService,
    private router: Router,
    private browser: BrowserService,
    private analyticsService: AnalyticsService,

  ) { }

  async ngOnInit() {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    this.uuid = userInStorage.uid
    this.cargarActividades(userInStorage);
    await this.analyticsService.setCurrentScreen('Historial')

  }

  cargarActividades(userInStorage: any): void {
    this.interactionService.getActividadesUsuario(userInStorage.uid)
      .pipe(takeUntil(this.destroy$))
      .subscribe(actividades => {
        this.actividades = actividades;
        console.log(actividades);
      });
  }
  // async openLessonModal(idActividad: string) {
  //   this.interactionService.getLeccionesDeActividad(this.uuid, idActividad)
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe(async lecciones => {
  //       this.lecciones = lecciones;
  //       if (this.lecciones && this.lecciones.length > 0) {
  //         // El arreglo no está vacío, realiza tus operaciones aquí
  //         await this.lessonModal.present();
  //
  //       } else {
  //         // El arreglo está vacío
  //         this.wus.presentAlertConfirm('Sin interacciones', 'No se encontro interacion dentro del curso', ['entendido'])
  //       }
  //
  //
  //     });
  //   // console.log('Lecciones: ', this.lecciones);
  //
  // }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  async openBrowser(url: string) {
    await this.browser.openBrowser(url);
  }

  openCourse(actividad: any) {
    console.log(actividad)

    this.router.navigateByUrl(`/academia/${actividad.categoria}/${actividad.slug}`)

  }

  async delete(id: string) {
    await this.interactionService.borrarActividad(this.uuid, id)

  }

  logDrag(ev, actividad) {
    ev.target.getSlidingRatio().then(async ratio => {
      // console.log('Ratio' , ratio);

      // Detecta un deslizamiento completo hacia la izquierda
      if (ratio >= 1.9 && !this.borrarSolicitado.has(actividad.id)) {
        this.borrarSolicitado.add(actividad.id); // Marca esta actividad como solicitada para borrar
        console.log('Borrar artículo' , actividad.id);
        await this.delete(actividad.id)
      }
    });
  }

}
