import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { InicioPage } from './inicio.page';
// import {GenerarCodigoPageModule} from '../generar-codigo/generar-codigo.module';
// import {GenerarCodigoPage} from '../generar-codigo/generar-codigo.page';
// import {GenerarCodigoRepresentantePageModule} from '../generar-codigo-representante/generar-codigo-representante.module';
import {CompletarRegistroPageModule} from '../completar-registro/completar-registro.module';
// import {SwiperModule} from 'swiper/angular';
import {NgArrayPipesModule} from 'ngx-pipes';
import {QRCodeModule} from "angularx-qrcode";
import { YouTubePlayerModule} from "@angular/youtube-player";
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";
// import {QRCodeModule} from "angularx-qrcode";

const routes: Routes = [
  {
    path: '',
    component: InicioPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    // GenerarCodigoPageModule,
    // GenerarCodigoRepresentantePageModule,
    CompletarRegistroPageModule,
    QRCodeModule,
    NgArrayPipesModule,
    SharedComponentsModule,
    // YouTubePlayerModule,
  ],
  declarations: [InicioPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]
})
export class InicioPageModule {}
