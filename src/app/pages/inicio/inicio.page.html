<ion-menu  side="end" menuId="employeeMenu" contentId="menuContent" *ngIf="role !== 'medico'">
  <ion-header>
    <ion-toolbar color="primary">
      <ion-title>{{ role }}</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-content *ngIf="role === 'representante'">
    <ion-list>
      <ion-item routerLink="/muestra-medica" (click)="closeEmployeeMenu()" >
        <ion-icon slot="start" name="medkit-outline" color="primary"></ion-icon>
        <ion-label class="ion-text-wrap">
          <h3>
            Administrar eventos
          </h3>
        </ion-label>
      </ion-item>

      <ion-item routerLink="/login-gerente" (click)="closeEmployeeMenu()"  *ngIf="isAdmin === true">
        <ion-icon slot="start" color="primary" name="log-in-outline" ></ion-icon>
        <ion-label class="ion-text-wrap">
          <h3>
            Ingresar como Gerente
          </h3>
        </ion-label>
      </ion-item>
      <ion-item button="true" (click)="seleccionarEspecialidad()">
        <ion-icon slot="start" color="primary"  name="swap-horizontal-outline"></ion-icon>
        <ion-label class="ion-text-wrap" >
          <h3>
          Cambia de especialidad
          </h3>
        </ion-label>
      </ion-item>

    </ion-list>

  </ion-content>
</ion-menu>
<ion-router-outlet id="menuContent"></ion-router-outlet>


<ion-header class="no-border" >
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-menu-button color="light"></ion-menu-button>
      <ion-button *ngIf="role === 'representante'" slot="end" (click)="actualizarContenidoRepresentante()" > <ion-icon name="reload-circle" size="large"></ion-icon></ion-button>

    </ion-buttons>
    <ion-buttons slot="end">
<!--      <ion-button (click)="getToken()"><ion-icon name="person"></ion-icon></ion-button>-->
      <ion-button *ngIf="isNativePlatform" slot="end"  (click)="verificarNotificaciones($event)">
        <ion-badge color="secondary" *ngIf="notifications.length > 0">{{ notifications.length }}</ion-badge>
        <ion-icon name="notifications" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>

    <ion-buttons slot="end" >
<!--      <ion-button *ngIf="role === 'medico'" slot="end" (click)="homeRefresh($event)" > <ion-icon name="reload-circle" size="large"></ion-icon></ion-button>-->

      <ion-button *ngIf="isLoggedIn" slot="end"  (click)="openQRModal()"> <ion-icon name="qr-code" size="large"></ion-icon></ion-button>
    </ion-buttons>
    <ion-buttons slot="end" *ngIf="role === 'representante'">


      <ion-button *ngIf="isLoggedIn" slot="end"  (click)="openEmployeeMenu()" > <ion-icon name="log-in-outline" size="large"></ion-icon></ion-button>
    </ion-buttons>

    <ion-title><ion-img style="width:100px; margin: auto" src="assets/logo_sanfer_conecta_blanco.svg"></ion-img></ion-title>




  </ion-toolbar>


</ion-header>

<!--<ion-fab #vistaM  vertical="bottom" horizontal="end" slot="fixed" >-->
<!--  <ion-button shape="round" (click)="solicitarVisita()">Solicitar Visita <ion-icon slot="end" name="medkit"></ion-icon>-->
<!--  </ion-button>-->
<!--</ion-fab>-->
<ion-button *ngIf="isLoggedIn && role === 'noMedico'" (click)="completeRegistration('noMedico')">Validar cedula</ion-button>


<ion-content>

  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="homeRefresh($event)"  *ngIf="role === 'medico'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>

  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="actualizarContenidoRepresentanteRefresh($event)"  *ngIf="role === 'representante'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>


  <ion-grid *ngIf="isLoggedIn" class="ion-no-padding" [fixed]="true">

<!--    <ion-row *ngIf="role === 'representante'">-->
<!--      <ion-col>-->
<!--        <ion-button color="secondary" (click)="openEmployeeMenu()" expand="block"><ion-icon slot="end" name="log-in-outline"></ion-icon> Menu de representante</ion-button>-->
<!--      </ion-col>-->
<!--    </ion-row>-->




    <ion-row *ngIf="role === 'productividad'">
      <ion-col>
        <ion-button color="secondary" routerLink="/productividad" expand="block"><ion-icon slot="end" name="log-in-outline"></ion-icon> Menu de productividad</ion-button>
      </ion-col>
    </ion-row>





    <ion-row >
      <ion-col >



        <div class="swiperHome swiperStyleSanfer">
          <div class="swiper-wrapper" >
            <div class="swiper-slide" style="padding-bottom: 20px" *ngFor="let slide of slides | orderBy: '-updated_at'">
              <!-- Para imágenes -->
              <ion-img *ngIf="slide.type === 'image'" [src]="strapiUrl + slide.image.url" (click)="openBrowser(slide.link)"></ion-img>
              <app-youtube-player *ngIf="slide.type === 'video'" [videoId]="slide.videoURL" [autoplay]="false"></app-youtube-player>

<!--              <img *ngIf="slide.type === 'photo'" [src]="slide.imageUrl" />-->

              <!-- Para videos -->
<!--              <div *ngIf="slide.type === 'video'" [id]="'sliderVideoPlayer' + slide.id"></div>-->
            </div>
          </div>
          <div class="swiper-pagination"></div>
          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>
        </div>
      </ion-col>


    </ion-row>

    <ion-row >
      <ion-col >



        <div class="ion-padding no_padding_top no_padding_bottom separadores">
          Herramientas <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>




        <swiper-container   #swipermenu  slides-per-view="4.5" slides-offset-before="15" slides-offset-after="15" class="ion-no-padding ion-no-margin swiperStyleSanfer" >

          <swiper-slide routerLink="/academia" >
            <ion-img class="centrar_imagen" style="width: 80%; padding-top: 10%" src="../assets/icon/educacion.svg"></ion-img>
            <h1 style="text-align: center">Academia Sanfer</h1>
          </swiper-slide>
          <swiper-slide routerLink="/modelos-anatomicos" >
            <ion-img class="centrar_imagen" style="width: 80%; padding-top: 10%" src="../assets/icon/modelosAnatomicos.svg"></ion-img>
            <h1 style="text-align: center">Modelos Anatómicos</h1>
          </swiper-slide>
          <swiper-slide routerLink="/vademecum" >
            <ion-img class="centrar_imagen" style="width: 80% ;padding-top: 7%" src="../assets/icon/vademecum.svg"></ion-img>
            <h1 style="text-align: center">Vademécum Sanfer</h1>
          </swiper-slide>
          <swiper-slide routerLink="/noticias" >
            <ion-img class="centrar_imagen " style="width: 80%; padding-top: 10%" src="../assets/icon/noticias.svg"></ion-img>
            <h1 style="text-align: center">Noticias</h1>
          </swiper-slide>

          <swiper-slide (click)="openBrowser('https://sanfer.com.mx/farmacovigilancia/reportar-medicamento/ps')" >
            <ion-img class="centrar_imagen " style="width: 80%; padding-top: 10%" src="../assets/icon/farmacovigilancia_1.svg"></ion-img>
            <h1 style="text-align: center">Farmaco vigilancia</h1>
          </swiper-slide>


        </swiper-container>


      </ion-col>


    </ion-row>

    <ion-row class="no_padding_top no_padding_top">
      <ion-col class="hiddenCol" size="12"  size-lg="2" size-md="1" size-xs="1">

      </ion-col>
      <ion-col size="12" size-xl="8" size-lg="8" size-md="8" size-xs="12">

        <div class="ion-padding separadores" (click)="homeRefresh($event)" *ngIf="role === 'medico'">
          <span class="publicaciones icon-with-text"></span> Publicaciones <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>

        <div class="ion-padding separadores" (click)="actualizarContenidoRepresentante()" *ngIf="role === 'representante'">
          <span class="publicaciones icon-with-text"></span> Publicaciones <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>




        <swiper-container pagination="true" slides-per-view="3.5" space-between="10" slides-offset-before="15" slides-offset-after="30" pager="true" class="swiperStyleSanfer">
          <swiper-slide *ngFor="let post of publications | orderBy: '-updated_at'"  >
            <ion-card button="true"  class="post-card" (click)="openContent(post)">
              <ion-img class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
            </ion-card>

          </swiper-slide>
        </swiper-container>

      </ion-col>
      <ion-col class="hiddenCol" size="12"  size-lg="2" size-md="1" size-xs="1">

      </ion-col>
    </ion-row>

    <ion-row >

      <div class="ion-padding no_padding_bottom separadores">
        Recursos de Investigación <ion-icon name="arrow-forward-outline"></ion-icon>
      </div>
      <ion-col>

        <swiper-container  #swipermenu class="menu-icons" slides-per-view="3.5" space-between="10" slides-offset-before="15" slides-offset-after="15">
          <swiper-slide >
            <ion-card button="true"  class="card-icons" (click)="openBrowser('https://pubmed.ncbi.nlm.nih.gov')">
              <ion-img class="centrar_imagen " style="height: 100px; width: 100px;" src="../assets/inicio/icono_pubmed_color.svg"></ion-img>

            </ion-card>
          </swiper-slide>
          <swiper-slide>
            <ion-card button="true" class="card-icons" routerLink="/springer" color="primary">
              <ion-img class="centrar_imagen" style="height: 100px; width: 100px;" src="../assets/inicio/Icono_springer.svg"></ion-img>
            </ion-card>
          </swiper-slide>
          <swiper-slide>
            <ion-card button="true"  class="card-icons" (click)="openBrowser('https://www.tandfonline.com/')">
              <ion-img class="centrar_imagen" style="height: 100px; width: 100px;" src="../assets/inicio/icono_taylor.svg"></ion-img>

            </ion-card>
          </swiper-slide>
          <swiper-slide >
            <ion-card button="true"  class="card-icons" (click)="openBrowser('https://actualidadmedica.mx/')">
              <ion-img class="centrar_imagen" style="height: 100px; width: 100px;" src="../assets/inicio/icono_actualidad.svg"></ion-img>


            </ion-card>
          </swiper-slide>


        </swiper-container>
      </ion-col>



    </ion-row>

    <!--        <ion-row>-->
    <!--          <ion-img class="centrar_imagen"  style="width: 30%; max-width: 250px; padding-top: 20px"  src="/assets/sanfer_conecta_oficial.svg"></ion-img>-->

    <!--        </ion-row>-->









    <ion-row class="no_padding_top no_padding_top">
      <ion-col class="hiddenCol" size="12"  size-lg="2" size-md="1" size-xs="1">

      </ion-col>
      <ion-col size="12" size-xl="8" size-lg="8" size-md="8" size-xs="12">
        <div class="ion-padding separadores" *ngIf="promocionalesSwitch">
          Promocionales <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>

        <swiper-container *ngIf="promocionalesSwitch" pagination="true" slides-per-view="1.1" space-between="10" slides-offset-before="15" slides-offset-after="15" class="swiperStyleSanfer" >
          <swiper-slide *ngFor="let promo of promocionales">

            <ion-card button="true" class="promo-cards" (click)="openBrowser(promo.link)">
              <ion-img class="centrar_imagen" src="{{strapiUrl + promo.image.url}}"></ion-img>
            </ion-card>
          </swiper-slide>
        </swiper-container>
      </ion-col>
      <ion-col class="hiddenCol" size="12"  size-lg="2" size-md="1" size-xs="1">

      </ion-col>
    </ion-row>


    <ion-row style="height: 100px"></ion-row>

  </ion-grid>



  <ion-modal
    #modalQR
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="0.6"
    [breakpoints]="[0, 0.6, 0.8]"
    [backdropDismiss]="true"
  >
    <ng-template>
      <ion-content class="ion-padding" color="primary">
        <div class="ion-text-center" >
          <h1>Bienvenida {{userInStorage.nombre}}</h1>
          <p>Muestra este código a tu representante para obtener muestras medicas y promociones exclusivas</p>
          <qrcode (click)="modalQR.setCurrentBreakpoint(0.8)" [qrdata]="userInStorage.uid" [width]="qrWidth" [errorCorrectionLevel]="'M'"></qrcode>


          <p>Cedula profesional {{cedula}}</p>
        </div>


      </ion-content>
    </ng-template>
  </ion-modal>




  <ion-modal
    #adBanner
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="0.24"
    [breakpoints]="[0, 0.24, 1]"
    [backdropDismiss]="true"
  >
    <ng-template>
      <ion-content >
        <ion-fab vertical="top" horizontal="end" *ngIf="closeBtn">
          <ion-fab-button (click)="closeModalBanner()">
            <ion-icon name="close"></ion-icon>
          </ion-fab-button>
        </ion-fab>
        <div class="ion-text-center banner-modal-style" >
          <!--          <ion-img src="{{bannerImg}}"></ion-img>-->
          <ion-img (click)="adBanner.setCurrentBreakpoint(1); activeButton();"  src="{{bannerImg}}"></ion-img>

          <h1>{{banners.promocion}}</h1>
          <h3 class="small_text"><ion-icon name="calendar" color="primary"></ion-icon> Vigencia: {{banners.vigencia}}</h3>
          <p style="color: #2e2f2f; font-size: 14px; line-height: 22px;">{{banners.condiciones}}</p>

          <div class="ion-padding" *ngIf="linkSwitch">
            <ion-button class="ion-text-wrap" (click)="abrirSitiodeCompra(banners.url)"> {{banners.boton}}
            </ion-button>
          </div>

        </div>
        <div style="height: 300px"></div>


      </ion-content>
    </ng-template>
  </ion-modal>




  <ion-modal
    #publicationModal
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="1"
    [breakpoints]="[0,  1]"

    [backdropDismiss]="true"
  >
    <ng-template>

      <ion-fab class="ion-padding" vertical="bottom" horizontal="center" *ngIf="closeBottomBtn"  @fadeAnimation>
        <ion-fab-button [color]="buttonColor"  (click)="closePublicationModal(postContent)">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <ion-fab vertical="top" horizontal="center" *ngIf="closeTopBtn" @fadeAnimation>
        <ion-fab-button [color]="buttonColor"  (click)="closePublicationModal(postContent)">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <!-------------Video--------------------->
      <ion-content color="dark" *ngIf="postContent.type === 'video'">
        <div class="ion-text-center vimeo-modal-style" >
          <div id="reproductorModal"></div>
        </div>
      </ion-content>

      <!-------------Galería--------------------->
      <ion-content *ngIf="postContent.type === 'gallery'">

        <div class="ion-text-center banner-modal-style" >
          <swiper-container pagination="true" *ngIf="postContent.gallery"   slides-per-view="1" class="ion-no-padding ion-no-margin swiperStyleSanfer" >
            <swiper-slide *ngFor="let img of postContent.gallery">
              <ion-img style="padding-bottom: 30px" class="centrar_imagen" src="{{strapiUrl}}{{img.url}}"></ion-img>
            </swiper-slide>
          </swiper-container>
          <h1>{{postContent.title}}</h1>
          <p  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></p>
          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)"> {{postContent.textButton}}
            </ion-button>
          </div>
          <h3 *ngIf="postContent.sourceInfo" (click)="openBrowserModalPublication(postContent.sourceLink)" class="small_text"><ion-icon name="link" color="primary"></ion-icon> Fuente: {{postContent.sourceInfo}}</h3>
        </div>
        <div style="height: 300px"></div>



      </ion-content>
      <!-------------Text--------------------->

      <ion-content [color]="modalcolor" *ngIf="postContent.type === 'text'">
        <div class="ion-text-center banner-modal-style" >
          <ion-img *ngIf="postContent.imageSwitch" class="centrar_imagen" src="{{strapiUrl}}{{postContent.image.url}}"></ion-img>
          <h1>{{postContent.title}}</h1>
          <div *ngIf="textContent" class="ion-padding no_padding_top"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></div>
          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)"> {{postContent.textButton}}
            </ion-button>
          </div>
          <h3 *ngIf="postContent.sourceInfo" (click)="openBrowserModalPublication(postContent.sourceLink)" class="small_text"><ion-icon name="link" color="primary"></ion-icon> Fuente: {{postContent.sourceInfo}}</h3>
        </div>
        <div style="height: 300px"></div>


      </ion-content>

      <!-------------Promo--------------------->
      <ion-content [color]="modalcolor" *ngIf="postContent.type === 'promo'">

        <div class="ion-text-center banner-modal-style" >
          <ion-img *ngIf="postContent.image"  src="{{strapiUrl}}{{postContent.image.url}}"></ion-img>

          <h1>{{postContent.promocion}}</h1>
          <h3 class="small_text"><ion-icon name="calendar" color="primary"></ion-icon> Vigencia: {{postContent.validity}}</h3>
          <p  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></p>

          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)">{{postContent.textButton}}</ion-button>
          </div>

        </div>
        <div style="height: 300px"></div>


      </ion-content>




    </ng-template>
  </ion-modal>

  <ion-button expand="block" (click)="signOut()">Cerrar sesion</ion-button>
<!--  <ion-button expand="block" (click)="contactoWhatsapp()">Contacto whatsapp</ion-button>-->





  <ion-modal
    #modalEspecialidades
    [initialBreakpoint]="0.5"
    [breakpoints]="[0.5, 1]"
    backdrop-dismiss="true"
  >
    <ng-template>
      <ion-content color="primary">
        <p class="ion-text-center">
          Para actualizar el contenido de las publicaciones como personal de sanfer, primero selecciona la categoria sanfer que deseas visualizar despues de la actualizacion
        </p>
        <ion-searchbar #searchBar color="light" placeholder="Buscar especialidad sanfer" (click)="modalEspecialidades.setCurrentBreakpoint(1)" (ionInput)="buscar( $event )"></ion-searchbar>

        <ion-list>
          <ion-item button="true" *ngFor="let especialidad of especialidadesSanfer | filterBy: ['categoria']: textoBuscar" (click)="loadCategory(especialidad)">
            <ion-icon name="apps-outline" color="primary" slot="start"></ion-icon>
            <ion-label>
              <h2>{{especialidad.categoria}}</h2>
            </ion-label>
            <ion-icon name="chevron-forward-circle-outline" slot="end" color="primary"></ion-icon>
          </ion-item>

        </ion-list>
        <p style="height: 200px"></p>
      </ion-content>
    </ng-template>
  </ion-modal>

  <ion-modal  #modalContentLoading [initialBreakpoint]="0.25" backdrop-dismiss="true">
    <ng-template>
      <ion-content color="primary" class="ion-padding ion-text-center">

        <p style="margin-top: 0;">Estamos cargando la información de la especialidad médica, que selecciionaste: {{especialidadSeleccionada}}
        </p>
        <ion-chip>
          Usuario
          <ion-icon *ngIf="userVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!userVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Vademecum
          <ion-icon *ngIf="vademecumVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!vademecumVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Modelos 3D
          <ion-icon *ngIf="modelsVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!modelsVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Noticias
          <ion-icon *ngIf="newsVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!newsVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>
        <ion-chip>
          Cursos
          <ion-icon *ngIf="academyVerified" name="checkmark-circle" color="success" ></ion-icon>
          <ion-icon *ngIf="!academyVerified" name="checkmark-circle-outline"></ion-icon>
        </ion-chip>

        <p></p>
        <p style="height: 200px"></p>
      </ion-content>
    </ng-template>
  </ion-modal>




</ion-content>
