.welcome-card img {
  max-height: 35vh;
  overflow: hidden;
}


.card-icons {
  margin: 15px 1px;
  border-radius: 20px;

}

.publicaciones ion-icon {
  font-size: 24px; /* Establece el tamaño del icono a 24px */
}

.icon-with-text {
  display: flex;
  align-items: center; /* Alinea los elementos hijos verticalmente en el centro */
  gap: 5px; /* Opcional: Añade un pequeño espacio entre el icono y el texto */
}

.separadores {
  display: flex;
  align-items: center; /* Asegura que todos los elementos dentro de 'separadores' también estén centrados verticalmente */
  gap: 10px; /* Espacio entre el conjunto icono-texto y el icono de flecha */
}

.swiper-button-prev {
  color: var(--ion-color-primary);
}
.swiper-button-next {
  color: var(--ion-color-primary);
}

.youtube-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Transparencia ligera */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}


.sample-slider{
  --add-bottom: 50px;
  padding-bottom: var(--add-bottom);
}
.sample-slider [class^="swiper-button-"]{
  top: calc(50% - var(--add-bottom) / 2);
}


.banner-modal-style{

}

//.menu-icons-card {
//  border-radius: 16px;
//}






//
//.welcome-slides{
//
//  --swiper-pagination-color: #fff;
//}
.swiper-container, .swiper-slide {
  display: flex;
  height: 100%;
}

.welcome-slides {
  --swiper-pagination-color: #fff;
  width: 100%;
}

.welcome-content {
  display: flex;
  flex-direction: column; /* Coloca los elementos de forma vertical */
  justify-content: center; /* Centrado vertical */
  align-items: center; /* Centrado horizontal */
  text-align: center; /* Asegura que el texto esté centrado */
  width: 100%;
  height: 700px;
}

.post-card {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
  margin-top: 0px;
  margin-left: 1px;
  padding-bottom: 127.78%; /* Esto mantiene la relación de aspecto de 9x16 */
  overflow: hidden;
  border-radius: 16px;
}
ion-card.promo-cards{
  //margin: 0 0 0 20px;
  margin: 0 1px 20px 1px;
  border-radius: 16px;
}
.post-thumbnail {
  position: absolute;
  object-fit: cover; /* Se encarga de escalar y recortar la imagen para llenar el contenendor mientras se mantiene su aspecto */
  object-position: center; /* Centra la imagen dentro del contenedor */
  width: 100%;
  height: 100%;

  //width: 100%;
  //height: 150px;
  //object-fit: cover; /* Se encarga de escalar y recortar la imagen para llenar el contenendor mientras se mantiene su aspecto */
  //object-position: center; /* Centra la imagen dentro del contenedor */
}



.vimeo-modal-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.post-title{
  font-size: 10px;
  color: #1e2023;
  text-align: center;
}
//.welcome-icon  {
//  margin: auto;
//  width: 200px;
//
//}
//.welcome-slides img {
//  margin: auto;
//  width: 100px;
//}

/* inicio.component.css */
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

ion-modal::part(handle) {
  width: 100px;
  background: rgba(255,255,255,0.5);
}

/*ion-toolbar {
  --background: transparent;
  --ion-color-base: transparent !important;
}*/

ion-content {
  /*--background: url('../../assets/bg_home.jpg') no-repeat center/cover fixed;
  --background-size: cover;*/
  //--background: #ccc url("../../assets/bg_home.png") no-repeat center center / cover;
  //--background: none;
  //
  //background: #ccc url("../../../assets/bg_home.png") no-repeat center center / cover;

}
ion-content.wob {
  /*--background: url('../../assets/bg_home.jpg') no-repeat center/cover fixed;
  --background-size: cover;*/
  //--background: #ccc url("../../assets/bg_home.png") no-repeat center center / cover;
  --background: none;

  background:none;

}

ion-header, ion-footer {

  border: 0 !important;
  border-bottom-color: transparent !important;
  background-image: none !important;
  border-bottom: none !important;
}



/*
.ion-padding-top {
  padding-top: 80px

}

ion-slide.bienvenida{
  display: block;
}

.slide-img-padding{
  padding: 20% 20% 10% 20%;
}
.slide-img-maxWidth{
  max-width: 250px;
}

.bienvenidaTitulo{
  font-size: 30px;
  font-family: 'HelveticaNeue-Light', 'HelveticaNeue', Helvetica, Arial, sans-serif;
  font-weight: bold;

}
.bienvenidaParrafo{
  font-size: 24px;
}
.pWidth{
  text-align: center;
  max-width: 400px;
  margin: auto;
}
*/

/*ion-toolbar.in-toolbar.ios.toolbar-title-default.hydrated, ion-toolbar.in-toolbar.ios.hydrated  {
  background-color: #ee3b33;
}*/



//ion-slides {
//  height: 90%;
//}
//
//.container{
//  max-width: 50%;
//  margin: auto;
//}
//
//.swiper-slide {
//  display: block;
//}
//
//.swiper-slide h2 {
//  color: #03687f;
//  margin-top: 2.8rem;
//  font-weight: bold;
//  font-size: 28px;
//}
//
//.swiper-slide img {
//  max-height: 50%;
//  max-width: 50%;
//  margin-top: 25%;
//  pointer-events: none;
//}

b {
  font-weight: 500;
  color: #e63027!important;

}

//p {
//  color: #03687f;
//
//  padding: 0 20%;
//  font-size: 20px;
//  line-height: 1.5;
//}

p b {
  color: var(--ion-text-color, #000000);
}



//
//ion-card.sp{
//  margin: 5px;
//  padding: 0;
//}
//
//ion-slides.sp {
//  padding-bottom: 10px;
//
//}




@media (max-width: 600px) {
  .swiperStyleSanfer h1 {
    font-size: 12px!important;
  }
  .banner-modal-style h1 {
    font-size: 20px;
  }
  //ion-row.maxHeightRow{ height: 150px!important}
  //ion-card.maxHeightRow{ height: 145px!important}
  //ion-img.farmacovigilancia{
  //  width: 20%!important;
  //  padding-top: 3%!important;
  //}
  //.swiper-slide img {
  //  margin-top: 10%;
  //
  //  max-height: 50%;
  //  max-width: 70%;
  //}
  p {
    padding: 0 10%;
  }
}
@media (max-width: 835px) {
  .swiperStyleSanfer h1 {
    font-size: 14px!important;
  }

  .banner-modal-style h1 {
    font-size: 25px;
  }

}


@media (min-width: 1080px) {
  .swiper-slide img {
    margin-top: 10%;

    max-width: 30%;
  }

}
@media (min-width: 1380px) {
  .swiper-slide img {
    margin-top: 10%;

    max-width: 30%;
  }

}



@media (min-width: 1920px) {
  .swiper-slide img {
    margin-top: 10%;

    max-width: 20%;
  }

}
@media (min-width: 2300px) {
  .swiper-slide img {
    margin-top: 10%;

    max-width: 15%;
  }

}
