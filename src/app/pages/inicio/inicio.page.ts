import {Component, OnInit, AfterViewInit, ViewChild, ElementRef, ChangeDetectorRef, HostListener, OnDestroy} from '@angular/core';
import {FirebaseAuthService} from '../../services/firebase-auth.service';
import {WidgetUtilService} from '../../services/widget-util.service';
import {Router} from '@angular/router';
import {
  DomController,
  IonModal,
  LoadingController, MenuController,
  ModalController, Platform,
  PopoverController
} from '@ionic/angular';
import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';
import { AlertController, Animation, AnimationController } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { Browser } from '@capacitor/browser';
import {CapacitorHttp, HttpResponse} from '@capacitor/core';
import {FirestoreDbService} from '../../services/firestore-db.service';
// import {GenerarCodigoRepresentantePage} from '../generar-codigo-representante/generar-codigo-representante.page';
import {CompletarRegistroPage} from '../completar-registro/completar-registro.page';
import {Firestore, doc, docData, collection, getDoc, writeBatch, increment, setDoc} from '@angular/fire/firestore';
import {Auth, deleteUser, getAuth, onAuthStateChanged, sendEmailVerification, signOut} from '@angular/fire/auth';
import {SpecialtiesService} from '../../services/specialties.service';
import {getFirestore} from 'firebase/firestore';
import {SwiperContainer} from "swiper/swiper-element";
import {FirebaseAuthentication} from "@capacitor-firebase/authentication";
import {FirebaseFirestore} from "@capacitor-firebase/firestore";
import {marked} from "marked";
import Swiper from 'swiper';
import Player from '@vimeo/player';
import {ContentService} from "../../services/content.service";
import {AuthService} from "../../services/auth.service";
import {BrowserService} from "../../services/browser.service";
import {AnalyticsService} from "../../services/analytics.service";
import {animate, style, transition, trigger} from "@angular/animations";
const incrementValue = increment(1);
const decrementValue = increment(-1);
import {
  FirebaseMessaging,
  GetTokenOptions,
} from "@capacitor-firebase/messaging";
import { Capacitor } from "@capacitor/core";
import {PushNotificationService} from "../../services/push-notification.service";
import { App } from '@capacitor/app';
import {AndroidSettings, IOSSettings, NativeSettings} from "capacitor-native-settings";
import {NotificationsPage} from "../../popovers/notifications/notifications.page";
import {UpdateUserService} from "../../services/update-user.service";
import {Subscription} from "rxjs";
import { PluginListenerHandle } from '@capacitor/core';

@Component({
  selector: 'app-inicio',
  templateUrl: './inicio.page.html',
  styleUrls: ['./inicio.page.scss'],
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.3s 0.3s ease-in', style({ opacity: 1 })) // Agregar un retraso de 0.3s
      ]),
      transition(':leave', [
        animate('0.3s ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class InicioPage implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('swiper') swiper!: ElementRef<SwiperContainer>;

  @ViewChild('adBanner') modalBanner: IonModal;
  @ViewChild('modalQR') modalQR: IonModal;
  @ViewChild('galleryModal') galleryModal: IonModal;

  @ViewChild('publicationModal') publicationModal: IonModal;


  @ViewChild('modalEspecialidades') modalEspecialidades: IonModal;
  @ViewChild('modalContentLoading') modalContentLoading: IonModal;

  especialidadesSanfer: any;
  especialidadSeleccionada: string;
  showSpinner: boolean = false;
  userVerified: boolean = false;
  newsVerified: boolean = false;
  academyVerified: boolean = false;
  vademecumVerified: boolean = false;
  modelsVerified: boolean = false;
  textoBuscar: string = '';
  categoriaSanfer: any;



  token = "";


  // swiperEl = document.querySelector('slideHome');

  isNativePlatform: boolean = false

  closeBottomBtn: boolean = false;
  closeTopBtn: boolean = false;

  banners: any;
  bannerImg: string;
  qrWidth: number = 256;

  posts:any = [];
  publications:any = [];
  springer: any;
  imagen: any;
  userInStorage: any;

  userContent: any;

  SendGridAPI: any = environment.sendGridAPI;
  measurementId: any = environment.firebaseConfig.measurementId;
  CURRENT_VERSION = environment.version;


  showSignupSpinner: boolean = false;

  isLoggedIn: boolean = false;
  slides: any = [];
  slideHome: any;
  welcomeSlides: any = [];
  promocionales: any = [];

  promocionalesSwitch: boolean = false;
  promoSwitch: boolean = false;

  linkSwitch: boolean = false;
  closeBtn: boolean = false;


  postContent: any = {}
  textContent: any
  modalcolor: string = 'white'
  buttonColor: string = 'primary'


  videoId: any
  vimeoPlayer: Player;





  // categorias: any;
  categoriaRep: any;


  updates: boolean = false;
  strapiUrl: string = environment.strapiURL;

  activated: boolean = false;

  confirmed: boolean = false;

  version: number = environment.version;


  especialidades: any;
  categoria: any;
  fecha = new Date();

  cedula: string = '';
  nombre: string = '';
  apellido: string = '';
  correo: string = '';
  telefono: string = '';
  isAdmin: boolean = false;
  role: string = '';
  udn: string = '';
  admin = false;
  nEmpleado: string = '';
  passcode: string = '';
  equipo: string = '';

  titulo: any;
  verifyUID: any;
  resultadoRepresentante: any;

  equipoAsignado: string = '';

  listadoDescargado: boolean = false;

  especialidadBanner: any;
  videoPaused = true; // Estado inicial del video (pausado)

  pubilictyDate = 0;
  slideOpts = {
    initialSlide: 0,
    speed: 400,
    pager: true,
    autoplay: true

  };





  bannerSeen: boolean = false;


  windowWidth: number;



  notifications: any = {};

  private appStateListener: PluginListenerHandle;
  private appRestoredListener: PluginListenerHandle;
  private isRefreshing = false; // Flag para controlar la ejecución


  constructor(
    private cdRef: ChangeDetectorRef,
    // // private db: AngularFirestore,
    private firestore: Firestore,
    private auth: Auth,

    private strapi: StrapiService,
    private firebaseAuthService: FirebaseAuthService,
    private widgetUtilService: WidgetUtilService,
    private router: Router,
    private domCtrl: DomController,
    private modalCtrl: ModalController,
    public alertController: AlertController,
    public animationCtrl: AnimationController,
    private firestoreDbService: FirestoreDbService,
    // private http: HTTP,
    // private fbas: FirebaseAnalyticsService,
    private loadingController: LoadingController,
    public popoverController: PopoverController,
    private especialidadService: SpecialtiesService,
    private content: ContentService,
    private menuCtrl: MenuController,
    private authService: AuthService,
    private browser: BrowserService,
    private analyticsService: AnalyticsService,
    private pns: PushNotificationService,
    private updateUser: UpdateUserService,
    private platform: Platform


) {

    FirebaseMessaging.addListener("notificationReceived", async (event) => {
      this.notifications = await this.pns.getDeliveredNotifications();
    });
    FirebaseMessaging.addListener("notificationActionPerformed", async (event: any) => {
      console.log("notificationActionPerformed: ", { event });
      await this.openBrowser(event.notification.data.url)
    });



  }





  async ngOnInit() {
    this.windowWidth = window.innerWidth;

    await this.getAuthState();
    await this.getUserContent();



    // Verifica si la plataforma es nativa o es web para mostrar u ocultar elementos segun la plataforma
    this.isNativePlatform = Capacitor.getPlatform() === 'android' || Capacitor.getPlatform() === 'ios';

    const appInstance = await this.analyticsService.getAppInstance();
    console.log('App instance', appInstance)
    await this.analyticsService.setCurrentScreen('Inicio' );


    if (this.platform.is('capacitor')) {
      // Móvil (iOS/Android)
      this.initializeMobileListener();
      this.initializeAppRestoredListener(); // Detecta si la app fue cerrada y luego abierta
    } else {
      // Web
      this.initializeWebListeners();
    }
    // const analytics = getAnalytics();
    // logEvent(analytics, 'screen_view', {
    //   firebase_screen: 'Inicio',
    //   firebase_screen_class: 'screenClass-standby'
    // });
    // Banners de bienvenida en el slide de la recepcion
    // this.strapi.getContenido('bienvenidas').subscribe((resp: any) => {
    //   this.welcomeSlides = resp;
    //   console.log('Slides Respuesta 1', this.welcomeSlides);
    // });
    // // Banners publicitarios para el home
    // this.strapi.getContenido('slides/1').subscribe(resp => {
    //   this.activated = resp.Activado;
    //   this.slides = resp.Slides;
    //   console.log('Slides Respuesta 2', this.slides);
    // });




    // this.getMedico();
    // this.checkRole();
    // this.getGeo();
    // console.log();
  }

  ngOnDestroy() {
    // Limpiar el listener móvil si existe
    this.appStateListener?.remove();
    this.appRestoredListener?.remove(); // Limpiar el listener para la restauración de la app

    // Limpiar listeners web
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('focus', this.handleFocus);
  }

  async ngAfterViewInit() {

    this.notifications = await this.pns.getDeliveredNotifications();

    setTimeout(async () => {
      console.log('Solicitando permiso...');

      await this.verificacionInicialNotificaciones();
    }, 10000);

    // console.log('Constante Slide Home', slideHome)
    // this.cdRef.detectChanges();
    // this.initializeVimeoPlayers(slideHome);






    // setTimeout(() => {
    //   if (this.miSwiper) {
    //     console.log('Mi swiper ')
    //     this.initSwiper();
    //   } else {
    //     console.log('No swiper ')
    //
    //   }
    // }, 500);

    // if (this.miSwiper) {
    //   console.log('Mi swiper ')
    //   this.initSwiper();
    // } else {
    //   console.log('No swiper ')
    //
    // }


  }


  async actualizarMedicoDB(userInStorage: any){
    if (userInStorage.version !== this.CURRENT_VERSION) {
      const success = await this.updateUser.updateUserCollections(userInStorage.uid, userInStorage, userInStorage.role )
      if (success) {
        const userRef = doc(this.firestore, `medicos/${userInStorage.uid}` );
        const userSnapshot = await getDoc(userRef);
        if (userSnapshot.exists()) {
          const userData = userSnapshot.data();
          await this.updateDataInStorage(userData, userInStorage.uid);
        }


      } else {
        console.log('La actualización del medico no fue necesaria o falló.');

      }
    } else {
      console.log('Medico Actualizado, no se requiere actualizacion');
    }
  }
  async actualizarRepresentanteDB(userInStorage: any){
    if (userInStorage.version !== this.CURRENT_VERSION) {
      const success = await this.updateUser.updateUserCollections(userInStorage.uid, userInStorage.email, userInStorage.role );
      if (success) {
        const userRef = doc(this.firestore, `representantes/${userInStorage.uid}` );
        const userSnapshot = await getDoc(userRef);
        if (userSnapshot.exists()) {
          const userData = userSnapshot.data();
          await this.updateDataInStorage(userData, userInStorage.uid);
        }
      } else {
        console.log('La actualización del representante no fue necesaria o falló.');

      }
    } else {
      console.log('Representante Actualizado, no se requiere actualizacion');
    }
  }



  initializeVimeoPlayers(slideHome: any) {
    console.log('Constante Slide Home dentro de la funcion: ', slideHome)

    slideHome.forEach(slide => {
      if (slide.type === 'video' && slide.urlVimeo) {
        console.log('Se detecto video: ', slide, `player-${slide.id}`)


        const player = new Player(`player-${slide.id}`, {
          url: slide.urlVimeo,
          // width: 640
        });
        // Configuración adicional del player, eventos, etc.
      }
    });
  }



  // async ionViewWillLeave() {
  //
  //   if (!this.userInStorage) {
  //     this.verifyOrDelete();
  //   }
  // }

  async ionViewDidEnter() {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    this.userInStorage = await JSON.parse(storageRawData.value);

    console.log('Storage despues de Ion Vio did Enter ', this.userInStorage)

    if (this.userInStorage) {

      await this.verifyRole(this.userInStorage);
      this.cedula = this.userInStorage.cedula;
      this.nombre = this.userInStorage.nombre;
      this.apellido = this.userInStorage.apellido;
      this.correo = this.userInStorage.correo;
      this.telefono = this.userInStorage.telefono;
      this.isAdmin = this.userInStorage.isAdmin;
      this.role = this.userInStorage.role;
      this.titulo = this.userInStorage.titulo;

      console.log('Storage Local encontrado: ', this.nombre, this.apellido, this.cedula, this.role, 'Administrador:', this.isAdmin, this.titulo)


      // console.log('Titulo', this.titulo);


      // const dataFirstTime: any = await Preferences.get({ key: 'firstTime' });
      //
      // const firstTime = JSON.parse(dataFirstTime.value);
      // this.firstTime = firstTime.firstTime;
      // console.log('First Time despues de Ion Vio did Enter ', this.firstTime)

      if (this.userInStorage.isAdmin) {
        console.log('Administrador detectado ', this.userInStorage.isAdmin)

        await this.checkIsAdmin(this.userInStorage);
      }



      // this.checkForUpdates();

    } else {
      // this.verifyOrDelete();
      await this.verifyOrDelete();
    }
  }
  // Verificacion de gerentes de producto



  // async sliderOption(num: number) {
  //   console.log('clicked', num)
  //   // this.swiperEl.slidesPerView = 3;
  //   // this.swiperEl.setAttribute('slides-per-view', '3');
  //   this.swiperEl.swiper.slideNext();
  //
  //   // // or if it was initialized with props
  //   // this.swiperEl.slidesPerView = 3;
  //
  // }


  // private initSwiper(): void {
  //   console.log('Init Slide')
  //
  //   const swiper = this.miSwiper.nativeElement.swiper;
  //   console.log('after nativ elemet')
  //
  //   if (swiper) {
  //     swiper.on('slideChange', () => {
  //       const activeIndex = swiper.activeIndex;
  //       const activeSlide = this.slideHome[activeIndex];
  //       if (activeSlide && activeSlide.type === 'video') {
  //         this.handleVideo(activeSlide.urlVimeo, 'sliderVideoPlayer' + activeIndex);
  //       }
  //     });
  //   }
  // }

  async numeroEmpleado(userInStorage: any) {

    const ret: any = await Preferences.get({ key: 'user' });
    const u = JSON.parse(ret.value);

    const alert = await this.alertController.create({
      cssClass: 'solicitarCita',
      message: `Proporcionanos tu numero de empleado
           ${userInStorage.nombre} requerimos que nos confirmes tu numero de empleado Sanfer con la letra M.`,
      inputs: [
        {
          name: 'nEmpleado',
          type: 'text',
          placeholder: 'Numero de Empleado Sanfer',
        }
      ],
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel',
          cssClass: 'secondary',
          handler: async () => {
            this.numeroEmpleado(userInStorage.nombre);
          }
        },
        {
          text: 'Actualizar Numero',
          handler: async ( numeroEmpleado ) => {
            console.log(numeroEmpleado.nEmpleado);


            const confirm = await this.alertController.create({
              header: numeroEmpleado.nEmpleado,
              message: `${userInStorage.nombre} ingresaste <strong> ${numeroEmpleado.nEmpleado} </strong> como tu numero de empleado, <br>¿estas de acuerdo? asegurate de agregar la letra M`,
              buttons: [
                {
                  text: 'Cancelar',
                  role: 'cancel',
                  cssClass: 'secondary',
                  handler: async () => {
                    this.numeroEmpleado(userInStorage);
                  }
                }, {
                  text: 'Confirmar',
                  handler: async () => {
                    console.log('Numero de empleado nuevo:', numeroEmpleado);
                    this.firestoreDbService.insertDataUserMerge('representantes', userInStorage.uid, {
                      nEmpleado: numeroEmpleado.nEmpleado
                    });

                    this.setUserDataInStorage(userInStorage, 'representantes');

                  }
                }
              ]
            });

            await confirm.present();

          }
        }]
    });

    await alert.present();
  }

  private async initializeMobileListener() {
    await this.platform.ready();
    this.appStateListener = await App.addListener('appStateChange', (state) => {
      if (state.isActive) {
        this.handleRoleBasedRefresh();
      }
    });
  }

  private async initializeAppRestoredListener() {
    await this.platform.ready();
    this.appRestoredListener = await App.addListener('appRestoredResult', () => {
      this.handleRoleBasedRefresh();
    });
  }

  private initializeWebListeners() {
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('focus', this.handleFocus);
  }

  private handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      this.handleRoleBasedRefresh();
    }
  };

  private handleFocus = () => {
    this.handleRoleBasedRefresh();
  };

  private async handleRoleBasedRefresh() {
    if (this.isRefreshing) {
      return; // Si ya se está refrescando, no hacer nada
    }

    this.isRefreshing = true; // Indicar que ha comenzado el refresco

    try {
      if (this.role === 'medico') {
        await this.homeRefresh(null);
      } else if (this.role === 'representante') {
        await this.actualizarContenidoRepresentanteRefresh(null);
      }
    } finally {
      this.isRefreshing = false; // Indicar que el refresco ha terminado
    }
  }


  async homeRefresh(event: any) {
    try {
      await this.widgetUtilService.presentLoadingMessage('Actualizando información...');
      await this.content.getUser(this.userInStorage);
      await this.getUserContent();
    } finally {
      await this.widgetUtilService.dismissLoader();
      if (event && event.target) {
        event.target.complete();
      }
    }
  }

  async changeUserContent() {

  }

  async actualizarContenidoRepresentante () {
    if(this.categoriaSanfer.value !== null) {
      console.log('Se encontro categoria Sanfer', this.categoriaSanfer.id)
      await this.verifySanferEmployeeData(this.categoriaSanfer, this.categoriaSanfer.id)
    } else {
      console.log('No Se encontro categoria Sanfer', this.categoriaSanfer.id)
      await this.seleccionarEspecialidad();
    }
  }

  async actualizarContenidoRepresentanteRefresh (event: any) {
    if(this.categoriaSanfer.value !== null) {
      console.log('Se encontro categoria Sanfer', this.categoriaSanfer)
      await this.verifySanferEmployeeData(this.categoriaSanfer, this.categoriaSanfer.id)
      event.target.complete();

    } else {
      console.log('No Se encontro categoria Sanfer', this.categoriaSanfer)
      await this.seleccionarEspecialidad();
      event.target.complete();
    }
  }

  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    console.log('User content', userContent)
    const categoriaSanfer = await Preferences.get({ key: 'especialidadBanner' });
    this.categoriaSanfer = JSON.parse(categoriaSanfer.value)
    console.log('Categoria Sanfer Parse', this.categoriaSanfer);
    this.userContent = await JSON.parse(userContent.value);
    console.log('Home user content: ', this.userContent)

    const banners = this.userContent.banners
    // console.log('Home contant loaded: ', banners)

    // Slide Pricipal del home
    this.slides = banners.slides
    this.slideHome = banners.slideHome;

    const swiper = new Swiper('.swiperHome', {
      direction: 'horizontal',
      // loop: true,
      pagination: {el: '.swiper-pagination'},
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      on: {
        slideChange: () => {
          // const activeSlide = this.slideHome[swiper.activeIndex];
          // if (activeSlide.type === 'video') {
          //   // this.youtubePlayerComponent.togglePlay()
          //   console.log('swipe al video registrado')
          //
          //   // this.slideVideo(activeSlide.urlVimeo, `sliderVideoPlayer${activeSlide.id}`);
          // }
        }
      }
    });

    console.log('Data del slide Home: ',this.slideHome);


    // Promo Slides
    this.promocionales = banners.promos;
    console.log('Promocionales: ', this.promocionales)
    this.promocionalesSwitch = banners.promocionalesSwitch;

    // Posteos multimedia
    this.posts = banners.posteos
    this.publications = banners.publications


    // Banner principal
    this.bannerImg = this.strapiUrl + banners.bannerPrincipal.url;
    this.promoSwitch = banners.promocionSwitch;
    this.linkSwitch = banners.linkSwitch;

    // return this.slideHome
  }

  async showPromo () {
    await this.modalBanner.present();

  }

  async cambioDeEquipo( userInStorage: any) {
    const ret: any = await Preferences.get({ key: 'user' });
    const u = JSON.parse(ret.value);


    const alert = await this.alertController.create({
      cssClass: 'solicitarCita',
      message: `<h4>Pertenecías al equipo ${userInStorage.equipo}</h4>
                <p [class]="small_text">pero requerimos que nos confirmes tu nuevo equipo asignado</p>`,
      inputs: [
        {
          name: 'argonautas',
          type: 'radio',
          label: 'Argonautas',
          value: 'argonautas-Argonautas2g?B3M',
        },
        {
          name: 'minotauros',
          type: 'radio',
          label: 'Minotauros',
          value: 'minotauros-Minotaurosj3cW!Q'
        },
        {
          name: 'apolos',
          type: 'radio',
          label: 'Apolos',
          value: 'apolos-ApolosS}5$aC',
        },
        {
          name: 'poseidones',
          type: 'radio',
          label: 'Poseidones',
          value: 'poseidones-Poseidonesv4R?ys'
        },
        {
          name: 'ciclopes',
          type: 'radio',
          label: 'Ciclopes',
          value: 'ciclopes-CiclopesbY9/6H',
        },
        {
          name: 'cronos',
          type: 'radio',
          label: 'Cronos',
          value: 'cronos-Cronos=5B?bf'
        },
        {
          name: 'atlantes',
          type: 'radio',
          label: 'Atlantes',
          value: 'atlantes-Atlantese4=CW4',
        },
        {
          name: 'titanes',
          type: 'radio',
          label: 'Titanes',
          value: 'titanes-Titanes6cyF{@'
        },
        {
          name: 'pegasos',
          type: 'radio',
          label: 'Pegasos',
          value: 'pegasos-Pegasos38vM<#',
        },
        {
          name: 'perseus',
          type: 'radio',
          label: 'Perseus',
          value: 'perseus-Perseus=E9n?#'
        },
        {
          name: 'midas',
          type: 'radio',
          label: 'Midas',
          value: 'midas-Midas4@Tjw)'
        },
        {
          name: 'horus',
          type: 'radio',
          label: 'Horus',
          value: 'horus-HorusM3tR+B'
        },
        {
          name: 'fenix-respi',
          type: 'radio',
          label: 'Fenix Respi',
          value: 'fenixrespi-Respi5M#Xqm'
        },
        {
          name: 'fenix-cardio-gineco',
          type: 'radio',
          label: 'Fenix Cardio Gineco',
          value: 'fenixcardiogineco-CardioGineco5buS@b'
        },
        {
          name: 'hades',
          type: 'radio',
          label: 'Hades',
          value: 'hades-Hades2;{vWj'
        },
        {
          name: 'hermes',
          type: 'radio',
          label: 'Hermes',
          value: 'hermes-Hermes3J2#yS'
        },
        {
          name: 'onco',
          type: 'radio',
          label: 'Onco',
          value: 'onco-Onco3J2#yS'
        },
        {
          name: 'nefro',
          type: 'radio',
          label: 'Nefro',
          value: 'nefro-Nefro=8V5'
        },
        {
          name: 'vulcanos',
          type: 'radio',
          label: 'Vulcanos',
          value: 'vulcanos-VulcanosWa=8V5'
        }
      ],
      buttons: [{
        text: 'Cancelar',
        role: 'cancel',
        cssClass: 'secondary',
        handler: async () => {
          this.cambioDeEquipo(userInStorage.equipo);
        }
      },
        {
          text: 'Cambiar equípo',
          role: 'presencial',
          cssClass: 'secondary',
          handler: async ( nuevoEquipo ) => {
            console.log(nuevoEquipo);

            const equipoDividido = nuevoEquipo.split('-');

            const confirm = await this.alertController.create({
              cssClass: 'my-custom-class',
              header: equipoDividido[0].toUpperCase(),
              message: `Seleccionaste ${equipoDividido[0].toUpperCase()} como tu nuevo equipo, ¿estas de acuerdo?`,
              buttons: [
                {
                  text: 'Cancelar',
                  role: 'cancel',
                  cssClass: 'secondary',
                  handler: async () => {
                    this.cambioDeEquipo(userInStorage.equipo);
                  }
                }, {
                  text: 'Confirmar',
                  handler: () => {
                    console.log('Passcode nuevo:', equipoDividido[1]);
                    this.firestoreDbService.insertDataUserMerge('representantes', u.uid, {
                      equipo: equipoDividido[0],
                      passcode: equipoDividido[1]

                    });

                    this.verifyRole(userInStorage);
                  }
                }
              ]
            });

            await confirm.present();

          }
        }]
    });

    await alert.present();
  }


  // Solicitud de visita del representante

  async solicitarVisita() {

    const alert = await this.alertController.create({
      cssClass: 'solicitarCita',
      message: `¿Qué tipo de visita le gustaría ${this.nombre}?`,
      inputs: [
        {
          name: 'presencial',
          type: 'radio',
          label: 'Visita presencial',
          value: 'presencial',
          checked: true
        },
        {
          name: 'virtual',
          type: 'radio',
          label: 'Visita virtual',
          value: 'virtual'
        }
      ],
      buttons: [
        {
          text: 'Solicitar visita',
          role: 'presencial',
          cssClass: 'secondary',
          handler: ( visita ) => {
            console.log(visita);
            this.enviarCorreo( visita );
          }
        }]
    });

    await alert.present();
  }



  async onSlideChange() {
    console.log('cambio de diapositiva')
    // const activeSlide = this.slideHome[/* índice del slide activo */];
    //
    // if (activeSlide && activeSlide.type === 'video') {
    //   // Llamar a la función para manejar el video
    //   // Por ejemplo: this.handleVideo(activeSlide.urlVimeo, 'idDelContenedorDeVideo');
    // }

  }

  // Envio del correo para Solicitud de visita del representante

  async enviarCorreo( visita: string ) {
    try {
      this.showSignupSpinner = true;
      // template del correo enviado
      const htmlTemplate = `
            <!DOCTYPE html>
                <html>
                    <body>
  <img src="http://cdn.mcauto-images-production.sendgrid.net/9ca8404a3798f71d/5e4c3738-68ad-4e58-9c20-f89457b4ece2/300x122.png"/>
                        <h1>
                            Solicitud de visita médica
                        </h1>
                        <br>
                        <h3>Datos del Médico</h3><br>
                        <p>El médico ${this.nombre} ${this.apellido}</p>
                        <p>Con cédula profesional: ${this.cedula}</p>
                        <p>Su correo es: ${this.correo}</p>
                        <p>Su teléfono es: ${this.telefono}</p>
                        <p>Le gustaría una visita: ${visita}</p>
                    </body>
                </html>
      `;
      // Reporta a firebase Analytics
      // firebase.analytics().logEvent('solicitudCita', {tipo: visita});
      // this.fbas.logEventParam('solicitud_de_cita', 'tipo_de_cita', visita);

      // this.http.setServerTrustMode('nocheck');
      // // this.http.setHeader('*', 'Access-Control-Allow-Origin' , '*');
      // // this.http.setHeader('*', 'Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT');
      // // this.http.setHeader('*', 'Accept', 'application/json');
      // // this.http.setHeader('*', 'content-type', 'application/json');
      // this.http.setDataSerializer('json');

      console.log(htmlTemplate)
      const options = {
        url: 'https://api.sendgrid.com/v3/mail/send',
        headers: {['authorization']: `Bearer ${this.SendGridAPI}`,
          ['Content-Type']: 'application/json'},
        data: {
          personalizations: [{to: [{email: '<EMAIL>'}]}],
          from: {email: '<EMAIL>', name: 'Sanfer Conecta'},
          subject: 'Solicitud de visita médica',
          content: [{type: 'text/html', value: htmlTemplate}]}

      };

      const response: HttpResponse = await CapacitorHttp.post(options);

      // this.http.post('https://api.sendgrid.com/v3/mail/send',
      //     {
      //       personalizations: [{to: [{email: '<EMAIL>'}]}],
      //       from: {email: '<EMAIL>', name: 'Sanfer Conecta'},
      //       subject: 'Solicitud de visita médica',
      //       content: [{type: 'text/html', value: htmlTemplate}]},
      //     {
      //       ['authorization']: `Bearer ${this.SendGridAPI}`,
      //       ['Content-Type']: 'application/json'})
      //     .then((data: any) => {
      //
      //       console.log(data.status);
      //       console.log(data.data); // data received by server
      //       console.log(data.headers);
      //
      //     })
      //     .catch((error: any) => {
      //
      //       console.log(error.status);
      //       console.log(error.error); // error message as string
      //       console.log(error.headers);
      //     });



      this.widgetUtilService.presentToast
      (`<h3>¡Reporte exitoso!</h3><br>Muchas gracias ${this.nombre}
Su solicitud ha sido recibida, muy pronto uno de nuestros representantes se pondrá en contacto con usted <br> ${response}`);

      // console.log('Resultados del usuario Nuevo: ', result1);
      // this.router.navigate(['/inicio']);
      this.showSignupSpinner = false;
    } catch (error) {
      console.log(error);
    }


  }




  // Remover la pantalla de bienvenida para el home
  // async removeWelcome() {
  //   this.firstTime = false;
  //   await Preferences.set({
  //     key: 'firstTime',
  //     value: JSON.stringify({
  //       firstTime: false
  //     })
  //   });
  //
  //   // console.log('First Time: ', this.firstTime);
  //
  //   // this.checkIsAdmin();
  //   // this.checkRole();
  //   // this.checkCedula();
  //   console.log('Es administrador: ', this.isAdmin);
  //
  //
  // }

  // // Abrir el código QR en el home
  // async openQR() {
  //   const modal  = await this.modalCtrl.create({
  //     component: GenerarCodigoPage,
  //     cssClass: 'qr',
  //     componentProps: {
  //       claseId: this.cedula
  //     }
  //   });
  //   await modal.present();
  // }


  // async openQRRepresentante() {
  //   const modal  = await this.modalCtrl.create({
  //     component: GenerarCodigoRepresentantePage,
  //     cssClass: 'qr',
  //     componentProps: {
  //       claseId: this.cedula
  //     }
  //   });
  //   await modal.present();
  // }


  async completeRegistration(fuente: string) {
    const ret: any = await Preferences.get({ key: 'user' });
    const u = JSON.parse(ret.value);

    console.log('Verificacion del retorno ', u);

    const modal  = await this.modalCtrl.create({
      component: CompletarRegistroPage,
      backdropDismiss: false,
      cssClass: 'qr',
      componentProps: {
        nombreLog: u.nombre,
        apellidoLog: u.apellido,
        uidLog: u.uid,
        emailLog: u.correo,
        representanteLog: u.representante,
        fuenteLog: fuente,
        cedulaLog: u.cedula,
        telefonoLog: u.telefono
      }
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();
    console.log('Registro completado:', data.registroCompletado);
    if (data.registroCompletado === true) {
      // this.checkRole();
      console.log('Verificando Role...');
    }
  }

  // Obtiene los datos del médico que se encuentran en el Storage
  //
  // async getMedico() {
  //   const ret = await Storage.get({ key: 'user' });
  //   const user = JSON.parse(ret.value);
  //   this.cedula = user.cedula;
  //   this.nombre = user.nombre;
  //   this.apellido = user.apellido;
  //   this.correo = user.correo;
  //   this.telefono = user.telefono;
  //   this.isAdmin = user.isAdmin;
  //   this.role = user.role;
  //
  //
  //   // console.log('Usuario Storage', user);
  //
  //   const fret = await Storage.get({ key: 'firstTime' });
  //   const fuser = JSON.parse(fret.value);
  //   this.firstTime = fuser.firstTime;
  //
  //   // this.checkIsAdmin();
  //
  //
  // }



  // Activar geolocalizacion del usuario
  // getGeo() {
  //
  //   this.geolocation.getCurrentPosition().then((resp) => {
  //     // resp.coords.latitude
  //     // resp.coords.longitude
  //
  //     const coords = `${resp.coords.latitude},${resp.coords.longitude}`;
  //
  //     this.lng = resp.coords.latitude;
  //     this.lat = resp.coords.longitude;
  //
  //     this.coords = coords;
  //     console.log('Longitud', this.lng);
  //     console.log('Latitud', this.lat);
  //     console.log('Coordenadas', coords);
  //   }).catch((error) => {
  //     console.log('Error getting location', error);
  //   });
  //
  // }




  // Version anterior de firebase
  // getAuthState() {
  //
  //   this.firebaseAuthService.getAuthState().subscribe(user => {
  //     // console.log('User auth state', user ? user.toJSON(): null);
  //     if (user) {
  //       // this.widgetUtilService.presentToast(user.uid);
  //
  //       const userFirestore = firebase.auth().currentUser;
  //
  //       console.log('User in Firestore for verification', userFirestore);
  //       if (!userFirestore.emailVerified) {
  //         console.log('Usuario No Verificado');
  //         this.emailVerification();
  //       }
  //       this.isLoggedIn = true;
  //     } else {
  //
  //       this.isLoggedIn = false;
  //       this.router.navigate(['/home']);
  //
  //
  //     }
  //
  //
  //   });
  //
  // }

  // getAuthState() {
  //   const auth = getAuth();
  //   // const db = getFirestore();
  //
  //   onAuthStateChanged(auth, (user) => {
  //     if (user) {
  //       console.log('User in Firestore for verification', user);
  //       if (!user.emailVerified) {
  //         console.log('Usuario No Verificado');
  //         this.emailVerification();
  //       }
  //       this.isLoggedIn = true;
  //     } else {
  //       this.isLoggedIn = false;
  //       this.router.navigate(['/home']);
  //     }
  //   });
  // }



  async getAuthState() {
    console.log('Init GetAuthState');

    try {
      // Periodically check the auth state
      const checkAuthState = async () => {
        const result = await FirebaseAuthentication.getCurrentUser();
        const user = result.user;
        console.log('User in Firestore', user);

        if (user) {
          console.log('User in Firestore for verification', user);
          if (!user.emailVerified) {
            console.log('Usuario No Verificado');
            await this.emailVerification();
          }
          this.isLoggedIn = true;
        } else {
          console.log(' No Usuario!!');

          this.isLoggedIn = false;

          // Activar despues de la remodelacion del home
          // this.router.navigate(['/home']);
        }
      };

      // Call this function periodically as needed
      // For example, you could use setInterval or a similar approach
      await checkAuthState();
    } catch (error) {
      console.error('Error checking authentication state:', error);
      // Handle error
    }
  }








  async clearStorage() {
    await Preferences.clear();
  }

  async logout() {
    try {
      await this.firebaseAuthService.logout();
      // this.fbas.logEvent('logout');
      this.clearStorage();


      // firebase.analytics().logEvent('logout');

      // this.widgetUtilService.presentToast('Sesión cerrada satisfactoriamente');
      this.router.navigate(['/login']);
      document.location.href = 'index.html';

    } catch (error: any) {

      console.log('Error', error);
      this.widgetUtilService.presentToastError(error.message);
    }

  }








  // async checkCedula() {
  //
  //   const ret = await Storage.get({ key: 'user' });
  //   const u = JSON.parse(ret.value);
  //
  //   console.log('Contenido del Storage', u);
  //
  //   if (u === null) {
  //
  //     // this.verifyOrDelete();
  //   }
  //
  //
  //
  // }





  async verifyRole(userInStorage: any) {
    console.log('Role del Usuario: ', userInStorage.role);
    switch (userInStorage.role) {
      case undefined:
        this.oldUser(userInStorage);
        break;

      case null:
        this.oldUser(userInStorage);
        break;
      case 'noMedico':
        console.log('El role del usuario es noMedico');
        this.noMedico(userInStorage);
        break;
      case 'medico':
        console.log('El role del usuario es Médico');
        this.doctor( userInStorage );
        await this.actualizarMedicoDB(userInStorage);

        break;
      case 'representante':
        console.log('El role del usuario es representante');
        await this.medicalRepresentative(userInStorage);
        await this.checkIsAdmin(userInStorage);
        await this.actualizarRepresentanteDB(userInStorage);
        break;
      case 'productividad':
        console.log('El role del usuario es productividad');
        this.productivityStaff(userInStorage);
        await this.actualizarRepresentanteDB(userInStorage);

        break;
      case 'administrador':
        console.log('El role del usuario es administrador');
        this.administrator(userInStorage);
        break;

    }
  }

  // Version anterior de firebase
  // funcion oldUser se ejecuta si el role es undefined, lo que indica que es un usuario registrado con aterioridad
  // async oldUser(userInStorage) {
  //   // console.log('funcion null ejecutada el usuario retorno un role null');
  //
  //
  //   //
  //
  //   // validamos si el usuario se obtuvo de el registro de respaldo cuando no teniamos validacion de cedula ante la sep
  //   if (userInStorage.registro === 'Registro SanferConecta Respaldo') {
  //     // ejecutamos la funcion para completar el registo
  //     // y mandamostanto la data como el registro obtenido de la informacion de firestore "Registro"
  //     this.completeRegisty(userInStorage, 'Registro SanferConecta Respaldo');
  //   }
  //
  //
  //   // //
  //   // Si el usuario es un medico antiguo, agrega el role del medico,
  //   // obtiene la data de vuelta de firesote y la anida en el storage local
  //   // para volver a realizar la verificacion
  //   if (userInStorage.registro === 'Registro SanferConecta') {
  //
  //     // Agrega el role medico en firestore
  //     const sanferConecta = await this.db.collection('medicos').doc(userInStorage.uid).set({
  //       role: 'medico'
  //     }, {merge: true});
  //     this.setUserDataInStorage(userInStorage, 'medicos');
  //
  //   }
  //
  //   // // //
  //   // Si el usuario es un represntante antiguo, agrega el role del representante,
  //   // obtiene la data de vuelta de firesote y la anida en el storage local
  //   // para volver a realizar la verificacion
  //   if (userInStorage.passcode) {
  //     // console.log('es un representante Antiguo');
  //
  //     // Agrega el role representante en firestore
  //     const sanferConecta = await this.db.collection('representantes').doc(userInStorage.uid).set({
  //       role: 'representante'
  //     }, {merge: true});
  //     this.setUserDataInStorage(userInStorage, 'representantes');
  //   }
  // }


  async oldUser(userInStorage: any) {
    const db = getFirestore(); // Get Firestore instance

    if (userInStorage.registro === 'Registro SanferConecta Respaldo') {
      // Execute function to complete registration
      await this.completeRegisty(userInStorage, 'Registro SanferConecta Respaldo');
    }

    if (userInStorage.registro === 'Registro SanferConecta') {
      // Add 'medico' role in Firestore and update local storage
      const medicoRef = doc(db, 'medicos', userInStorage.uid);
      await setDoc(medicoRef, { role: 'medico' }, { merge: true });
      await this.setUserDataInStorage(userInStorage, 'medicos');
    }

    if (userInStorage.passcode) {
      // Add 'representante' role in Firestore and update local storage
      const representanteRef = doc(db, 'representantes', userInStorage.uid);
      await setDoc(representanteRef, { role: 'representante' }, { merge: true });
      await this.setUserDataInStorage(userInStorage, 'representantes');
    }
  }




  noMedico(userInStorage: any) {
    // console.log('funcion noMedico ejecutada');
    // ejecutamos la funcion para completar el registo
    // y mandamostanto la data como el registro obtenido de la informacion de firestore "Registro"
    this.completeRegisty(userInStorage, 'noMedico');
  }
  doctor( userInStorage: any ) {
    // console.log('funcion Medico ejecutada');

    // this.especialidadService.especialidadesMedicas().subscribe( resp => {
    //     this.especialidades = resp.tituloProfesional;
    //     // console.log( 'Espacialidades medicas', this.especialidades);
    //
    //     this.bannerEspecialidad(this.userInStorage.titulo, this.especialidades);
    //
    //
    //   }
    //
    // );

    // this.verifyStorageVersion(userInStorage);

  }


  async medicalRepresentative(userInStorage: any) {
    console.log('funcion Representante ejecutada', userInStorage);

    // this.verifyStorageVersion(userInStorage);
    // this.especialidadService.categoriaBanner().subscribe( resp => {
    //     this.categorias = resp;
    //     console.log( 'Categorias del banner', this.categorias);
    //     this.categoriaRep = this.categorias[17];
    //
    //
    //
    //     // this.bannerEspecialidad(, this.especialidades);
    //
    //
    //   }
    //
    // );





    if (!userInStorage.udn) {
      console.log('El representante no tiene UDN');

      await this.passcodeMR(userInStorage);

    } else {
      // console.log('El representante si tiene , se asigna el role: ', userInStorage.role);

      this.role = userInStorage.role;
    }

  }
  async productivityStaff(userInStorage: any) {
    // console.log('funcion Productividad ejecutada');

    // console.log('Se detecto que es productividad');
    this.role = userInStorage.role;

    // this.verifyStorageVersion(userInStorage);



  }
  administrator(userInStorage: any) {
    // console.log('funcion Administrador ejecutada');
    const validatedAdmin = this.checkIsAdmin(userInStorage);
    this.isAdmin = userInStorage.isAdmin;

    // this.verifyStorageVersion(userInStorage);


  }






  async completeRegisty( userInStorage: any, fuente: string) {
    // esta funcion es ejecutada si el usuario no tiene el registro completo de su role, solo aplica para noMedico
    // y para usuarios registrados antes de la acrualización que no tienen un role definido
    // console.log('Funcion completeRegisty ejecutada');

    const modal  = await this.modalCtrl.create({
      component: CompletarRegistroPage,
      backdropDismiss: false,
      cssClass: 'qr',
      componentProps: {
        nombreLog: userInStorage.nombre,
        apellidoLog: userInStorage.apellido,
        uidLog: userInStorage.uid,
        emailLog: userInStorage.correo,
        representanteLog: userInStorage.representante,
        fuenteLog: fuente,
        cedulaLog: userInStorage.cedula,
        telefonoLog: userInStorage.telefono
      }
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();
    // console.log('Registro completado:', data.registroCompletado);


    if (data.registroCompletado) {

      const storageRawData: any = await Preferences.get({ key: 'user' });
      this.userInStorage = JSON.parse(storageRawData.value);
      this.verifyRole(this.userInStorage);
      this.role = this.userInStorage.role;
      // this.firstTime = true;
      // console.log('Verificando Role... role en el storage nuevo: ', this.userInStorage.role);
    }
  }



  // Version anterior de firebase
// Falta actualizar
//   async checkIsAdmin(userInStorage) {
//     // console.log('Es administrador despues de la funcion: ', userInStorage);
//     if (userInStorage.isAdmin) {
//       // console.log('Inicio del Admin', userInStorage.isAdmin);
//
//
//       const representante = await this.db.collection('representantes').doc(userInStorage.uid).ref.get();
//       const representanteAdministrador = representante.data();
//       this.isAdmin = representanteAdministrador.isAdmin;
//
//       // console.log('Firestore nos comunica que es: ', this.isAdmin);
//
//       return this.isAdmin;
//     }
//   }


  // async checkIsAdmin(userInStorage: any) {
  //   if (userInStorage.isAdmin) {
  //     const representanteRef = doc(this.firestore, 'representantes', userInStorage.uid);
  //     const representanteSnapshot = await getDoc(representanteRef);
  //
  //
  //     if (representanteSnapshot.exists()) {
  //       const representanteAdministrador: any = representanteSnapshot.data();
  //       this.isAdmin = representanteAdministrador.isAdmin;
  //       return this.isAdmin;
  //     } else {
  //       // Handle the case where the document does not exist
  //       console.log('No such document!');
  //       return false;
  //     }
  //   }
  // }

  async checkIsAdmin(userInStorage: any): Promise<boolean> {
    // Default value for isAdmin
    let isAdmin = false;

    if (userInStorage.isAdmin) {
      const representanteRef = doc(this.firestore, 'representantes', userInStorage.uid);
      const representanteSnapshot = await getDoc(representanteRef);

      if (representanteSnapshot.exists()) {
        const representanteAdministrador: any = representanteSnapshot.data();
        isAdmin = representanteAdministrador.isAdmin;
      } else {
        console.log('No such document!');
      }
    }

    // Return the isAdmin flag
    return isAdmin;
  }


// Version anterior de Firebase
  // async setUserDataInStorage(userInStorage, collectionToGetUser) {
  //
  //
  //   // Esta funcion trae de vuelta el ultimo resultado del usuario en firestore,
  //   // el user ins storages se utiliza para tomar el uid y el collection para definir
  //   // si consulta la tabla de los 'medicos' o la de los 'representantes'
  //   const userInFirestore = await this.db.collection(collectionToGetUser).doc(userInStorage.uid).ref.get();
  //   const usuarioSolicitado = userInFirestore.data();
  //
  //   await Storage.remove({ key: 'user' });
  //
  //   // console.log('Usuario en Firebase', usuarioSolicitado);
  //
  //   await Storage.set({
  //     key: 'user',
  //     value: JSON.stringify({
  //
  //       // Datos compartidos por cualquier usuario
  //       uid: userInStorage.uid,
  //       nombre: usuarioSolicitado.nombre,
  //       apellido: usuarioSolicitado.apellido,
  //       correo: usuarioSolicitado.email,
  //       telefono: usuarioSolicitado.telefono,
  //       role: usuarioSolicitado.role,
  //       registro: usuarioSolicitado.Registro,
  //       fecha: usuarioSolicitado.fechaMs,
  //
  //       // Datos de un Medico
  //       cedula: usuarioSolicitado.cedula,
  //       titulo: usuarioSolicitado.titulo,
  //       institucion: usuarioSolicitado.institucion,
  //       tipo: usuarioSolicitado.tipo,
  //       anioRegistro: usuarioSolicitado.anioRegistro,
  //       genero: usuarioSolicitado.genero,
  //       representante: usuarioSolicitado.representante,
  //       fechaFinal: usuarioSolicitado.fechaFinal,
  //
  //       // Datos de un representante
  //       nEmpleado: usuarioSolicitado.nEmpleado,
  //       passcode: usuarioSolicitado.passcode,
  //       udn: usuarioSolicitado.udn,
  //       equipo: usuarioSolicitado.equipo,
  //
  //       // Datos de un Administrador
  //       isAdmin: usuarioSolicitado.admin,
  //
  //
  //
  //
  //
  //
  //
  //     })
  //   });
  //
  //   this.firstTime = true;
  //   this.showSignupSpinner = false;
  //   const storageRawData = await Storage.get({ key: 'user' });
  //   this.userInStorage = JSON.parse(storageRawData.value);
  //   this.role = userInStorage.role;
  //   this.fbas.setUserId(userInStorage.uid);
  //
  //
  //   // En este punto se intenta ejecutar nuevamente la validacion
  //   this.verifyRole(this.userInStorage);
  // }


  async setUserDataInStorage(userInStorage: any, collectionToGetUser: string) {

    try {
      // Construct the document reference
      const docRef = `${collectionToGetUser}/${userInStorage.uid}`;

      // Get the document from Firestore
      const userSnapshot = await FirebaseFirestore.getDocument({ reference: docRef });

      if (userSnapshot.snapshot) {
        // Assuming 'data()' method or equivalent to extract the data
        const usuarioSolicitado: any = userSnapshot.snapshot.data;
        // Remove the existing storage item
        await Preferences.remove({ key: 'user' });

        // Set the new user data in storage
        await Preferences.set({
          key: 'user',
          value: JSON.stringify({
            // Data common to all users
            uid: userInStorage.uid,
            nombre: usuarioSolicitado.nombre,
            apellido: usuarioSolicitado.apellido,
            correo: usuarioSolicitado.email,
            telefono: usuarioSolicitado.telefono,
            role: usuarioSolicitado.role,
            registro: usuarioSolicitado.Registro,
            fecha: usuarioSolicitado.fechaMs,
            // Doctor-specific data
            cedula: usuarioSolicitado.cedula,
            titulo: usuarioSolicitado.titulo,
            institucion: usuarioSolicitado.institucion,
            tipo: usuarioSolicitado.tipo,
            anioRegistro: usuarioSolicitado.anioRegistro,
            genero: usuarioSolicitado.genero,
            representante: usuarioSolicitado.representante,
            fechaFinal: usuarioSolicitado.fechaFinal,
            // Representative-specific data
            nEmpleado: usuarioSolicitado.nEmpleado,
            passcode: usuarioSolicitado.passcode,
            udn: usuarioSolicitado.udn,
            equipo: usuarioSolicitado.equipo,
            // Admin-specific data
            isAdmin: usuarioSolicitado.admin,
          })
        });

        // this.firstTime = true;
        this.showSignupSpinner = false;

        const storageRawData: any = await Preferences.get({ key: 'user' });
        this.userInStorage = JSON.parse(storageRawData.value);
        this.role = userInStorage.role;

        // Set user ID for analytics
        // this.fbas.setUserId(userInStorage.uid);

        // Re-validate the user role
        await this.verifyRole(this.userInStorage);

        // Proceed with storing the user data as needed
        // Your logic for handling and storing the user data goes here
      } else {
        // Handle the case where the document does not exist
        console.log('User document does not exist');
      }
    } catch (error) {
      // Handle any errors that occur during the fetch
      console.error('Error fetching user data:', error);
    }
    // Get the user data from Firestore
    // Esta funcion trae de vuelta el ultimo resultado del usuario en firestore,
    // el user ins storages se utiliza para tomar el uid y el collection para definir
    // si consulta la tabla de los 'medicos' o la de los 'representantes'


    // const userDocRef = doc(this.firestore, collectionToGetUser, userInStorage.uid);
    // const userSnapshot: any = await getDoc(userDocRef);
    // const usuarioSolicitado: User = userSnapshot.data();


  }




  async passcodeMR(userInStorage: any) {
    this.role = userInStorage.role;

    // Definir un mapa de passcodes a sus respectivos equipos y UDNs
    const passcodeMap = {
      'Argonautas2g?B3M': { equipo: 'argonautas', udn: 'dogma' },
      'Poseidonesv4R?ys': { equipo: 'poseidones', udn: 'dogma' },
      'Minotaurosj3cW!Q': { equipo: 'minotauros', udn: 'dogma' },
      'HorusM3tR+B': { equipo: 'horus', udn: 'dogma' },
      'Cronos=5B?bf': { equipo: 'cronos', udn: 'cardio' },
      'Atlantese4=CW4': { equipo: 'atlantes', udn: 'cardio' },
      'CiclopesbY9/6H': { equipo: 'ciclopes', udn: 'cardio' },
      'Pegasos38vM<#': { equipo: 'pegasos', udn: 'infecto' },
      'Perseus=E9n?#': { equipo: 'perseus', udn: 'infecto' },
      'Midas4@Tjw)': { equipo: 'midas', udn: 'infecto' },
      'Titanes6cyF{@': { equipo: 'titanes', udn: 'infecto' },
      'ApolosS}5$aC': { equipo: 'apolos', udn: 'fenix' },
      'Respi5M#Xqm': { equipo: 'fenixrespi', udn: 'fenix' },
      'CardioGineco5buS@b': { equipo: 'fenixcardiogineco', udn: 'fenix' },
      'VulcanosWa=8V5': { equipo: 'vulcanos', udn: 'otros' },
      'Onco3J2#yS': { equipo: 'onco', udn: 'otros' },
      'Nefro=8V5': { equipo: 'nefro', udn: 'otros' },
      'Hades2;{vWj': { equipo: 'hades', udn: 'otros' },
      'Hermes3J2#yS': { equipo: 'hermes', udn: 'otros' },
      'ComercialKa(^%C*4S8EH': { equipo: 'marketing', udn: 'marketing' },
      'Directores22PSZy2k,X#)': { equipo: 'directivo', udn: 'directivos' },
      // Cambio de Equipos, si tienes lógicas especiales para estos, se manejarían aparte.
      'Ignis5M#Xqm': { equipo: 'ignis', udn: 'cambio' },
      'Aeris5buS@b': { equipo: 'aeris', udn: 'cambio' },
      'Terra$4b}Nh': { equipo: 'terra', udn: 'cambio' },
      'Aqua6*bK%+': { equipo: 'aqua', udn: 'cambio' },
    };


    // Verificar y asignar equipo basado en el passcode
    if (userInStorage.passcode in passcodeMap && !userInStorage.udn) {
      const { equipo, udn } = passcodeMap[userInStorage.passcode];
      this.equipoAsignado = equipo;
      await this.actualizacionAutomaticaRepresentante(equipo, udn, userInStorage);
    } else if (['Ignis5M#Xqm', 'Aeris5buS@b', 'Terra$4b}Nh', 'Aqua6*bK%+'].includes(userInStorage.passcode)) {
      // Manejar cambio de equipos si es necesario
      await this.cambioDeEquipo(userInStorage.equipo);
    }

    // Verificar número de empleado
    if (!userInStorage.nEmpleado) {
      await this.numeroEmpleado(userInStorage);
    }
  }




  // async passcodeMR(userInStorage: any) {
  //   // console.log('Role del usuario en firebase result1.role', result1.role );
  //   // this.widgetUtilService.presentToast
  //   // (`<h3>${result1.nombre} te encuentras loggeado como ${result1.role}</h3> `);
  //   this.role = userInStorage.role;
  //
  //   if (!userInStorage.udn) {
  //     // DOGMA
  //     if (userInStorage.passcode === 'Argonautas2g?B3M'
  //       || userInStorage.passcode === 'Poseidonesv4R?ys'
  //       || userInStorage.passcode === 'Minotaurosj3cW!Q'
  //       || userInStorage.passcode === 'HorusM3tR+B') {
  //
  //       // const equipoLower = result1.equipo.toLowerCase();
  //       // console.log(equipoLower, 'Pertenece al equipo DOGMA');
  //
  //
  //       const udn = 'dogma';
  //
  //       switch (userInStorage.passcode) {
  //         case 'Argonautas2g?B3M':
  //           this.equipoAsignado = 'argonautas';
  //           break;
  //         case 'Poseidonesv4R?ys':
  //           this.equipoAsignado = 'poseidones';
  //           break;
  //         case 'Minotaurosj3cW!Q':
  //           this.equipoAsignado = 'minotauros';
  //           break;
  //         case 'HorusM3tR+B':
  //           this.equipoAsignado = 'horus';
  //           break;
  //
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //     // Cardio
  //
  //     if (userInStorage.passcode === 'Cronos=5B?bf'
  //       || userInStorage.passcode === 'Atlantese4=CW4'
  //       || userInStorage.passcode === 'CiclopesbY9/6H') {
  //
  //       // const equipoLower = result1.equipo.toLowerCase();
  //       // console.log(equipoLower, 'Pertenece al equipo Cardio');
  //       //
  //
  //       const udn = 'cardio';
  //
  //       switch (userInStorage.passcode) {
  //         case 'Cronos=5B?bf':
  //           this.equipoAsignado = 'cronos';
  //           break;
  //         case 'Atlantese4=CW4':
  //           this.equipoAsignado = 'atlantes';
  //           break;
  //         case 'CiclopesbY9/6H':
  //           this.equipoAsignado = 'ciclopes';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //     // Infecto
  //
  //     if (userInStorage.passcode === 'Pegasos38vM<#'
  //       || userInStorage.passcode === 'Perseus=E9n?#'
  //       || userInStorage.passcode === 'Midas4@Tjw)'
  //       || userInStorage.passcode === 'Titanes6cyF{@') {
  //
  //       const udn = 'infecto';
  //
  //       switch (userInStorage.passcode) {
  //         case 'Pegasos38vM<#':
  //           this.equipoAsignado = 'pegasos';
  //           break;
  //         case 'Perseus=E9n?#':
  //           this.equipoAsignado = 'perseus';
  //           break;
  //         case 'Midas4@Tjw)':
  //           this.equipoAsignado = 'midas';
  //           break;
  //         case 'Titanes6cyF{@':
  //           this.equipoAsignado = 'titanes';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //
  //     // Fenix
  //
  //     if (userInStorage.passcode === 'ApolosS}5$aC'
  //       || userInStorage.passcode === 'Respi5M#Xqm'
  //       || userInStorage.passcode === 'CardioGineco5buS@b') {
  //
  //       const udn = 'fenix';
  //
  //       switch (userInStorage.passcode) {
  //         case 'ApolosS}5$aC':
  //           this.equipoAsignado = 'apolos';
  //           break;
  //         case 'Respi5M#Xqm':
  //           this.equipoAsignado = 'fenixrespi';
  //           break;
  //         case 'CardioGineco5buS@b':
  //           this.equipoAsignado = 'fenixcardiogineco';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //
  //     // Otros
  //
  //     if (userInStorage.passcode === 'VulcanosWa=8V5'
  //       || userInStorage.passcode === 'Onco3J2#yS'
  //       || userInStorage.passcode === 'Nefro=8V5'
  //       || userInStorage.passcode === 'Hades2;{vWj'
  //       || userInStorage.passcode === 'Hermes3J2#yS') {
  //
  //       const udn = 'otros';
  //
  //       switch (userInStorage.passcode) {
  //         case 'VulcanosWa=8V5':
  //           this.equipoAsignado = 'vulcanos';
  //           break;
  //         case 'Onco3J2#yS':
  //           this.equipoAsignado = 'onco';
  //           break;
  //         case 'Nefro=8V5':
  //           this.equipoAsignado = 'nefro';
  //           break;
  //         case 'Hades2;{vWj':
  //           this.equipoAsignado = 'hades';
  //           break;
  //         case 'Hermes3J2#yS':
  //           this.equipoAsignado = 'hermes';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //
  //     //  Comercial Marketing
  //
  //     if (userInStorage.passcode === 'ComercialKa(^%C*4S8EH') {
  //
  //       const udn = 'marketing';
  //
  //       switch (userInStorage.passcode) {
  //         case 'ComercialKa(^%C*4S8EH':
  //           this.equipoAsignado = 'marketing';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //     // Directivos
  //
  //     if (userInStorage.passcode === 'Directores22PSZy2k,X#)') {
  //       const udn = 'directivos';
  //
  //       switch (userInStorage.passcode) {
  //         case 'Directores22PSZy2k,X#)':
  //           this.equipoAsignado = 'directivo';
  //           break;
  //       }
  //       this.actualizacionAutomaticaRepresentante(this.equipoAsignado, udn, userInStorage);
  //     }
  //
  //     // Cambio de Equipos
  //
  //     if (userInStorage.passcode === 'Ignis5M#Xqm' || userInStorage.passcode === 'Aeris5buS@b' || userInStorage.passcode === 'Terra$4b}Nh' || userInStorage.passcode === 'Aqua6*bK%+') {
  //
  //       // console.log(userInStorage.passcode, ' Se muestra selección de equipo para ellos ');
  //
  //
  //
  //       this.cambioDeEquipo( userInStorage.equipo );
  //
  //       // this.firestoreDbService.insertDataUserMerge('representante', u.uid, {
  //       //   udn: 'otras',
  //       //   equipo: equipoLower
  //       // });
  //     }
  //
  //
  //
  //   }
  //
  //
  //   if (!userInStorage.nEmpleado) {
  //
  //     // console.log('No tiene numero de empleado');
  //     this.numeroEmpleado(userInStorage);
  //   }
  //
  // }





  async actualizacionAutomaticaRepresentante(equipo: string, udn: string, userInStorage: any) {
    try {
      // Initialize Firestore batch
      const batch = writeBatch(this.firestore);

      // Define the documents to update
      const actualizarRepresentanteRef = doc(this.firestore, `representantes/${userInStorage.uid}`);
      // const listadoRepresentantesRef = doc(this.firestore, `productividad/${udn}/${this.equipoAsignado}/representantes/listado/${userInStorage.uid}`);
      // const actualizarContadorUdnRef = doc(this.firestore, `productividad/${udn}`);
      // const actualizarContadorEquipoRef = doc(this.firestore, `productividad/${udn}/${this.equipoAsignado}/representantes`);

      // Prepare the updates
      batch.set(actualizarRepresentanteRef, {
        udn,
        equipo,
        fechaMs: this.fecha.valueOf(),
      }, { merge: true });

      // batch.set(listadoRepresentantesRef, {
      //   nombre: userInStorage.nombre,
      //   apellido: userInStorage.apellido,
      //   email: userInStorage.email,
      //   telefono: userInStorage.telefono,
      //   uid: userInStorage.uid,
      //   udn,
      //   equipo,
      //   createdAt: this.fecha.toString(),
      //   nEmpleado: userInStorage.nEmpleado
      // });
      //
      // // const increment = firebase.firestore.FieldValue.increment(1); // Ensure you import and use Firestore FieldValue correctly
      // batch.set(actualizarContadorUdnRef, {
      //   registros: incrementValue,
      //   representantes: incrementValue
      // }, { merge: true });

      // batch.set(actualizarContadorEquipoRef, {
      //   registros: incrementValue
      // }, { merge: true });

      // Commit the batch
      await batch.commit();
      // Once the batch is committed, update the user data in storage
      await this.setUserDataInStorage(userInStorage, 'representantes');
    } catch (error) {
      console.error('Error in actualizacionAutomaticaRepresentante:', error);
      // Handle the error appropriately
    }
  }


  async verifyOrDelete() {
    const emergencyUIDData = await Preferences.get({ key: 'emergencyUID' });
    const uidReserva = emergencyUIDData.value ? JSON.parse(emergencyUIDData.value) : null;

    this.verifyUID = uidReserva ? uidReserva.uid : this.userInStorage?.uid;

    if (this.verifyUID) {
      try {
        const medicoRef = doc(this.firestore, `medicos/${this.verifyUID}`);
        const medicoSnapshot = await getDoc(medicoRef);

        if (medicoSnapshot.exists()) {
          const dataDelMedico = medicoSnapshot.data();
          // Add your logic here for handling the medico data
          // ...

          if (Object.keys(dataDelMedico).length === 0) {
            // If 'medico' is empty, check 'representante'
            await this.checkRepresentante();
          } else {
            // Logic for non-empty 'medico'

            if (this.userInStorage) {
              // Revisar Codigo actualizado
              if (!dataDelMedico['cedula'] && dataDelMedico['role'] === 'noMedico') {
                await this.completeRegistration('noMedico');
              }
            } else {
              await this.widgetUtilService.presentToastTime('Medico detectado como no medico', 5000);
              await this.logout();
            }
          }


        } else {
          // Medico does not exist, check representante
          await this.checkRepresentante();
        }
      } catch (error) {
        console.error('Error:', error);
        // Handle errors appropriately
      }
    }

    this.isLoggedIn = true; // Update isLoggedIn status based on your logic
  }

  async checkRepresentante() {
    const representanteRef = doc(this.firestore, `representantes/${this.verifyUID}`);
    const representanteSnapshot = await getDoc(representanteRef);

    if (representanteSnapshot.exists()) {
      const dataDelRepresentante = representanteSnapshot.data();

      if (Object.keys(dataDelRepresentante).length === 0) {
        await this.handleEmptyData();
      } else {
        // Logic for non-empty representante data
        // ...
      }
    } else {
      await this.handleEmptyData();
    }
  }

  async handleEmptyData() {
    // Logic for handling cases where data is empty or user does not exist
    console.log('USUARIO VACIO REMODELACION A CONTINUIACION DE BORRARA DESCOMENTAR LAS SIGUIENTES LINEAS')
    // await Storage.clear();
    // const user = this.auth.currentUser;
    //
    // if (user) {
    //   try {
    //     await deleteUser(user);
    //     window.alert('Algo en tu registro no salió bien, intenta registrarte nuevamente');
    //   } catch (error) {
    //     if (error.code === 'auth/requires-recent-login') {
    //       window.alert('Detectamos un error en tu cuenta, intenta ingresar nuevamente "Error cuenta de usuario no encontrada "');
    //       await signOut(this.auth);
    //     }
    //     // Additional error handling
    //   }
    // }
  }



  async verifyStorageVersion(userInStorage: any) {
    if (!userInStorage.version) {
      if (userInStorage.role === 'noMedico') {
        this.completeRegistration('noMedico');
      }

      if (userInStorage.role === 'medico' || userInStorage.role === 'representante' || userInStorage.role === 'productividad') {
        const userRef = doc(this.firestore, userInStorage.role === 'medico' ? 'medicos' : 'representantes', userInStorage.uid);
        const userSnapshot = await getDoc(userRef);
        if (userSnapshot.exists()) {
          const userData = userSnapshot.data();
          await this.updateDataInStorage(userData, userInStorage.uid);
        }
      }
    } else if (userInStorage.version !== 3.0) {
      // Logic for older version users
      // You can add your logic here for users with an older version
    }
  }




  async updateDataInStorage( userData: any , uid: string) {
    console.log('UID del usuario antes de ingresarlo nuevamente al storage: ', uid);
    const loading = await this.loadingController.create({
      message: `Estamos actualizando tu informacion para esta nueva versión ${this.version}`, backdropDismiss: true,
      translucent: true,
    });
    try {

      await loading.present();

      const dataToStore = {
        uid,
        ...userData,
        // Puedes seguir agregando otras propiedades aquí si es necesario
      };

      await Preferences.set({
        key: 'user',
        value: JSON.stringify(dataToStore)
      });



      await this.widgetUtilService.presentToast(`Tus dados se actualizaron correctamente a la nueva version'`);

      await loading.dismiss();


    } catch (error) {

      console.log('Algo salio mal, vuelve a loggearte', error);

      await this.algoSalioMal('Actualizacion de tus datos');
    }


  }

  async algoSalioMal(action: string) {
    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: 'Algo Salio mal',
      subHeader: `vuelve a loggearte`,

      message: `Algo salio mal durante la: ${action}` +
        'Cerraremos tu sesión solo ingresa nuevamente',
      backdropDismiss: false,

      buttons: [
        {
          text: 'Cerrar sesion',
          handler: async () => {
            this.logout();
            this.router.navigate(['/login']);

          }
        }
      ]
    });
    await alert.present();

    const { role } = await alert.onDidDismiss();
  }

  // Aqui comienzan las funciones de actualizacion de la APP Ionic
  //
  // async checkForUpdates() {
  //
  //   const update = await this.deploy.checkForUpdate();
  //   if (update.available) {
  //     this.updates = true;
  //   }
  //
  // }
  //
  // async performManualUpdate() {
  //   const update = await this.deploy.checkForUpdate();
  //   if (update.available) {
  //
  //
  //     await this.deploy.downloadUpdate(async (progress) => {
  //       console.log('Progreso de la app al descargar', progress);
  //
  //       this.showBar = true;
  //       this.porcentajeDescarga = progress/100;
  //       if (progress === 100) {
  //         this.showBar = false;
  //       }
  //
  //     })
  //     await this.deploy.extractUpdate(async (progress) => {
  //       console.log('Progreso de la app al extraer', progress);
  //       this.showBarUpdate = true;
  //       this.porcentajeActualizacion = progress/100;
  //       if (progress === 100) {
  //         this.showBarUpdate = false;
  //       }
  //     }) ;
  //
  //     await this.deploy.reloadApp();
  //
  //
  //
  //
  //     const loading = await this.loadingController.create({
  //       message: `Procederemos a actualizar la aplicación a su nueva versión, por favor no cierres la app... ${this.porcentajeActualizacion}%`, backdropDismiss: false,
  //       translucent: true,
  //     });
  //
  //     const downloading = await this.loadingController.create({
  //       message: `Estamos descargando la nueva version de la app...${this.porcentajeDescarga}%`, backdropDismiss: false,
  //       translucent: true,
  //     });
  //
  //     // await loading.present();
  //     // await this.deploy.extractUpdate((progress) => {
  //     //   console.log(progress);
  //     // });
  //     // loading.dismiss();
  //     // await this.deploy.reloadApp();
  //   }
  // }
  // async checkVersions() {
  //   const versions = await this.deploy.getAvailableVersions();
  //   console.log(versions);
  // }


  // Hasta aqui terminan las funciones de actualizacion de la APP Ionic

  async emailVerification() {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      console.error("No hay usuario actual.");
      return;
    }

    if (user.emailVerified) {
      console.log("El correo ya está verificado.");
      return; // Si el correo ya está verificado, no hacemos nada
    }

    try {
      await sendEmailVerification(user);
      console.log("Correo de verificación enviado.");
      await this.showPersistentAlert();
    } catch (error) {
      console.error("Error al enviar correo de verificación:", error);
    }
  }

  async showPersistentAlert() {
    let alert = await this.alertController.create({
      header: 'Verificación de Correo Requerida',
      message: `Un correo de verificación ha sido enviado. Por favor verifica tu correo para continuar usando la aplicación.`,
      backdropDismiss: false,
      buttons: [
        {
          text: 'Revisaré mi correo',
          handler: () => {
            this.verifyEmailStatus();
          }
        }
      ]
    });

    await alert.present();
  }

  async verifyEmailStatus() {
    const auth = getAuth();
    const currentUser = auth.currentUser;
    await currentUser.reload();

    if (currentUser.emailVerified) {
      console.log("Correo verificado. Cerrando alerta...");
      // Actualiza la UI o realiza acciones adicionales aquí si es necesario
    } else {
      console.log("Correo aún no verificado. Informando al usuario...");
      this.showNotVerifiedAlert();
    }
  }

  async showNotVerifiedAlert() {
    const auth = getAuth();
    const user = auth.currentUser;
    const notVerifiedAlert = await this.alertController.create({
      header: 'Correo No Verificado',
      message: 'Tu correo aún no ha sido verificado. Por favor, revisa tu bandeja de entrada y el spam. Si no encuentras el correo de verificación, puedes solicitar otro.',
      backdropDismiss: false,
      buttons: [{
        text: 'Solicitar otro correo',
        handler: async () => {
          console.log("Solicitando el reenvío del correo de verificación...");
          // Aquí se podría llamar a sendEmailVerification nuevamente
          await sendEmailVerification(user);
          await this.showPersistentAlert(); // Vuelve a mostrar la alerta original

        }
      }, {
        text: 'Cerrar',
        handler: () => {
          this.showPersistentAlert(); // Vuelve a mostrar la alerta original
        }
      }
      ]
    });

    await notVerifiedAlert.present();
  }



  async openArticle( url: string) {

    await Browser.open({ url: `${url}` });
  }


  async openBannerModal() {
    if( this.promoSwitch ) {
      console.log('Banner activo')
      await this.modalBanner.present();
    }
  }

  async openQRModal() {
    await this.modalQR.present();
  }

  async abrirSitiodeCompra(url: string) {
    await Browser.open({ url: `${url}`, toolbarColor: '#ff2836' });
  }


  async openBrowser(url:string) {
    await this.analyticsService.logEvent('open_browser', {url: url})
    await this.browser.openBrowser(url)
  }
  async openBrowserModalPublication(url:string) {

    await this.publicationModal.dismiss();
    await this.browser.openBrowser(url)

  }


  activeButton() {
    this.closeBtn = true
  }



  async closeModalBanner() {
    this.closeBtn = false
    await this.modalBanner.dismiss();
  }



  async handleVideo(videoURL: string, playerId: string) {
    console.log(videoURL, playerId);

    // Extrae el ID del video de Vimeo
    const vimeo = videoURL;
    if (vimeo.includes('https://')) {
      const idVimeo = vimeo.split('/');
      this.videoId = idVimeo[3];
    } else {
      this.videoId = vimeo;
    }

    console.log(this.videoId);

    // Elimina el reproductor existente
    if (this.vimeoPlayer !== undefined) {
      this.vimeoPlayer.destroy();
    }

    // Crea un nuevo reproductor
    this.vimeoPlayer = new Player(playerId, {
      id: this.videoId,
      responsive: true,
      color: '#f32d36'
    });

    // Eventos del reproductor
    this.vimeoPlayer.on('play', async (play: any) => {
      console.log('El usuario dio play en el video', play);
    });

    this.vimeoPlayer.on('pause', async (pause: any) => {
      console.log('El usuario dio pausa al video', pause);
    });

    this.vimeoPlayer.on('ended', async (end: any) => {
      console.log('El video ha terminado', end);
    });

    // Manejo de errores
    this.vimeoPlayer.on('error', (error: any) => {
      console.error('Error en el reproductor de Vimeo', error);
    });
  }


  async openContent(post: any) {
    this.postContent = post;

    this.modalcolor = post.modalColor;
    this.buttonColor = post.buttonColor;

    this.textContent = undefined
    if (this.postContent.textContent !== null && this.postContent.textContent !== undefined) {
      this.textContent = marked(this.postContent.textContent)
    }

    switch (post.type) {
      case 'link':
        console.log('Abrir vinculo')
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'link', title: post.title, url: this.postContent.link})

        await this.openBrowser(this.postContent.link)
        break;
      case 'video':
        console.log('Abrir video')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'video', title: post.title, url: post.videoURL})
        await this.handleVideo(post.videoURL, 'reproductorModal');
        this.cerrarModalHandle();
        break;
      case 'gallery':
        console.log('Abrir galeria Nueva')
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'gallery', title: post.title})
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        this.cerrarModalHandle();
        break;
      case 'pdf':
        console.log('Abrir pdf')
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'pdf', title: post.title, url: this.postContent.pdfURL})
        await this.openBrowser(this.postContent.pdfURL)
        break;
      case 'text':
        console.log('Abrir text')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'text', title: post.title})

        this.cerrarModalHandle();
        break;
      case 'model':
        console.log('Abrir model', this.postContent.modelURL)
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'model', title: post.title, url: this.postContent.modelURL})

        await this.open3DModel(this.postContent.modelURL)
        break;
      case 'promo':
        console.log('Abrir promo')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        await this.analyticsService.logEvent('abrir_publicacion', {type: 'promo', title: post.title})
        this.cerrarModalHandle();
        break;
    }

  }

  cerrarModalHandle() {
    this.publicationModal.onDidDismiss().then(() => {
      this.closeBottomBtn = false;
      this.closeTopBtn = false;
    });
  }

  // Cierra el modal del contenido
  async closePublicationModal(postContent: any) {
    this.closeBottomBtn = false
    this.closeTopBtn = false

    if (postContent.type === 'vimeo') {
      if (this.vimeoPlayer) {
        try {
          await this.vimeoPlayer.pause();
        } catch (error) {
          console.error("Error al pausar el video", error);
        }
      }
    }

    await this.publicationModal.dismiss();
  }

  async signOut() {
    await this.authService.signOut();
  }

  async contactoWhatsapp () {
    await this.updateUser.contactoRepresentanteWhatsapp(this.userInStorage.uid);
  }


  async open3DModel(modelUrl: any) {
    if (!modelUrl.includes('http')) {

      const url =  `https://sketchfab.com/models/${modelUrl}/embed?autostart=1&amp;preload=1&amp;ui_controls=1&amp;ui_infos=1&amp;ui_inspector=1&amp;ui_stop=1&amp;ui_watermark=1&amp;ui_watermark_link=1`
      console.log('URL DEL MODELO', url)
      await Browser.open({ url , toolbarColor: '#ff2836' });
    } else {
      await Browser.open({ url: `${modelUrl}` });

    }

  }



  async alertaNotificaciones() {
    console.log('Alerta Solicitada...');

    const alert = await this.alertController.create({
      cssClass: 'solicitarCita',
      header: `${this.userInStorage.nombre}`,
      subHeader: 'Permitenos la oportunidad de entregarte contenido de calidad',
      message: ` requerimos que nos otorgues permiso para enviarte notificaciones, solo te enviaremos notificaciones cuando algo relevante para tu especialidad medica sea publicado`,
      buttons: [
        {
          text: 'Aún no',
          role: 'cancel',
          handler: () => {
            console.log('Alert canceled');
          },
        },
        {
          text: 'Adelante',
          handler: async (  ) => {
            await this.pns.requestPermissions();
            await this.pns.subscribeToTopic(this.categoriaSanfer.slug)
          }
        }]
    });

    await alert.present();
  }

  async alertaNotificacionesNegadas() {

    const alert = await this.alertController.create({
      cssClass: 'solicitarCita',
      header: `${this.userInStorage.nombre}`,
      subHeader: 'Detectamos que no contamos con tu permiso',
      message: 'de enviarte notificaciones, permitenos la oportunidad de demostrarte que las notificaciones seran de tu agrado, solo da click en adelante y activa las notificaciones',
      buttons: [
        {
          text: 'Aún no',
          role: 'cancel',
          handler: () => {
            console.log('Alert canceled');
          },
        },
        {
          text: 'Adelante',
          handler: async (  ) => {
            this.openAppSettings();
          }
        }]
    });

    await alert.present();
  }

  openAppSettings() {
    NativeSettings.open({
      optionAndroid: AndroidSettings.ApplicationDetails,
      optionIOS: IOSSettings.App
    })
  }

  async requestPermissions() {
    if (Capacitor.getPlatform() === "web") {
    } else {
      setTimeout(async () => {
        // console.log('Solicitando permiso...');

        await this.alertaNotificaciones();
      }, 6000);
    }


  }



  async verificarNotificaciones (e: Event) {
    const pemisionStatus = await this.pns.checkPermissions();
    console.log('Estado del permiso: ', pemisionStatus)

    switch (pemisionStatus) {
      case 'granted':
        this.notifications = await this.pns.getDeliveredNotifications();
        console.log('Tema', this.categoriaSanfer.value)
        // const notificationTopic = await this.pns.subscribeToTopic(this.categoriaSanfer);
        // console.log('Topic notification', notificationTopic)
        await this.pns.subscribeToTopic(this.categoriaSanfer.slug)
        const roles = ['representante', 'administrador', 'productividad', 'gerente', 'empleado'];
        if (roles.includes(this.userInStorage.role)) {
          await this.pns.subscribeToTopic('empleados');
        }

        await this.openNotifications(e);
        break;
      case 'denied':
        await this.alertaNotificacionesNegadas();
        break;
      case 'prompt':
        await this.alertaNotificaciones();
        break;
    }
  }


  async verificacionInicialNotificaciones () {
    const pemisionStatus = await this.pns.checkPermissions();
    switch (pemisionStatus) {
      case 'granted':
        this.notifications = await this.pns.getDeliveredNotifications();
        await this.pns.subscribeToTopic(this.categoriaSanfer.slug)
        const roles = ['representante', 'administrador', 'productividad', 'gerente', 'empleado'];
        if (roles.includes(this.userInStorage.role)) {
          await this.pns.subscribeToTopic('empleados');
        }
        break;
      case 'denied':
        await this.alertaNotificacionesNegadas();
        break;
      case 'prompt':
        await this.alertaNotificaciones();
        break;
    }
  }

  async getToken(){
    console.log('Tema', this.categoriaSanfer.categoria)

    this.token = await this.pns.getToken();
    console.log('Token: ', this.token)


  }

  async openNotifications(e: Event) {
    this.notifications = await this.pns.getDeliveredNotifications();

    const popover = await this.popoverController.create({
      component: NotificationsPage,
      event: e,
      size: 'auto'
    });

    await popover.present();

    const { role } = await popover.onDidDismiss();
    console.log(`Popover dismissed with role: ${role}`);
    this.notifications = await this.pns.getDeliveredNotifications();

  }

  async openEmployeeMenu() {
    console.log('menu secundario')
    await this.menuCtrl.open('employeeMenu');
  }

  async closeEmployeeMenu() {
    await this.menuCtrl.close('employeeMenu');

  }



  async seleccionarEspecialidad() {
    await this.widgetUtilService.presentLoadingMessage('Estamos cargando las categorias')
    await this.closeEmployeeMenu();
    this.especialidadesSanfer = await this.content.getCategory(500);
    console.log('Especialidad Sanfer: ', this.especialidadesSanfer)
    await this.widgetUtilService.dismissLoader()
    await this.modalEspecialidades.present()


  }


  async seleccionarEspecialidadRefresh(event: any) {
    await this.widgetUtilService.presentLoadingMessage('Estamos cargando las categorias')
    await this.closeEmployeeMenu();
    this.especialidadesSanfer = await this.content.getCategory(500);
    await this.widgetUtilService.dismissLoader()
    await this.modalEspecialidades.present()

    event.target.complete();



  }






  async verifySanferEmployeeData(especialidad: any, categoryId: string) {
    this.showSpinner = true;
    await this.widgetUtilService.presentLoadingMessage('Actualizando información...')

    await Preferences.set({
      key: 'especialidadBanner',
      value: JSON.stringify(especialidad)
    });
    const user = await this.content.getSanferEmployeeData(categoryId);
    this.verifyAndLog('user', user);

    const vademecum = await this.content.getVademecum(350);
    this.verifyAndLog('vademecum', vademecum);

    const models = await this.content.get3dModels(300);
    this.verifyAndLog('models', models);

    const news = await this.content.getNews(300);
    this.verifyAndLog('news', news);

    const academy = await this.content.getAcademy(300);
    this.verifyAndLog('academy', academy);

    await this.getUserContent();
    await this.widgetUtilService.dismissLoader()
    this.showSpinner = false;
    // await this.widgetUtilService.dismissLoader();
  }

  verifyAndLog(key, value) {
    if (this.isValid(value)) {
      console.log(`${key} tiene un valor válido.`);
      this[`${key}Verified`] = true;
    } else {
      console.log(`${key} es null, undefined, o vacía.`);
    }
  }

  isValid(value) {
    return value !== null && value !== undefined && value !== '';
  }

  resetVerifyData() {
    this.showSpinner = false;
    this.userVerified = false;
    this.newsVerified = false;
    this.academyVerified = false;
    this.vademecumVerified = false;
    this.modelsVerified = false;
  }

  async loadCategory(especialidad: any) {
    this.resetVerifyData();
    console.log(especialidad);
    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: especialidad.categoria,
      // subHeader: `${email}`,
      message: `Estas por seleccionar ${especialidad.categoria}, la informacion de esta categoria sera cargada en tu perfil `,
      buttons: [
        {
          text: 'Seleecionar otra',
          handler: async () => {
            // console.log('Recuperar contraseña');
            await this.alertController.dismiss()

          }
        },
        {
          text: 'Cargar información',
          handler: async () => {
            console.log('verifySanfer: ', especialidad.id);
            this.especialidadSeleccionada = especialidad.categoria
            await this.alertController.dismiss()
            await this.modalEspecialidades.dismiss()
            await this.modalContentLoading.present();
            await this.pns.unsubscribeFromTopic(this.categoriaSanfer.slug)
            await this.verifySanferEmployeeData(especialidad, especialidad.id);
            await this.pns.subscribeToTopic(this.categoriaSanfer.slug)
            await this.modalContentLoading.dismiss();



          }
        }
      ]
    });
    await alert.present();

    // const { role } = await alert.onDidDismiss();


  }

  buscar( event: any ) {
    if(event.detail.value == '' || null) {
      console.log('busqueda no realizada')
    } else {
      this.textoBuscar = event.detail.value;
    }

  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.windowWidth = event.target.innerWidth;
  }

}
