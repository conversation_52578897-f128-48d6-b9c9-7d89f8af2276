<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      Tab 1
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
<!--  <ion-header collapse="condense">-->
<!--    <ion-toolbar>-->
<!--      <ion-title size="large">Tab 1</ion-title>-->
<!--    </ion-toolbar>-->
<!--  </ion-header>-->

<!--  <app-explore-container name="Tab 1 page"></app-explore-container>-->


  <ion-button routerLink="/login">
    Login
  </ion-button>
  <ion-button routerLink="/signup">
    Signup
  </ion-button>

  <ion-button routerLink="/inicio">
    inicio
  </ion-button>

  <qrcode [qrdata]="'Your data string'" [width]="256" [errorCorrectionLevel]="'M'"></qrcode>

</ion-content>
