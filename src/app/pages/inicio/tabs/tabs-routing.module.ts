import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabsPage } from './tabs.page';
import { authGuard } from "../../../guards/auth.guard";

const routes: Routes = [
  {
    path: '',
    component: TabsPage,
    children: [
      {
        path: 'inicio',
        loadChildren: () => import('../../../pages/inicio/inicio.module').then(m => m.InicioPageModule),
        canActivate: [authGuard]
      },
      {
        path: 'recomendaciones',
        loadChildren: () => import('../../../pages/recomendadciones/recomendadciones.module').then(m => m.RecomendadcionesPageModule)
      },
      {
        path: 'historial',
        loadChildren: () => import('../historial/historial.module').then(m => m.HistorialPageModule)
      },
      {
        path: 'profile',
        loadChildren: () => import('../../../pages/profile/profile.module').then(m => m.ProfilePageModule)
      },
      {
        path: '',
        redirectTo: '/inicio',
        pathMatch: 'full'
      }
    ]
  },
  // La ruta redundante ha sido eliminada
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
})
export class TabsPageRoutingModule {}
