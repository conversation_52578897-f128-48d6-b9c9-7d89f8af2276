<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button color="primary" text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>

    <ion-title slot="" color="primary">Ingresar</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-grid fixed style="height: 100%">
    <ion-row class="ion-justify-content-center ion-align-items-center" style="height: 100%">
      <ion-col size="12" size-md="6" size-lg="6">
        <ion-card class="card">
          <div style="padding-top: 20px" class="small_text ion-text-center">
            ¿Aun no tienes cuenta?<br /><a routerLink="/signup">Registrate aquí</a>
          </div>
          <ion-card-header>
            <ion-card-title class="ion-text-center">Iniciar sesión</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <form [formGroup]="loginForm">
              <ion-item>
                <ion-icon slot="start" name="mail" color="primary"></ion-icon>
                <ion-input
                  label-placement="floating"
                  type="email"
                  formControlName="email"
                  label="Email"
                ></ion-input>
              </ion-item>
              <div class="error-message">{{ formError.email }}</div>

              <ion-item>
                <ion-icon slot="start" name="key" color="primary"></ion-icon>
                <ion-input
                  label-placement="floating"
                  label="Contraseña"
                  (keyup.enter)="loginWithEmailPassword(email.value, password.value)"
                  type="password"
                  formControlName="password"
                ></ion-input>
              </ion-item>
              <div class="error-message">{{ formError.password }}</div>

              <ion-button
                expand="block"
                class="ion-margin-top"
                [disabled]="loginForm.invalid"
                (click)="loginWithEmailPassword(email.value, password.value)"
              >
                <ion-spinner name="dots" slot="start" *ngIf="showLoginSpinner"></ion-spinner>
                <ion-icon name="log-in" slot="end"></ion-icon>
                Ingresar
              </ion-button>
            </form>

            <!-- Botón para iniciar sesión con HCA -->
            <ion-button
              expand="block"
              color="light"
              class="ion-margin-top"
              (click)="loginWithHCA()"
            >
              <ion-icon name="key" slot="start"></ion-icon>
              Ingresar con HCA
            </ion-button>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
