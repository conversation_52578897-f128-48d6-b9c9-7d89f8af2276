import { Component, OnInit } from '@angular/core';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {Router} from "@angular/router";


// Firebase Dependencies
import {Firestore, doc, docData, collection, getDoc, writeBatch} from '@angular/fire/firestore';
import {
  deleteUser,
  getAuth,
  OAuthProvider,
  signInWithPopup,
  initializeAuth,
  signInWithEmailAndPassword,
  indexedDBLocalPersistence,
  EmailAuthProvider,
  signInWithCredential, signInWithRedirect, getRedirectResult
} from '@angular/fire/auth';


// import {
//   getAuth,
//   GoogleAuthProvider,
//   OAuthProvider,
//   PhoneAuthProvider,
//   signInWithCredential,
//   EmailAuthProvider,
//   signOut,
// } from 'firebase/auth';



// Capawesome
import {FirebaseFirestore} from "@capacitor-firebase/firestore";
import {LOGIN} from "../../interfaces/formValidationMessage";
import {HelperService} from "../../services/helper.service";




import { Capacitor } from '@capacitor/core';
import {getApp, initializeApp} from "@angular/fire/app";
import {FirebaseAuthentication, AuthStateChange, AuthCredential} from "@capacitor-firebase/authentication";
import {FirebaseAuthenticationService} from "../../services/firebase-authentication.service";
import {Preferences} from "@capacitor/preferences";
import {StrapiService} from "../../services/strapi.service";
import {AlertController} from "@ionic/angular";
import {WidgetUtilService} from "../../services/widget-util.service";
import {environment} from "../../../environments/environment";
import {AnalyticsService} from "../../services/analytics.service";
import { SegmentService } from '../../services/segment.service';
import { AnalyticsBrowser } from '@segment/analytics-next';




@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {

  loginForm: any = FormGroup;
  email: any = FormControl;
  password: any = FormControl;
  formError: any = {
    email: '',
    password: ''
  };


  showLoginSpinner = false;
  validationMessage: any = LOGIN;

  version: number = environment.version;
  segmentAnalytics: AnalyticsBrowser;


  constructor(
    private router: Router,
    private helperService: HelperService,
    private fas: FirebaseAuthenticationService,
    private strapi: StrapiService,
    public alertController: AlertController,
    private widgetUtilService: WidgetUtilService,
    private analyticsService: AnalyticsService,
    private segmentService: SegmentService
  ) {
    this.segmentAnalytics = this.segmentService.getAnalytics();

  }


  async ngOnInit() {
    this.createFormControl();
    this.createForm();
    await this.analyticsService.setCurrentScreen('Login')

  }

  /**
   * Logs in a user with the provided email and password.
   *
   * @param {string} email - The email address of the user.
   * @param {string} password - The password of the user.
   * @returns {Promise<void>} - A promise that resolves once the user is logged in successfully.
   */
  async loginWithEmailPassword(email: string, password: string) {
    try {
      const nativeSignInResult =
        await FirebaseAuthentication.signInWithEmailAndPassword({
          email, password
        });
      const credential = EmailAuthProvider.credential(email, password);
      // console.log('Obtain Credential 1 Firebase ', credential)
      const auth = getAuth();
      const result = await signInWithCredential(auth, credential);
      await this.obtainUserInfo(result)

    } catch (error: any) {
      console.log('Error', error);
      this.showLoginSpinner = false;
      // Handle other error cases...
      if (error.message === 'Error: There is no user record corresponding to this identifier. The user may have been deleted.') {
        console.log(`Mensaje de error: ${error.message} y codigo de error: ${error.code}`);
        await this.correoNoEncontrado(this.email.value);
      }
      console.log(`Mensaje de error: ${error.message} y codigo de error: ${error.code}`);
      if (error.message === 'Error: The password is invalid or the user does not have a password.') {
        console.log(`Mensaje de error: ${error.message} y codigo de error: ${error.code}`);
        await this.passwordEquivocado(this.email.value);
      }
      if (error.message !== 'Error: The password is invalid or the user does not have a password.'
        && error.message !== 'Error: There is no user record corresponding to this identifier. The user may have been deleted.') {

        await this.widgetUtilService.presentToastError(`HEY Mensaje de error: ${error.message} y codigo de error: ${error.code}`);

      }
    }

  }




  async loginWithHCA() {
    try {
      const auth = getAuth();
      const provider = new OAuthProvider('oidc.onekey');

      console.log('Auth: ', auth, ' Provider:', provider)
      // 1. Configuración de scopes REQUERIDOS
      provider.addScope('openid');
      provider.addScope('https://auth.onekeyconnect.com/x/profile.basic');


      this.showLoginSpinner = true;


      // 2. Autenticación con popup (única llamada)
      const result = await signInWithRedirect(auth, provider);

      console.log('Result: ', result)
      // 3. Procesar credenciales
      const credential = OAuthProvider.credentialFromResult(result);
      if (!credential) {
        throw new Error('No se pudo obtener la credencial');
      }

      console.log('Token de acceso:', credential.accessToken);
      console.log('Token de ID:', credential.idToken);

      // 4. Obtener información del usuario
      // const user = result.user;
      // const idTokenResult = await user.getIdTokenResult(true);
      //
      // console.log('Usuario:', user);
      // console.log('Claims:', idTokenResult.claims);
      //
      // // 5. Guardar datos y navegar
      // await this.storeUserData(user);
      await this.router.navigate(['/welcome'], { replaceUrl: true });

    } catch (error: any) {
      console.error('Error completo:', error);

      // 6. Manejo específico de errores
      if (error.code === 'auth/popup-blocked') {
        await this.widgetUtilService.presentToastError(
          'Permite ventanas emergentes para autenticarte'
        );
      } else if (error.code === 'auth/cancelled-popup-request') {
        await this.widgetUtilService.presentToastError(
          'Solicitud cancelada por el usuario'
        );
      } else {
        await this.widgetUtilService.presentToastError(
          `Error de autenticación: ${error.message || 'Error desconocido'}`
        );
      }
    } finally {
      this.showLoginSpinner = false;
    }
  }

  /**
   * Store user data locally
   */
  async storeUserData(user: any) {
    const dataToStore = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
    };

    await Preferences.set({
      key: 'user',
      value: JSON.stringify(dataToStore),
    });

    console.log('Datos del usuario guardados:', dataToStore);
  }




  // /**
  //  * Obtain user information.
  //  *
  //  * @param {any} data - The user data.
  //  * @return {Promise<void>} - A Promise that resolves when the user information is obtained.
  //  */
  // async obtainUserInfo (data: any) {
  //   const medicoDocument = `medicos/${data.user.uid}`;
  //   const representanteDocument = `representantes/${data.user.uid}`;
  //   const medicoSnapshot = await FirebaseFirestore.getDocument({reference: medicoDocument});
  //   if (medicoSnapshot.snapshot) {
  //     console.log('Se detecto medico en el login', medicoSnapshot.snapshot)
  //     const dataDelMedico = medicoSnapshot.snapshot.data;
  //     if (dataDelMedico !== null && Object.keys(dataDelMedico).length === 0) {
  //        const representanteSnapshot = await FirebaseFirestore.getDocument({reference: representanteDocument});
  //        if (representanteSnapshot.snapshot) {
  //         const dataDelRepresentante = representanteSnapshot.snapshot.data;
  //         if (dataDelRepresentante !== null && Object.keys(dataDelRepresentante).length === 0) {
  //           await this.clearStorage();
  //           await this.handleUserDeletion();
  //         } else {
  //           await this.setUserDataInStorage(dataDelRepresentante, data.user.uid);
  //         }
  //       } else {
  //         await this.clearStorage();
  //         await this.handleUserDeletion();
  //       }
  //     } else {
  //      await this.setUserDataInStorage(dataDelMedico, data.user.uid);
  //     }
  //   } else {
  //     const representanteSnapshot: any = await FirebaseFirestore.getDocument({reference: representanteDocument});
  //     if (representanteSnapshot.snapshot) {
  //       console.log('Se detecto representante en el login', representanteSnapshot.snapshot)
  //
  //       const dataDelRepresentante = representanteSnapshot.snapshot.data;
  //       if (dataDelRepresentante !== null && Object.keys(dataDelRepresentante).length === 0) {
  //         await this.clearStorage();
  //         await this.handleUserDeletion();
  //       } else {
  //         await this.setUserDataInStorage(dataDelRepresentante, data.user.uid);
  //       }
  //     } else {
  //       await this.clearStorage();
  //       await this.handleUserDeletion();
  //     }
  //   }
  // }


  async obtainUserInfo(data: any) {
    // Identificadores de documentos para médico y representante basados en el uid del usuario.
    const paths = {
      medico: `medicos/${data.user.uid}`,
      representante: `representantes/${data.user.uid}`
    };

    // Función auxiliar para obtener datos del documento y manejar la lógica común.
    const fetchDataAndHandle = async (path: string) => {
      const snapshot = await FirebaseFirestore.getDocument({ reference: path });
      if (snapshot.snapshot) {
        let userData: any = snapshot.snapshot.data;
        if (userData !== null && Object.keys(userData).length > 0) {
          // Verifica si el campo 'Registro' existe y está en mayúsculas
          if (userData.hasOwnProperty('Registro')) {
            // Cambia 'Registro' a 'registro' y elimina el campo 'Registro'
            userData = {
              ...userData,
              registro: userData.Registro,
            };
            delete userData.Registro; // Elimina el campo original 'Registro'
          }
          await this.setUserDataInStorage(userData, data.user.uid);
          return true; // Datos válidos encontrados y manejados.
        }
      }
      return false; // No se encontraron datos válidos.
    };

    // Intenta obtener y manejar los datos del médico.
    const medicoHandled = await fetchDataAndHandle(paths.medico);
    if (!medicoHandled) {
      // Si no se manejan los datos del médico, intenta con el representante.
      const representanteHandled = await fetchDataAndHandle(paths.representante);
      if (!representanteHandled) {
        // Si no se encuentran datos válidos ni para el médico ni para el representante, limpia y maneja la eliminación.
        await this.clearStorage();
        await this.handleUserDeletion();
      }
    }
  }


  private async handleUserDeletion() {

    const auth = getAuth();
    if (auth.currentUser) {
      await deleteUser(auth.currentUser);
      window.alert('Algo en tu registro no salio bien, intenta registrarte nuevamente');
      await this.router.navigate(['/home'], { replaceUrl: true });

    }
  }

  async clearStorage() {
    await Preferences.clear();
  }
  async setUserDataInStorage( userData: any, uid: string ) {
    console.log('Login Init set user in Storage 1 USERDATA RAW', userData);

    const dataToStore = {
      uid,
      ...userData,
      // Puedes seguir agregando otras propiedades aquí si es necesario
      // version: this.version,
    };

    await Preferences.set({
      key: 'user',
      value: JSON.stringify(dataToStore)
    });

    this.showLoginSpinner = false;
    await this.widgetUtilService.presentToast(`Bienvenido ${userData.nombre} Estas loggeado'`);
    this.resetForm();

    const platform = Capacitor.getPlatform();
    console.log('Platform log', platform);

    try {
      if (userData.role === 'medico') {
        // Google Analytics
        console.log(uid, {
          name: `${userData.nombre} ${userData.apellido}`,
          email: userData.email,
          channel: platform,
          oneKey: userData.onekey_id,
          titulo: userData.titulo,
          role: userData.role,
          cedula: userData.cedula
        });

        this.analyticsService.logEvent('login', {
          method: 'password',
          uuid: uid,
          userId: uid,
          oneKey: userData.onekey_id,
          email: userData.email,
          titulo: userData.titulo,
          channel: platform
        });

        // Llamada a la función independiente para identificar al usuario con Segment
        const traits = {
          name: `${userData.nombre} ${userData.apellido}`,
          email: userData.email,
          channel: platform,
          oneKey: userData.onekey_id,
          titulo: userData.titulo,
          role: userData.role,
          cedula: userData.cedula
        };

        this.identifyUserWithSegment(this.segmentAnalytics, uid, traits);

      } else {
        // Google Analytics para empleados
        this.analyticsService.logEvent('login empleado', {
          method: 'password',
          uuid: uid,
          userId: uid,
          email: userData.email,
          nEmpleado: userData.nEmpleado,
          channel: platform
        });

        // Llamada a la función independiente para identificar al empleado con Segment
        const traits = {
          name: `${userData.nombre} ${userData.apellido}`,
          email: userData.email,
          channel: platform,
          nEmpleado: userData.nEmpleado,
          role: userData.role
        };
        this.identifyUserWithSegment(this.segmentService, uid, traits);

      }
    } catch (error) {
      console.error('Error during login process:', error);
      this.widgetUtilService.presentToast('Error durante el proceso de inicio de sesión.');
    }




    await this.router.navigate(['/welcome'], { replaceUrl: true });
  }

  identifyUserWithSegment(segmentAnalytics: any, uid: string, traits: any) {

    segmentAnalytics.identify(uid, traits)

  }







  async passwordEquivocado(email: string) {
    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: '¿Olvidaste tu contraseña?',
      subHeader: `${email}`,
      message: `Has intentado ingresar con el correo ${email}, ` +
        'Si olvidaste tu contraseña da click en recuperar mi contraseña, pero si la recuerdas y crees haberte equivocado solo intentalo nuevamente',
      buttons: [
        {
          text: 'Recuperar mi contraseña',
          handler: async () => {
            // console.log('Recuperar contraseña');
            this.router.navigate(['/recover', email]);

          }
        },
        {
          text: 'Intentarlo nuevamente',
          handler: async () => {
            // console.log('Ingresar password nuevamente');

          }
        }
      ]
    });
    await alert.present();

    const { role } = await alert.onDidDismiss();
  }


  async correoNoEncontrado(email: string) {
    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: 'Correo no encontrado',
      subHeader: `${email}`,
      message: `No tenemos registro del correo proporcionado ${email}, ` +
        'asegurate de que está correctamente estrito, en caso de que así lo sea entonces no tenemos registro tuyo ' +
        'y necesitas registrarte, para ello da click en registrarme, si encontraste un error en tu correo por favor da click en ' +
        'corregirlo para intentar ingresar nuevamente',
      buttons: [
        {
          text: 'Mi correo esta correcto',
          handler: async () => {
            console.log('El correo esta bien, se redirige al registro médico');
            this.router.navigate(['/signup']);

          }
        },
        {
          text: 'Me equivoque al escribirlo',
          handler: async () => {
            console.log('Se equivoco al escribirlo');

          }
        }
      ]
    });
    await alert.present();

    const { role } = await alert.onDidDismiss();
  }
  recover() {
    // console.log('Boton de recuperacion');
    try {
      this.router.navigate(['/recover', this.email.value]);

    } catch (e) {
      console.log('Error', e);

    }
  }




  createFormControl() {
    this.email = new FormControl('',[
      Validators.required,
      Validators.email
    ]);
    this.password = new FormControl('',[
      Validators.required,
      Validators.minLength(5)
    ]);
  }
  createForm() {
    this.loginForm = new FormGroup({
      email: this.email,
      password: this.password
    });
    this.loginForm.valueChanges.subscribe((data: any) => this.onFormValueChanged(data));
  }

  onFormValueChanged(data: any) {

    this.formError = this.helperService.prepareValidationMessage(this.loginForm, this.validationMessage, this.formError);

  }

  resetForm() {
    this.loginForm.reset();
    this.formError = {
      email: '',
      password: ''
    };
  }

}
