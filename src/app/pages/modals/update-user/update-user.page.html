<ion-header >
  <ion-toolbar color="primary">
    <ion-title>Actualización requerida</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-grid>
    <ion-row>
      <ion-col class="ion-text-center">

        <h3>Apoyenos con unas preguntas para mejorar su experiencia en la aplicacion</h3>
        <ion-list>
          <p>Durante su registro nos proporciono la cedula {{ userInStorage.cedula }} correspondiente al titulo: {{ userInStorage.titulo }}</p>

          <ion-radio-group (ionChange)="tipoCedula($event)">
            <ion-item>
              <ion-radio value="general">Es mi cedula general</ion-radio>
            </ion-item>
            <ion-item>
              <ion-radio value="especialidad">Es mi cedula de especialidad</ion-radio>
            </ion-item>
          </ion-radio-group>


          <p *ngIf="cedulaGeneral">¿Cuenta con cedula de especialidad?</p>

          <ion-radio-group (ionChange)="cuentaConCedulaEspecialidad($event)" *ngIf="cedulaGeneral">
            <ion-item>
              <ion-radio value="true">Si</ion-radio>
            </ion-item>
            <ion-item>
              <ion-radio value="false">No</ion-radio>
            </ion-item>
          </ion-radio-group>

          <p *ngIf="siTieneCedulaEspecialidad">Proporcionenos su cédula de especialidad</p>

          <ion-item *ngIf="siTieneCedulaEspecialidad">
            <ion-input
              type="tel"
              label="Cedula de especialidad"
              labelPlacement="stacked"
              [(ngModel)]="cedulaEspecialidad"
              [clearOnEdit]="true"
              [clearInput]="true"
              placeholder="Ingreresa tu cedula de especialidad"
            >
            </ion-input>
          </ion-item>

          <p *ngIf="mostrarCampoCedulaGeneral">Por favor proporciónenos su cédula general</p>

          <ion-item *ngIf="mostrarCampoCedulaGeneral">
            <ion-input
              type="tel"
              label="Cédula General"
              labelPlacement="stacked"
              [(ngModel)]="cedulaEspecialidad"
              [clearOnEdit]="true"
              [clearInput]="true"
              placeholder="Ingreresa tu cedula general"
            >
            </ion-input>
          </ion-item>


          <p>Por favor proporciónenos su fecha de nacimiento</p>
          <ion-item id="fechaNacimientoTrigger">
            <ion-icon name="calendar" color="primary" slot="start" size="large"></ion-icon>
            <ion-label *ngIf="!fechaNacimiento">Seleccione su fecha de nacimiento</ion-label>
            <!-- Mostrar la fecha de nacimiento seleccionada si existe -->
            <ion-label *ngIf="fechaNacimiento">{{ fechaNacimiento | date: 'longDate' }}</ion-label>
          </ion-item>

          <ion-popover trigger="fechaNacimientoTrigger" triggerAction="click">
            <ng-template>
              <ion-datetime
                presentation="date"
                [(ngModel)]="fechaNacimiento"
                (ionChange)="fechaSeleccionada($event)"
                [showDefaultButtons]="true">
              </ion-datetime>
            </ng-template>

          </ion-popover>


        </ion-list>

        <!-- Botón de Actualización con habilitación condicional -->
        <ion-button [disabled]="!puedeActualizarInformacion()" (click)="actualizarInformacion()">
          Actualizar información
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>




</ion-content>
