import {Component, Input, OnInit} from '@angular/core';
import {AngularFirestore} from "@angular/fire/compat/firestore";
import {ModalController} from "@ionic/angular";
import {AnalyticsService} from "../../../services/analytics.service";

@Component({
  selector: 'app-update-user',
  templateUrl: './update-user.page.html',
  styleUrls: ['./update-user.page.scss'],
})
export class UpdateUserPage implements OnInit {
  cedulaGeneral: boolean = false;
  siTieneCedulaEspecialidad: boolean = false;
  mostrarCampoCedulaGeneral: boolean = false;
  cedulaGeneralValor: string = '';
  fechaNacimiento: string | null = null;
  cedulaEspecialidad: string = '';
  esMayorDeEdad: boolean = false;

  @Input() userInStorage: any;
  @Input() userId: string;

  constructor(
    private firestore: AngularFirestore,
    private modalCtrl: ModalController,
    private analyticsService: AnalyticsService,

  ) { }

  async ngOnInit() {
    await this.analyticsService.setCurrentScreen('Actualizar usuario no existente en el CRM')

  }

  tipoCedula(ev: any) {
    this.cedulaGeneral = ev.detail.value === 'general';
    // Mostrar campo de Cédula General si el usuario selecciona que su cédula es de especialidad
    this.mostrarCampoCedulaGeneral = ev.detail.value === 'especialidad';
    if (!this.cedulaGeneral) {
      this.siTieneCedulaEspecialidad = false
    }
  }

  cuentaConCedulaEspecialidad(ev: any) {
    this.siTieneCedulaEspecialidad = ev.detail.value === 'true';
  }

  fechaSeleccionada(event: any) {
    this.fechaNacimiento = event.detail.value.split('T')[0];
  }


  validarMayorDeEdad(fechaNacimiento: string) {
    const fechaNacimientoDate = new Date(fechaNacimiento);
    const fechaActual = new Date();
    const diferenciaEnAños = fechaActual.getFullYear() - fechaNacimientoDate.getFullYear();
    const diferenciaEnMeses = fechaActual.getMonth() - fechaNacimientoDate.getMonth();
    const diferenciaEnDias = fechaActual.getDate() - fechaNacimientoDate.getDate();

    // Ajusta la validación basada en el mes y día de nacimiento
    if (
      diferenciaEnAños > 18 ||
      (diferenciaEnAños === 18 && diferenciaEnMeses > 0) ||
      (diferenciaEnAños === 18 && diferenciaEnMeses === 0 && diferenciaEnDias >= 0)
    ) {
      this.esMayorDeEdad = true;
    } else {
      this.esMayorDeEdad = false;
    }

    // Aquí puedes realizar acciones adicionales basadas en si el usuario es mayor de edad o no
    console.log('¿Es mayor de edad?', this.esMayorDeEdad);
  }

  actualizarInformacion() {
    if (!this.puedeActualizarInformacion()) {
      console.log('La información no se puede actualizar debido a que falta información o el usuario no es mayor de edad.');
      return;
    }

    const tipoCedulaSeleccionada = this.cedulaGeneral ? 'general' : 'especialidad';

    const usuarioActualizado = {
      fechaNacimiento: this.fechaNacimiento,
      cedulaGeneral: this.cedulaGeneralValor,
      cedulaEspecialidad: this.cedulaEspecialidad,
      rutas: null,
      tipoCedula: tipoCedulaSeleccionada, // Agrega el campo `tipoCedula`

      // Añade aquí más campos según sea necesario
    };

    this.firestore.collection('medicos').doc(this.userId).update(usuarioActualizado)
      .then(() => {
        console.log('Información del usuario actualizada con éxito');
        this.modalCtrl.dismiss(true); // Cierra el modal y pasa `true`

      })
      .catch(error => {
        console.error('Error actualizando la información del usuario:', error);
      });
  }
  puedeActualizarInformacion(): boolean {
    // Asegura que el método siempre retorne un valor booleano.
    return !!this.fechaNacimiento &&
      (this.cedulaGeneral ||
        (this.mostrarCampoCedulaGeneral && !!this.cedulaGeneralValor));
  }

}
