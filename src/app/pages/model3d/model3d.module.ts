import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { Model3dPage } from './model3d.page';

const routes: Routes = [
  {
    path: '',
    component: Model3dPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [Model3dPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class Model3dPageModule {}
