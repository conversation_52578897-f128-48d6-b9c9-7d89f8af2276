<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let sub of subCategoria">{{sub.Subcategoria}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>



  <ion-grid>
    <ion-row>
      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
      <ion-col size="12" size-xl="8" size-lg="8" size-md="10" size-sm="12" size-xs="12">
        <ion-row >
      <ion-col class="ion-no-padding" size="12" size-xl="3" size-lg="4" size-md="4" size-sm="4" size-xs="4"  *ngFor="let modelo of subCategoria">
<!--        <ion-card (click)="openArticle(modelo.Identificador, modelo.url, modelo.Nombre, strapiUrl, modelo.Imagen_principal.formats.thumbnail.url)" color="primary">-->
<!--          <ion-img *ngIf="modelo.Imagen_principal"  src="{{strapiUrl}}{{modelo.Imagen_principal.url}}"></ion-img>-->
<!--          <ion-card-header class="ion-no-margin ion-no-padding">-->
<!--            <h3 class="ion-text-center " >{{modelo.Nombre}}</h3>-->
<!--          </ion-card-header>-->
<!--        </ion-card>-->

        <ion-card color="primary" button="true"   (click)="openModel(modelo)"  class="medicamentoSugerido" mode="md">
          <div class="imagen-producto">
            <ion-img *ngIf="modelo.Imagen_principal"  src="{{strapiUrl}}{{modelo.Imagen_principal.url}}"></ion-img>

          </div>
          <ion-card-header>
            <ion-card-title color="primary" *ngIf="modelo.Nombre" class="title-container">
              <span class="title-text" >{{modelo.Nombre}}</span>
              <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
            </ion-card-title>
          </ion-card-header>

        </ion-card>
      </ion-col>
        </ion-row>
      </ion-col>

      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
    </ion-row>
  </ion-grid>


</ion-content>
