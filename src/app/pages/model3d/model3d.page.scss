//ion-card{
//  margin: 0;
//  padding: 0;
//}
h3 {
  font-size: 15px;
}

.small-text-category {
  font-size: 14px;
}



.title-container {
  display: flex; // Flexbox layout
  align-items: center; // Vertical center alignment
  justify-content: space-between; // Space between text and icon
  height: 3em; // Adjust this based on your font-size to fit two lines

  .title-text {
    flex-grow: 1; // Allow text to take up available space
    overflow: hidden; // Prevent overflow
    text-overflow: ellipsis; // Add ellipsis for overflow text
    display: -webkit-box; // For line clamping
    -webkit-line-clamp: 2; // Clamp text at two lines
    -webkit-box-orient: vertical;
  }

  .title-icon {
    padding-left: 10px; // Adjust as needed
  }
}



ion-card{

  margin: 5px;
  padding: 0;
  box-shadow:
    rgb(255 0 0 / 20%) 0px 3px 5px -2px,
    rgb(255 0 0 / 14%) 0px 3px 0px 0px,
    rgb(255 0 0 / 12%) 0px 1px 5px 1px;
  //box-shadow:none !important

}

ion-card-header{
  padding: 0 5px 0 5px;
}

ion-card-title{
  font-size: 10px;
}

ion-card-subtitle{
  margin-top: 0px;
  font-size: 8px;

}

ion-card-content {
  font-size: 10px;
  padding: 5px;

}


