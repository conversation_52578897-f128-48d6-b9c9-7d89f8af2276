import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { Model3dPage } from './model3d.page';

describe('Model3dPage', () => {
  let component: Model3dPage;
  let fixture: ComponentFixture<Model3dPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ Model3dPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(Model3dPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
