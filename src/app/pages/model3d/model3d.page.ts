import { Component, OnInit } from '@angular/core';
import {environment} from '../../../environments/environment';
import {RespuestaModelos3D, Subcategoria, Modelo3D} from '../../interfaces/modelos3dInterfaces';
import {StrapiService} from '../../services/strapi.service';
import {ActivatedRoute, Router} from '@angular/router';
import {Firestore, doc, docData, collection, getDoc, writeBatch, addDoc} from '@angular/fire/firestore';
import { Browser } from '@capacitor/browser';
import {serverTimestamp} from '@angular/fire/database';
import {Preferences} from "@capacitor/preferences";
import {WidgetUtilService} from "../../services/widget-util.service";
import {DataService} from "../../services/data.service";
import {SpringerResponse} from "../../models/springer.model";
import {InteractionService} from "../../services/interaction.service";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-model3d',
  templateUrl: './model3d.page.html',
  styleUrls: ['./model3d.page.scss'],
})
export class Model3dPage implements OnInit {

  category: string
  id: string;
  slug: string;
  strapiUrl = environment.strapiURL;
  // modelos: any;
  subCategoria: Subcategoria[] = [];
  subId = '';
  nombreCategoria: string;

  user: any;
  uid: any;

  valor1: any;
  valor: any;
  inde: number;

  // data: any;
  constructor(private strapi: StrapiService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              // private db: AngularFirestore,
              private firestore: Firestore,
              private widgetUtilService: WidgetUtilService,
              private data: DataService,
              private interaction: InteractionService,
              private analyticsService: AnalyticsService


  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      console.log('resultado de id', result);
      this.category = result.category;

      this.slug = result.slug;


    });

  }


  async ngOnInit() {
    // const ret = await Preferences.get({ key: 'user' });
    // this.user = JSON.parse(ret.value);
    // this.uid = this.user.uid;

    await this.widgetUtilService.presentLoading();
    await this.getUserInStorage();

    await this.getSubcategoryData();


    await this.widgetUtilService.dismissLoader();
    await this.analyticsService.setCurrentScreen(`modelos-anatomicos/${this.category}/${this.slug}`);


    // console.log('Respuesta', this.subCategoria);

  }

  async setInteraction(model: any) {


    const data= {
      titulo: model.Nombre,
      imagen: model.Imagen_principal.url,
      categoria: this.category,
      slug: this.slug,
      type: 'modelo-3d'
    }
    await this.interaction.registrarInteraccion(this.uid, `https://sketchfab.com/models/${model.Identificador}/embed?autostart=1&amp;preload=1&amp;ui_controls=1&amp;ui_infos=1&amp;ui_inspector=1&amp;ui_stop=1&amp;ui_watermark=1&amp;ui_watermark_link=1`, data);
  }



  async openModel(data: any) {
    await this.setInteraction(data);

    // openArticle(id, url, nombre, strapi, img)
    // modelo.Identificador, modelo.url, modelo.Nombre, strapiUrl, modelo.Imagen_principal.formats.thumbnail.url
    // console.log('Respuesta de la URL', url);
    if (data.url === null) {


      await Browser.open({ url: `https://sketchfab.com/models/${data.Identificador}/embed?autostart=1&amp;preload=1&amp;ui_controls=1&amp;ui_infos=1&amp;ui_inspector=1&amp;ui_stop=1&amp;ui_watermark=1&amp;ui_watermark_link=1`, toolbarColor: '#ff2836' });

      // this.userLog(id, `https://sketchfab.com/models/${id}/embed?autostart=1&amp;preload=1&amp;ui_controls=1&amp;ui_infos=1&amp;ui_inspector=1&amp;ui_stop=1&amp;ui_watermark=1&amp;ui_watermark_link=1`, nombre, strapi + img);
      await this.analyticsService.logEvent('select_content', {url: `https://sketchfab.com/models/${data.Identificador}`});

    } else {

      await Browser.open({ url: `${data.url}` });
      await this.analyticsService.logEvent('select_content', {url: `${data.url}`});


      // this.userLog(id, url, nombre, strapi + img);

    }

  }

  // Version anterior de Firebase
  // async userLog(id, url, nombre, img) {
  //   if (this.user.role === 'medico') {
  //
  //     console.log(url);
  //     // console.log('Titulo', await this.noticeLog);
  //     this.db.firestore.collection('medicos').doc(this.uid).collection('historial').add({
  //       action: 'Visito el modelo 3D',
  //       date: new Date().valueOf(),
  //       model3d: true,
  //       url,
  //       id,
  //       titulo: nombre,
  //       img
  //     }).then((docRef) => {
  //       console.log('Document written with ID: ', docRef.id);
  //     });
  //   }
  //   if (this.user.role === 'representante') {
  //
  //     console.log('Representante detectado: ', url);
  //     // console.log('Titulo', await this.noticeLog);
  //     this.db.firestore.collection('representantes').doc(this.uid).collection('historial').add({
  //       action: 'Visito el modelo 3D',
  //       date: new Date().valueOf(),
  //       model3d: true,
  //       url,
  //       id,
  //       titulo: nombre,
  //       img
  //     }).then((docRef) => {
  //       console.log('Document written with ID: ', docRef.id);
  //     });
  //   }
  //
  //
  //
  //
  // }

  async getUserInStorage(){
    const ret = await Preferences.get({ key: 'user' });
    this.user = JSON.parse(ret.value);
    this.uid = this.user.uid;

  }
  async userLog(id, url, nombre, img) {
    const userRole = this.user.role; // Assuming this.user.role contains the role
    const userUid = this.uid; // Assuming this.uid contains the user's UID
    const userLogCollectionPath = userRole === 'representante' ? `representantes/${userUid}/historial` : `medicos/${userUid}/historial`;

    const logData = {
      action: 'Visito el modelo 3D',
      date: serverTimestamp(), // Use serverTimestamp for consistency
      model3d: true,
      url,
      id,
      titulo: nombre,
      img
    };

    try {
      const userLogColRef = collection(this.firestore, userLogCollectionPath);
      const docRef = await addDoc(userLogColRef, logData);
      console.log('Document written with ID: ', docRef.id);
    } catch (error) {
      console.error('Error adding document: ', error);
      // Handle the error appropriately
    }
  }




  async getSubcategoryData() {
    const modelosStorage = await Preferences.get({ key: 'modelos' });
    const modelos3D = JSON.parse(modelosStorage.value);
    const filtered = await modelos3D.filter((resp: any) => {
      return resp.slug === this.category;
    })
    console.log('Filtradas', filtered[0])

    const filtroSubcategoria = await filtered[0].Subcategoria.filter((resp: any) => {
      return resp.slug === this.slug;
    })
    const subCategoria = filtroSubcategoria[0]
    console.log('Subcategporia: ', subCategoria)

    this.nombreCategoria = subCategoria.Subcategoria
    this.subCategoria = subCategoria.Modelo3D;

    console.log('Esto es la subcategoria resultante: ', this.subCategoria)


    // this.modelos = filtered[0];
    //
    // this.nombreCategoria = this.modelos.NombreCategoria
    // this.subCategoria = this.modelos.Subcategoria;
  }








}
