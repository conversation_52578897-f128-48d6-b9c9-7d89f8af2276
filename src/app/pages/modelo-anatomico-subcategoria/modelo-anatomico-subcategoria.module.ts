import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ModeloAnatomicoSubcategoriaPage } from './modelo-anatomico-subcategoria.page';

const routes: Routes = [
  {
    path: '',
    component: ModeloAnatomicoSubcategoriaPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [ModeloAnatomicoSubcategoriaPage]
})
export class ModeloAnatomicoSubcategoriaPageModule {}
