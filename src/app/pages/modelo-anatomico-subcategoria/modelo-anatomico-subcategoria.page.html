<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{nombreCategoria}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>


<!--  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">-->
<!--    <ion-refresher-content></ion-refresher-content>-->
<!--  </ion-refresher>-->

  <ion-grid>
    <ion-row>
      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
      <ion-col size="12" size-xl="8" size-lg="8" size-md="10" size-sm="12" size-xs="12">
        <ion-row >
      <ion-col class="ion-no-padding" size="12" size-xl="3" size-lg="4" size-md="4" size-sm="4" size-xs="4"  *ngFor="let sub of subCategoria">

        <ion-card color="primary" button="true"   (click)="openArticle(sub)"  class="medicamentoSugerido" mode="md">
          <div class="imagen-producto">
            <ion-img *ngIf="sub.ImagenSubcategoria" src="{{strapiUrl}}{{sub.ImagenSubcategoria.url}}"></ion-img>

          </div>
          <ion-card-header>
            <ion-card-title color="primary" *ngIf="sub.Subcategoria" class="title-container">
              <span class="title-text" >{{sub.Subcategoria}}</span>
              <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
            </ion-card-title>
          </ion-card-header>

        </ion-card>

<!--        <ion-card (click)="openArticle(sub.id, sub.Modelo3D)" color="primary">-->
<!--          <ion-img *ngIf="sub.ImagenSubcategoria" src="{{strapiUrl}}{{sub.ImagenSubcategoria.url}}"></ion-img>-->
<!--          <ion-card-header class="ion-no-padding ion-no-margin">-->
<!--            <h3 class="ion-text-center " *ngIf="sub.Subcategoria">{{sub.Subcategoria}}</h3>-->
<!--          </ion-card-header>-->
<!--        </ion-card>-->
      </ion-col>
        </ion-row>
      </ion-col>

      <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
    </ion-row>
  </ion-grid>


</ion-content>
