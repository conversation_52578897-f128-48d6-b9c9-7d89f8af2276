import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ModeloAnatomicoSubcategoriaPage } from './modelo-anatomico-subcategoria.page';

describe('ModeloAnatomicoSubcategoriaPage', () => {
  let component: ModeloAnatomicoSubcategoriaPage;
  let fixture: ComponentFixture<ModeloAnatomicoSubcategoriaPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ModeloAnatomicoSubcategoriaPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ModeloAnatomicoSubcategoriaPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
