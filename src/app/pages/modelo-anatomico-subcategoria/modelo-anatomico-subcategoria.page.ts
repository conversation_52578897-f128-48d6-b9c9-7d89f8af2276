import { Component, OnInit } from '@angular/core';
import {environment} from '../../../environments/environment';
import {RespuestaModelos3D, Subcategoria} from '../../interfaces/modelos3dInterfaces';
import {StrapiService} from '../../services/strapi.service';
import {ActivatedRoute, Router} from '@angular/router';
import {WidgetUtilService} from "../../services/widget-util.service";
import {DataService} from "../../services/data.service";
import {Preferences} from "@capacitor/preferences";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-modelo-anatomico-subcategoria',
  templateUrl: './modelo-anatomico-subcategoria.page.html',
  styleUrls: ['./modelo-anatomico-subcategoria.page.scss'],
})
export class ModeloAnatomicoSubcategoriaPage implements OnInit {

  slug: string;
  strapiUrl = environment.strapiURL;
  modelos: any;

  nombreCategoria: string;
  subCategoria: Subcategoria[] = [];

  objeto: any;


  constructor(
    private strapi: StrapiService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private data: DataService,
    private analyticsService: AnalyticsService,

  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.slug = result.slug;
    });

  }







  async ngOnInit() {

    await this.widgetUtilService.presentLoading();

    const modelosStorage = await Preferences.get({ key: 'modelos' });
    const modelos3D = JSON.parse(modelosStorage.value);
    const filtered = await modelos3D.filter((resp: any) => {
      return resp.slug === this.slug;
    })
    console.log('Filtradas', filtered[0])
    this.modelos = filtered[0];

    this.nombreCategoria = this.modelos.NombreCategoria
    this.subCategoria = this.modelos.Subcategoria;

    await this.widgetUtilService.dismissLoader();
    await this.analyticsService.setCurrentScreen(`modelos-anatomicos/${this.slug}`);



  }

  async openArticle(data: any) {


    console.log(data);

    this.data.setData(data);
    await this.router.navigateByUrl(`/modelos-anatomicos/${this.slug}/${data.slug}`);

  }


}
