import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ModelosAnatomicosPage } from './modelos-anatomicos.page';
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";

const routes: Routes = [
  {
    path: '',
    component: ModelosAnatomicosPage
  }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes),
        SharedComponentsModule
    ],
  declarations: [ModelosAnatomicosPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class ModelosAnatomicosPageModule {}
