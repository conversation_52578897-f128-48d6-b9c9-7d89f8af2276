<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Modelos Anatómicos</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="modelosRefresh($event)" *ngIf="userInStorage.role === 'medico'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>

  <swiper-container pagination="true" slides-per-view="1"  class="ion-no-padding ion-no-margin swiperStyleSanfer" >
    <swiper-slide *ngFor="let slide of slideModelos" style="margin-bottom: 20px;">
      <ion-img *ngIf="slide.type === 'image'" (click)="openBrowser(slide.link)" [src]="strapiUrl + slide.image.url"></ion-img>
      <app-youtube-player *ngIf="slide.type === 'video'" [videoId]="slide.videoURL" [autoplay]="false"></app-youtube-player>
    </swiper-slide>
  </swiper-container>

<ion-grid>
  <ion-row>
    <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
    <ion-col size="12" size-xl="8" size-lg="8" size-md="10" size-sm="12" size-xs="12">
      <ion-row >
        <ion-col class="ion-no-padding" size="12" size-xl="3" size-lg="4" size-md="4" size-sm="4" size-xs="4"  *ngFor="let modelo of modelos">

          <ion-card color="primary" button="true"   (click)="openArticle(modelo.slug)"   class="medicamentoSugerido" mode="md">
            <div class="imagen-producto">
              <ion-img src="{{strapiUrl}}{{modelo.ImagenCategoria.url}}"></ion-img>

            </div>
            <ion-card-header>
              <ion-card-title color="primary" *ngIf="modelo?.NombreCategoria" class="title-container">
                <span class="title-text">{{ modelo.NombreCategoria }}</span>
                <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
              </ion-card-title>
            </ion-card-header>

          </ion-card>

        </ion-col>
      </ion-row>
    </ion-col>

    <ion-col size="12" size-xl="2" size-lg="2" size-md="1" size-sm="0" size-xs="0"></ion-col>
  </ion-row>
</ion-grid>

</ion-content>
