import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ModelosAnatomicosPage } from './modelos-anatomicos.page';

describe('ModelosAnatomicosPage', () => {
  let component: ModelosAnatomicosPage;
  let fixture: ComponentFixture<ModelosAnatomicosPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ModelosAnatomicosPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ModelosAnatomicosPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
