import { Component, OnInit } from '@angular/core';
import {environment} from '../../../environments/environment';
import {RespuestaModelos3D} from '../../interfaces/modelos3dInterfaces';
import {StrapiService} from '../../services/strapi.service';
import {Router} from '@angular/router';
import {WidgetUtilService} from "../../services/widget-util.service";
import {Preferences} from "@capacitor/preferences";
import {Browser} from "@capacitor/browser";
import {ContentService} from "../../services/content.service";
import {BrowserService} from "../../services/browser.service";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-modelos-anatomicos',
  templateUrl: './modelos-anatomicos.page.html',
  styleUrls: ['./modelos-anatomicos.page.scss'],
})
export class ModelosAnatomicosPage implements OnInit {


  userInStorage: any = []
  userContent: any;
  slideModelos: any
  slideModelosSwitch: boolean = false;


  strapiUrl = environment.strapiURL;


  modelos: RespuestaModelos3D[] = [];

  constructor(
    private strapi: StrapiService,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private content: ContentService,
    private browser: BrowserService,
    private analyticsService: AnalyticsService,


  ) {}

  async ngOnInit() {
    await this.widgetUtilService.presentLoading();
    await this.getUserContent();
    await this.getUserInStorage();
    await this.widgetUtilService.dismissLoader();
    await this.analyticsService.setCurrentScreen('Modelos-anatomicos' );


  }

  openArticle(slug: string) {
    this.router.navigate(['/modelos-anatomicos', slug]);
  }

  // handleRefresher(event) {
  //   if (event) {
  //     event.target.complete();
  //   }
  // }
  //
  // doRefresh(event) {
  //
  //   this.cargarInfo();
  //
  //
  //
  // }
  //
  // cargarInfo() {
  //   this.strapi.getContenido('modelos-anatomicos-3-ds')
  //       .subscribe((resp: any) => {
  //         // console.log('Vademecum', resp);
  //         this.modelos = resp;
  //
  //         // console.log('Datos API', resp);
  //       });
  //   this.handleRefresher(event);
  // }


  async getUserInStorage() {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    this.userInStorage = userInStorage;
    console.log('Usuario en el storage', this.userInStorage)

  }



  async modelosRefresh(event: any) {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    // console.log('contenido actualizado en el home', event)
    await this.content.getUser(userInStorage);
    await this.content.get3dModels(300);
    await this.getUserContent();
    event.target.complete();
  }



  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    this.userContent = await JSON.parse(userContent.value);
    const banners = this.userContent.banners
    console.log('modelos loaded: ', banners)

    // Slides de la academia

    const bannersOriginalArray = banners.banners
    const bannersFiltrados = bannersOriginalArray.filter((resp: any) => {
      return resp.location === 'modelo';
    })

    this.slideModelos = bannersFiltrados



    this.slideModelosSwitch = banners.slideModelosSwitch

    const modelosStorage = await Preferences.get({ key: 'modelos' });
    const modelos3D = JSON.parse(modelosStorage.value);

    this.modelos = modelos3D;
    // await this.widgetUtilService.dismissLoader();
  }

  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }



}
