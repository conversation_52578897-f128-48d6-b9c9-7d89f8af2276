import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AddEventPageRoutingModule } from './add-event-routing.module';

import { AddEventPage } from './add-event.page';
import {NgArrayPipesModule} from "ngx-pipes";
import {SharedComponentsModule} from "../../../modules/shared-components/shared-components.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    AddEventPageRoutingModule,
    ReactiveFormsModule,
    NgArrayPipesModule,
    SharedComponentsModule,
  ],
  declarations: [AddEventPage]
})
export class AddEventPageModule {}
