<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Nuevo Evento</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/muestra-medica"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-grid >

    <ion-row class="ion-justify-content-center centrar_imagen">
      <ion-col size="12"  size-md="8" size-lg="8" size-sm="12" size-xs="12">
        <form [formGroup]="addProductForm" autocomplete="off">
          <ion-card>

            <ion-card-content>


              El nombre del evento es:
              <h1>{{this.nombre.value}}</h1>
              con fecha de inicio:
              <h3>{{this.fechaInicioData | date: 'medium'}}</h3>
              con fecha de finalización:

              <h3>{{this.fechaFinalData | date: 'medium'}}</h3>
              <h3>Serán Asignados los siguientes representantes:</h3>
              <ion-chip outline color="tertiary"  *ngFor="let email of representantesAsignados" >
                <ion-label>
                  <h2>{{email.email}}</h2>
                  <p>{{email.nombre}} {{email.apellido}}</p>
                  <p>No. de empleado: {{email.nEmpleado}}</p>

                </ion-label>
                <ion-icon name="close-circle" (click)="removeRepresentante(email.id)"></ion-icon>
              </ion-chip>
              <ion-button expand="block" size="" class="ion-margin-top" [disabled]="addProductForm.invalid" (click)="addEvent()">
                <ion-spinner name="dots" slot="start" *ngIf="showAddProductSpinner" ></ion-spinner>
                <ion-icon name="circle-add" slot="end" ></ion-icon>
                Crear Evento
              </ion-button>

            </ion-card-content>
          </ion-card>

          <ion-list>
            <ion-item>
              <ion-label position="floating">Nombre de Evento</ion-label>
              <ion-input type="text" formControlName="nombre"></ion-input>

            </ion-item><div class="error-message" >{{formError.nombre}}</div>

            <ion-item >
              <ion-icon name="calendar" color="primary" slot="start" size="large"></ion-icon>
              <ion-label>
                Fecha inicial
                <ion-datetime-button datetime="fechaInicio"></ion-datetime-button>

              </ion-label>

            </ion-item>

            <ion-item >
              <ion-icon name="calendar" color="primary" slot="start" size="large"></ion-icon>
              <ion-label>
                Fecha final
                <ion-datetime-button datetime="fechaFinal"></ion-datetime-button>

              </ion-label>

            </ion-item>

            <ion-item>

<!--              <ion-label class="ion-text-wrap">-->
<!--                <h1>Asignar Representantes</h1>-->
<!--                <p>Escribe el nombre o el correo del representante que quieres asignar al evento</p>-->

<!--              </ion-label>-->
              <ion-icon name="information-circle" color="primary" slot="start" size="large" id="hover-info" ></ion-icon>

              <ion-popover trigger="hover-info" triggerAction="hover">
                <ng-template>
                  <ion-content class="ion-padding">Te recomendamos buscar el usuario por correo, pero si decides buscar por nombre escribe el nombre y da click en buscar, los nombres usualemnte se encuentran con la primer letra en mayuscula, intenta usar acentos</ion-content>
                </ng-template>
              </ion-popover>
              <ion-button slot="end" (click)="searchUser( searchTerm )">Buscar</ion-button>
              <ion-input clear-input="true" class="ion-text-wrap" label="Asignar Representantes" label-placement="floating" placeholder="Busca por nombre o correo" (ionInput)="searchValue($event)"></ion-input>
            </ion-item>

          </ion-list>




          <ion-list>
            <ion-item *ngFor="let representante of representanteResponse" (click)="agregarRepresentante(representante)" >
              <ion-icon slot="start" name="mail" color="primary"></ion-icon>
              <ion-label>
                <h2>{{representante.email}}</h2>
                <p>Nombre: {{representante.nombre}} {{representante.apellido}}
                <p>Equipo: {{representante.equipo}}</p>
                <p>UDN: {{representante.udn}}</p>
                <p>No. de empleado: {{representante.nEmpleado}}</p>
              </ion-label>
            </ion-item>
          </ion-list>

          <ion-modal [keepContentsMounted]="true">
            <ng-template>
              <ion-datetime
                locale="es-MX-u-hc-h12"
                (ionChange)="fechaInicioFunction( $event)"
                id="fechaInicio"
                >
              </ion-datetime>
            </ng-template>
          </ion-modal>

          <ion-modal [keepContentsMounted]="true">
            <ng-template>
              <ion-datetime
                locale="es-MX-u-hc-h12"
                (ionChange)="fechaFinalFunction( $event)"
                id="fechaFinal"
                >
              </ion-datetime>
            </ng-template>
          </ion-modal>

        </form>

      </ion-col>
    </ion-row>
  </ion-grid>






</ion-content>
