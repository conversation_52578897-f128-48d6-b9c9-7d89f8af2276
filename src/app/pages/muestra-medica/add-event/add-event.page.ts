import {Component, OnInit, ViewChild} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {ADDEVENT, ADDPRODUCT} from '../../../constants/formValidationMessage';
import {HelperService} from '../../../services/helper.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch, query, getDocs, where} from '@angular/fire/firestore';

// import { SwiperComponent} from 'swiper/angular';


// import {IonSlides} from '@ionic/angular';
// import {any} from 'codelyzer/util/function';
import {Router} from '@angular/router';
import Swiper from "swiper";

@Component({
  selector: 'app-add-event',
  templateUrl: './add-event.page.html',
  styleUrls: ['./add-event.page.scss'],
})
export class AddEventPage implements OnInit {

  // @ViewChild('mySlider')  slides: IonSlides ;
  // @ViewChild(SwiperComponent, { static: false }) swiper?: SwiperComponent;

  addProductForm: FormGroup;


  searchTerm: string;
  representantes: any;
  fechaInicioData: any;
  fechaFinalData: any;

  fechaMinima: any;


  nombre: FormControl;
  formError: any = {
    nombre: '',
  };

  slideOpts = {
    initialSlide: 0,
    speed: 400,
    allowTouchMove: false

  };

  representanteResponse: any = [];
  representantesAsignados = [];
  representantesArray = [];
  representantesAsignadosEmail = [];
  validationMessage: any = ADDEVENT;
  showAddProductSpinner = false;

  constructor(private helperService: HelperService,
              private firestoreDbService: FirestoreDbService,
              private widgetUtilService: WidgetUtilService,
              // private db: AngularFirestore,
              private firestore: Firestore,
              private router: Router
  ) {




  }

  ngOnInit() {

    this.createFormControl();
    this.createForm();
  }

  resetForm() {
    this.addProductForm.reset();
    this.formError = {
      nombre: '',
    };
  }
  // Esta linea de codigo permite insertar datos a cualquier coleccion ya creada e incluso
  // crear una nueva colleccion si se cambia el Collection ID
  async addEvent() {
    try {
      this.showAddProductSpinner = true;

      const eventId = await this.firestoreDbService.insertData('eventos', {
        nombre: this.nombre.value,
        fechaInicio: this.fechaInicioData,
        fechaFinal: this.fechaFinalData,
        representantesAsignados: this.representantesAsignados,
        representantesArray: this.representantesArray
      }).then((docRef) => {
        console.log('Document written with ID: ', docRef.id);
        this.showAddProductSpinner = false;
        this.widgetUtilService.presentToast('Evento agregado exitosamente');
        this.resetForm();
        this.router.navigate(['/muestra-medica/event', docRef.id], {replaceUrl: true });

      });


    } catch (error) {
      console.log(error);
      this.widgetUtilService.presentToastError(error.message);
      this.showAddProductSpinner = false;

    }

  }

  createFormControl() {
    this.nombre = new FormControl('',[
      Validators.required

    ]);
  }

  createForm() {
    this.addProductForm = new FormGroup({
      nombre: this.nombre,
    });
    this.addProductForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
  }

  onFormValueChanged(data) {

    this.formError = this.helperService.prepareValidationMessage(this.addProductForm, this.validationMessage, this.formError);




  }


  fechaInicioFunction( event) {
    console.log('Fecha de inicio', event.detail.value);

    this.fechaInicioData = event.detail.value;
    // this.fechaMinima = this.fechaInicioData.split('T')[0];
    console.log(this.fechaMinima);

  }
  fechaFinalFunction( event) {
    console.log(event.detail.value);

    this.fechaFinalData = event.detail.value;

  }

// Version anterior de firebase
//   async searchUser(event) {
//     const searchVal = event.detail.value; // search value from <input>
//
//     console.log(event.detail.value);
//     if (event.detail.value.length >= 3) {
//
//       console.log('mayor de 3 caracteres', searchVal);
//       this.representanteResponse = [];
//       this.representantes = this.db.firestore.collection('representantes');
//
//       const docRef = await this.representantes.where('nombre', '==', searchVal).get()
//           .then((querySnapshot) => {
//             querySnapshot.forEach((doc) => {
//               // doc.data() is never undefined for query doc snapshots
//               console.log(doc.id, ' => ', doc.data());
//
//               this.representanteResponse.push({id: doc.id, data: doc.data()});
//
//
//               console.log(this.representanteResponse);
//             });
//           })
//           .catch((error) => {
//             console.log('Error getting documents: ', error);
//           });
//       if(event.detail.value.includes('@')){
//         console.log('Es un correo')
//         const docRef = await this.representantes.where('email', '==', searchVal).get()
//             .then((querySnapshot) => {
//               querySnapshot.forEach((doc) => {
//                 // doc.data() is never undefined for query doc snapshots
//                 console.log(doc.id, ' => ', doc.data());
//
//                 this.representanteResponse.push({id: doc.id, data: doc.data()});
//
//
//                 console.log(this.representanteResponse);
//               });
//             })
//             .catch((error) => {
//               console.log('Error getting documents: ', error);
//             });
//       }
//
//
//
//     }
//
// // Create a reference to the cities collection
//
// // Create a query against the collection.
//
//
//   }

  async searchValue (event: any) {
    this.searchTerm = event.detail.value
  }


  async searchUser(searchTerm: string): Promise<void> {
    const searchVal: string = searchTerm; // Obtener el valor de búsqueda del evento de entrada
    console.log('Search value', searchVal)
    this.representanteResponse = []
    if (searchVal.length >= 3) {
      // Asegurarse de que el valor de búsqueda tenga al menos 3 caracteres
      try {
        // Referencia a la colección de representantes
        const representantesRef = collection(this.firestore, 'representantes');
        let querySnapshot;

        if (searchVal.includes('@')) {
          // Búsqueda por email
          const q = query(representantesRef, where('email', '==', searchVal));
          querySnapshot = await getDocs(q);
        } else {
          // Búsqueda por nombre
          const q = query(representantesRef, where('nombre', '==', searchVal));
          querySnapshot = await getDocs(q);
        }

        const representanteResponse = [];
        querySnapshot.forEach((doc) => {
          representanteResponse.push({ id: doc.id, ...doc.data() });
        });

        if (representanteResponse.length > 0) {
          console.log('Usuario(s) encontrado(s):', representanteResponse);
          this.representanteResponse = representanteResponse
          // Aquí podrías hacer algo con los usuarios encontrados, como mostrarlos en la UI
        } else {
          console.log('No se encontraron usuarios con ese criterio de búsqueda.');
        }
      } catch (error) {
        console.error('Error al buscar usuarios:', error);
      }
    } else {
      console.log('Por favor, ingresa al menos 3 caracteres para buscar.');
    }
  }

  // async searchUser(event) {
  //   const searchVal = event.detail.value; // search value from <input>
  //
  //   console.log(event.detail.value);
  //   if (searchVal.length >= 3) {
  //     console.log('mayor de 3 caracteres', searchVal);
  //     this.representanteResponse = [];
  //
  //     try {
  //       const representantesRef = collection(this.firestore, 'representantes');
  //       let querySnapshot;
  //
  //       if (searchVal.includes('@')) {
  //         const q = query(representantesRef, where('email', '==', searchVal));
  //         querySnapshot = await getDocs(q);
  //       } else {
  //         const q = query(representantesRef, where('nombre', '==', searchVal));
  //         querySnapshot = await getDocs(q);
  //       }
  //
  //       querySnapshot.forEach((docSnapshot) => {
  //         console.log(docSnapshot.id, ' => ', docSnapshot.data());
  //         this.representanteResponse.push({ id: docSnapshot.id, data: docSnapshot.data() });
  //       });
  //
  //       console.log(this.representanteResponse);
  //     } catch (error) {
  //       console.log('Error getting documents: ', error);
  //     }
  //   }
  // }

  // swipeNext() {
  //   this.slides.slideNext();
  // }
  // swipePrev() {
  //   this.slides.slidePrev();
  // }


  agregarRepresentante(data) {

    if (!this.representantesAsignados.some(el => el.id === data.id)) {
      this.representantesAsignados.push(data);
      this.representantesArray.push(data.id);
    }
    console.log(this.representantesArray);


  }

  removeRepresentante(id) {
    console.log(id);
    this.representantesAsignados = this.representantesAsignados.filter(person => person.id !== id);

    this.representantesArray = this.representantesArray.filter(person => person !== id);


    console.log(this.representantesArray);
// [ 1, 2, 4, 5 ]
  }

  protected readonly event = event;
}
