<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Agregar producto</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>


  <ion-grid fixed style="height: 100%">
    <ion-row class="ion-justify-content-center centrar_imagen" style="height: 100%">
      <ion-col size="12"  size-md="6" size-lg="6">

              <!--              <ion-icon name="key" color="secondary"></ion-icon>-->

          <ion-img class="centrar_imagen" style="width: 130px; "  src="/assets/add_product.png"></ion-img>

        <p class="ion-text-center">Agrega un nuevo producto al inventario: </p>
            <form [formGroup]="addProductForm" autocomplete="off">

              <ion-list>
                <ion-item>
                  <ion-label position="floating">Nombre de producto</ion-label>
                  <ion-input type="text" formControlName="nombre"></ion-input>

                </ion-item><div class="error-message" >{{formError.nombre}}</div>

                <ion-item>
                  <ion-label position="floating">Cantidad de productos</ion-label>
                  <ion-input type="number" formControlName="cantidad"></ion-input>

                </ion-item><div class="error-message">{{formError.cantidad}}</div>

                <ion-item>
                  <ion-label position="floating">SKU (Identificador)</ion-label>
                  <ion-input type="text" formControlName="sku"></ion-input>

                </ion-item><div class="error-message" >{{formError.sku}}</div>
                <ion-item>
                  <ion-label position="floating">Costo</ion-label>
                  <ion-input type="number" formControlName="costo"></ion-input>

                </ion-item><div class="error-message" >{{formError.costo}}</div>
                <ion-item>
                  <ion-label position="floating">Anotaciones</ion-label>
                  <ion-input type="text" formControlName="nota"></ion-input>

                </ion-item><div class="error-message" >{{formError.nota}}</div>


              </ion-list>


              <ion-button expand="block" size="" class="ion-margin-top" [disabled]="addProductForm.invalid" (click)="addProduct()">
                <ion-spinner name="dots" slot="start" *ngIf="showAddProductSpinner" ></ion-spinner>
                <ion-icon name="circle-add" slot="end" ></ion-icon>
                Agregar producto
              </ion-button>
            </form>
      </ion-col>
    </ion-row>
  </ion-grid>


</ion-content>
