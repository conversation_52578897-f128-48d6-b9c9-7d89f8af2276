import { Component, OnInit } from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {HelperService} from '../../../services/helper.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {ADDPRODUCT} from '../../../constants/formValidationMessage';
import {Firestore, doc, docData, collection, getDoc, writeBatch, addDoc} from '@angular/fire/firestore';

import {ActivatedRoute, Router} from '@angular/router';

@Component({
  selector: 'app-add-product',
  templateUrl: './add-product.page.html',
  styleUrls: ['./add-product.page.scss'],
})
export class AddProductPage implements OnInit {

  id: any;
  addProductForm: FormGroup;

  nombre: FormControl;
  cantidad: FormControl;
  costo: FormControl;
  nota: FormControl;
  sku: FormControl;
  formError: any = {
    nombre: '',
    cantidad: '',
    nota: '',
    sku: '',
    costo: ''
  };

  validationMessage: any = ADDPRODUCT;
  showAddProductSpinner = false;

  constructor(private helperService: HelperService,
              private activatedRoute: ActivatedRoute,
              private router: Router,
              private firestoreDbService: FirestoreDbService,
              private widgetUtilService: WidgetUtilService,
              private firestore: Firestore) {

    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.id = result.id;
    });

  }

  ngOnInit() {

    this.createFormControl();
    this.createForm();
  }

  resetForm() {
    this.addProductForm.reset();
    this.formError = {
      nombre: '',
      cantidad: '',
      nota: '',
      sku: '',
      costo: ''

    };
  }

  // Version anterior de firebase
  // async addProduct() {
  //   try {
  //     this.showAddProductSpinner = true;
  //
  //     this.db.firestore.collection('eventos').doc(this.id).collection('productos').add({
  //       nombre: this.nombre.value,
  //       cantidad:  this.cantidad.value,
  //       nota: this.nota.value,
  //       costo: this.costo.value,
  //       sku: this.sku.value,
  //       amount: 1
  //
  //     })
  //         .then(() => {
  //           console.log('Document successfully written!');
  //           this.showAddProductSpinner = false;
  //           this.widgetUtilService.presentToast('Producto agregado exitosamente');
  //           this.resetForm();
  //           this.router.navigate(['/muestra-medica/event', this.id]);
  //
  //           // Si fue satisfactorio el registro
  //           // Verifica si existe el docuemtno creado mandando el uidMedico, el nombre y el apellido
  //         });
  //
  //
  //
  //
  //
  //
  //   } catch (error) {
  //     console.log(error);
  //     this.widgetUtilService.presentToastError(error.message);
  //     this.showAddProductSpinner = false;
  //
  //   }
  //
  // }

  async addProduct() {
    try {
      this.showAddProductSpinner = true;

      const eventoProductosRef = collection(this.firestore, `eventos/${this.id}/productos`);
      await addDoc(eventoProductosRef, {
        nombre: this.nombre.value,
        cantidad: this.cantidad.value,
        nota: this.nota.value,
        costo: this.costo.value,
        sku: this.sku.value,
        amount: 1
      });

      console.log('Document successfully written!');
      await this.widgetUtilService.presentToast('Producto agregado exitosamente');
      this.resetForm();
      this.showAddProductSpinner = false;
      await this.router.navigate(['/muestra-medica/event', this.id]);

    } catch (error) {
      console.log(error);
      await this.widgetUtilService.presentToastError(error.message);
      this.showAddProductSpinner = false;
    }
  }

  createFormControl() {
    this.nombre = new FormControl('', [
      Validators.required

    ]);
    this.cantidad = new FormControl('', [
      Validators.required

    ]);
    this.costo = new FormControl('', [

    ]);
    this.nota = new FormControl('', [

    ]);
    this.sku = new FormControl('', [
      Validators.required

    ]);
  }

  createForm() {
    this.addProductForm = new FormGroup({
      nombre: this.nombre,
      cantidad: this.cantidad,
      nota: this.nota,
      costo: this.costo,
      sku: this.sku

    });
    this.addProductForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
  }

  onFormValueChanged(data) {

    this.formError = this.helperService.prepareValidationMessage(this.addProductForm, this.validationMessage, this.formError);




  }
}
