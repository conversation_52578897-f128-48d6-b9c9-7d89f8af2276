<ion-header>
  <ion-toolbar>

    <ion-title><ion-icon name="cart"  color="primary"></ion-icon> Carr<PERSON> </ion-title>
    <ion-buttons slot="end"><ion-button (click)="closeModal()" ><ion-icon slot="icon-only" name="close"></ion-icon></ion-button></ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content fullscreen>
  <ion-spinner name="" slot="start" *ngIf="showEditProductSpinner" ></ion-spinner>
  <div class="ion-padding">
    <ion-list>

      <ion-item  *ngFor="let p of cart" class="ion-text-wrap">
        <ion-button color="medium" fill="clear" (click)="removeCartItem(p)" class=".ion-float-sm-{end}">
          <ion-icon name="close-circle" slot="icon-only"></ion-icon>
        </ion-button>
        <ion-label class=".ion-float-sm-left">
          <h2>{{p.nombre}}</h2>
          <p>{{p.cantidad - 1 }} muestras disponibles</p>
        </ion-label>

        <ion-button class=".ion-float-left" color="medium" fill="clear" (click)="decreaseCartItem(p)">
          <ion-icon name="remove-circle"></ion-icon>
        </ion-button>
        <h3 class="ion-padding">{{p.amount}}</h3>
        <ion-button color="medium" fill="clear" (click)="increaseCartItem(p)">
          <ion-icon name="add-circle"></ion-icon>
        </ion-button>
      </ion-item>

      <ion-item>
        <ion-grid>
          <ion-row>
            <ion-col size="3">
              Total:
            </ion-col>
            <ion-col size="9" class="ion-text-end">
              {{getTotal()}} muestras a entregar
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-item>
    </ion-list>
    <ion-button expand="full" (click)="checkout()">Entregar muestras
    </ion-button>
  </div>

</ion-content>
