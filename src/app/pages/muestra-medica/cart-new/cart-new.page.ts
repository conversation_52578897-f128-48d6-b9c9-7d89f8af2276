import {Component, Input, OnInit} from '@angular/core';
import {FirebaseAuthService} from '../../../services/firebase-auth.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch, updateDoc, setDoc} from '@angular/fire/firestore';

import {ModalController, NavParams} from '@ionic/angular';
import {CartService} from '../../../services/cart.service';
import {HelperService} from '../../../services/helper.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {Router} from '@angular/router';
import firebase from 'firebase/compat/app';
// import {Plugins} from '@capacitor/core';
// const { Storage, BarcodeScanner, Geolocation } = Plugins;

// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
// import { Browser } from '@capacitor/browser';
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import { Device } from '@capacitor/device';
// import { Geolocation } from '@capacitor/geolocation';

@Component({
  selector: 'app-cart-new',
  templateUrl: './cart-new.page.html',
  styleUrls: ['./cart-new.page.scss'],
})
export class CartNewPage implements OnInit {

  userInStorage: any;

  // profileInfo: any = {};
  profileAvailable = false;

  showEditProductSpinner = false;


  usuarioActivo: string;

  coords: any;


  cart = [];
  numero = 1;


  productosDelCarrito = [];


  fecha = new Date();

  @Input() doctorUid: string;
  @Input() eventId: string;
  @Input() doctorName: string;
  @Input() doctorLastName: string;
  @Input() doctorCedula: string;
  @Input() doctorTel: number;
  @Input() doctorMail: string;



  constructor( private firebaseAuthService: FirebaseAuthService,
               // private db: AngularFirestore,
              private firestore: Firestore,
               private navParams: NavParams,
               private cartService: CartService,
               private modalCtrl: ModalController,
               private helperService: HelperService,
               private firestoreDbService: FirestoreDbService,
               private widgetUtilService: WidgetUtilService,
               private router: Router) {

    // console.log('ID del Doctor en el modal',navParams.get('doctorUid'))
    // this.getUserProfile();
  }

  async ngOnInit() {




    this.cart = this.cartService.getCart();
    // this.getGeo();

    console.log('Doctor UID', this.doctorUid);
    console.log('Contenido del carrito', this.cart);

    const storageRawData = await Preferences.get({ key: 'medico' });
    this.userInStorage = await JSON.parse(storageRawData.value);
    this.usuarioActivo = this.userInStorage.uid;


  }
  /*

    getUserProfile() {

      this.profileAvailable = false;
      this.firebaseAuthService.getAuthState().subscribe(user => {
        if (user) {
          this.profileInfo = user.toJSON();
        }
        // console.log('informacion del perfil', this.profileInfo);
        this.profileAvailable = true;
      }, (error) => {
        this.profileAvailable = true;
        this.widgetUtilService.presentToastError(error.message);
      });
    }
  */


  decreaseCartItem(product) {
    this.cartService.decreaseProduct(product);
  }
  increaseCartItem(product) {
    this.cartService.addProduct(product);
  }
  removeCartItem(product) {
    this.cartService.removeProduct(product);
  }

  getTotal() {
    return this.cart.reduce((i, j) => i + j.amount, 0);
  }

  closeModal() {

    this.modalCtrl.dismiss();
  }
  //
  // async getGeo() {
  //
  //   const coordinates = await Geolocation.getCurrentPosition();
  //   this.coords = coordinates;
  //   if (coordinates) {
  //     console.log('Tenemos coordenadas');
  //     this.coords = coordinates;
  //
  //   } else {
  //     console.log('No tenemos coordenadas');
  //
  //     this.coords = 'Sin Coordenadas';
  //
  //   }
  //   // const coords = `${resp.coords.latitude},${resp.coords.longitude}`;
  //   console.log('Current position:', coordinates);
  //
  // }



  /* clearCart(){}


     async updateProduct(){
         try {
             this.showEditProductSpinner = true;
             const updateProductDetails = {};
             for (const formField in this.editDoctorForm.controls) {
                 const control = this.editDoctorForm.controls[formField];
                 if (control.dirty){

                     updateProductDetails[formField] = control.value;

                 }
             }
             console.log('updateProductDetails', updateProductDetails);
             await this.firestoreDbService.updateData('doctores', this.doctorUid, updateProductDetails);
             await this.getDoctorDetail();
             await this.openEditDoctorForm();
             this.widgetUtilService.presentToast('Producto actualizada exitosamente')
             this.showEditProductSpinner = false;
             this.showEditDoctorForm = false;
         }catch (error) {
             this.widgetUtilService.presentToastError(error.message);
             this.showEditProductSpinner = false;
         }
     }*/




//   async checkout() {
//
//
//     try {
//       this.showEditProductSpinner = true;
//
//
//
//
//       // Esta funcion es para actualizar todos los productos modificados en la version anterior
//       /*var arrayLength = this.cart.length;
//        for (var i = 0; i < arrayLength; i++) {
//
//            //Do something
//            const stockModificado = this.cart[i].amount;
//
//            const decrement = firebase.firestore.FieldValue.increment(-stockModificado);
//            // console.log('Constante StockModificado', stockModificado);
//            const updateProductDetails = {cantidad: decrement};
//            // console.log('Arreglo del carrito', this.cart[i].id);
//            await this.firestoreDbService.updateData('Producto', this.cart[i].id, updateProductDetails);
//
//
//
//
//
//        }
//
// */
//
//       /* this.cart.forEach(function (item) {
//
//            let objetoNombre = Object(item.nombre);
//            console.log('Consola del item', objetoNombre);
//        });
// */
//
//       // Esta funcion es para actualizar todos los productos modificados en la version For...in
//
//       const today  = this.fecha.valueOf();
//
//       for (const i in this.cart) {
//         if (this.cart.hasOwnProperty(i)) {
//           console.log('Data del ID', this.cart[i]);
//
//
//
//           // Inicia el Batch
//           const batch = this.db.firestore.batch();
//
//
//           // Revisar si se cambia el nombre de muestras a entregas
//           // Actualiza la informacion del representante con su nuevo equipo y unidad de negocio
//
//           const reporteEvento = this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').doc(this.doctorUid);
//
//           const reporteProductoEvento = this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').doc(this.doctorUid).collection('muestras').doc();
//
//           const producto = this.db.firestore.collection('eventos').doc(this.eventId).collection('productos').doc(this.cart[i].id);
//
//
//           // console.log('For in demo', this.cart[i])
//
//
//           // Do something
//           const stockModificado = this.cart[i].amount;
//           const decrement = firebase.firestore.FieldValue.increment(-stockModificado);
//
//           console.log('Constante StockModificado', stockModificado);
//           const updateProductDetails = {cantidad: decrement};
//           // this.db.firestore.collection('eventos').doc(this.eventId).collection('productos').doc(this.cart[i].id).update(updateProductDetails);
//           batch.update(producto, updateProductDetails);
//
//           batch.set( reporteProductoEvento, {
//
//             muestra: this.cart[i].nombre,
//             cantidad: this.cart[i].amount,
//             fecha: today,
//             representanteUid: this.userInStorage.uid,
//
//
//
//
//
//           });
//           batch.set(reporteEvento, {
//
//             representanteUid: this.userInStorage.uid,
//             representante: this.userInStorage.nombre + ' ' + this.userInStorage.apellido,
//             representanteNombre: this.userInStorage.nombre,
//             representanteApellido: this.userInStorage.apellido,
//             representanteUdn: this.userInStorage.udn,
//             representanteEquipo: this.userInStorage.equipo,
//             representanteEmail: this.userInStorage.correo,
//             fecha: today,
//
//             localizacion: this.coords
//           }, {merge: true});
//
//
//
//
//           /*
//
//                   batch.set( muestraMedico, {
//
//                   });
//                   batch.set( muestraRepresentante, {
//
//                   });
//           */
//
//
//
//           /*
//                   // console.log('Arreglo del carrito', this.cart[i].id);
//                   await this.firestoreDbService.updateData('Producto', this.cart[i].id, updateProductDetails);
//                   await this.firestoreDbService.updateDataChild('medicos', this.doctorUid, 'muestras',  {
//                     Muestra: this.cart[i].nombre,
//                     Cantidad: this.cart[i].amount,
//                     Fecha: today,
//                     Usuario: {uid: this.profileInfo.uid, email: this.profileInfo.email, lastLoginAt: this.profileInfo.lastLoginAt, emailVerified: this.profileInfo.emailVerified},
//                     Localizacion: this.coords
//
//                   });
//                   await this.firestoreDbService.insertData('reporte', {
//                     Muestra: this.cart[i].nombre,
//                     Cantidad: this.cart[i].amount,
//                     Fecha: today,
//                     Medico: [this.doctorName, this.doctorLastName, this.doctorCedula, this.doctorMail, this.doctorTel, this.doctorUid],
//                     Usuario: {uid: this.profileInfo.uid, email: this.profileInfo.email, lastLoginAt: this.profileInfo.lastLoginAt, emailVerified: this.profileInfo.emailVerified},
//                     Localizacion: this.coords
//                   });*/
//
//           // Commit the batch
//           batch.commit().then(async () => {
//             console.log('Batch completado');
//             this.router.navigate(['/muestra-medica']);
//             this.widgetUtilService.presentToast('Muestras reportadas satisfactoriamente');
//
//             this.showEditProductSpinner = false;
//             // ...
//           });
//
//
//
//
//
//         }
//
//
//
//       }
//
//
//
//
//
//
//     } catch (error) {
//       await this.widgetUtilService.presentToastError(error.message);
//       console.log(error.message);
//       this.showEditProductSpinner = false;
//     }
//
//     await this.modalCtrl.dismiss({
//       Nombre: this.cart[0].nombre,
//       Cantidad: this.cart[0].cantidad
//     });
//
//
//
//   }



  async checkout() {
    try {
      this.showEditProductSpinner = true;
      const today = this.fecha.valueOf();
      const batch = writeBatch(this.firestore);

      for (const i in this.cart) {
        if (this.cart.hasOwnProperty(i)) {
          console.log('Data del ID', this.cart[i]);

          const stockModificado = this.cart[i].amount;
          const productoRef = doc(this.firestore, `eventos/${this.eventId}/productos/${this.cart[i].id}`);
          const reporteEventoRef = doc(this.firestore, `eventos/${this.eventId}/reporte/${this.doctorUid}`);
          const reporteProductoEventoRef = doc(this.firestore, `eventos/${this.eventId}/reporte/${this.doctorUid}/muestras`);

          const updateProductDetails = { cantidad: stockModificado * -1 }; // Assuming you want to decrement

          updateDoc(productoRef, updateProductDetails);
          setDoc(reporteProductoEventoRef, {
            muestra: this.cart[i].nombre,
            cantidad: this.cart[i].amount,
            fecha: today,
            representanteUid: this.userInStorage.uid
          });
          setDoc(reporteEventoRef, {
            representanteUid: this.userInStorage.uid,
            // ... other fields
          }, { merge: true });
        }
      }

      await batch.commit();
      console.log('Batch completado');
      this.router.navigate(['/muestra-medica']);
      this.widgetUtilService.presentToast('Muestras reportadas satisfactoriamente');
      this.showEditProductSpinner = false;

    } catch (error) {
      console.log(error.message);
      await this.widgetUtilService.presentToastError(error.message);
      this.showEditProductSpinner = false;
    }

    await this.modalCtrl.dismiss({
      Nombre: this.cart[0].nombre,
      Cantidad: this.cart[0].cantidad
    });
  }


}
