import {Component, Input, OnInit} from '@angular/core';
import {FirebaseAuthService} from '../../../services/firebase-auth.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch, increment} from '@angular/fire/firestore';

import {ModalController, NavParams} from '@ionic/angular';
import {CartService} from '../../../services/cart.service';
import {HelperService} from '../../../services/helper.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {Router} from '@angular/router';
// import {Plugins} from '@capacitor/core';
import firebase from 'firebase/compat/app';
// const { Storage, BarcodeScanner, Geolocation } = Plugins;


// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser';
import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import { Device } from '@capacitor/device';
// import { Geolocation } from '@capacitor/geolocation';

// Increment by 1
const incrementValue = increment(1);

// Decrement by 1
const decrementValue = increment(-1);
@Component({
  selector: 'app-cart',
  templateUrl: './cart.page.html',
  styleUrls: ['./cart.page.scss'],
})
export class CartPage implements OnInit {

  userInStorage: any;

  // profileInfo: any = {};
  profileAvailable = false;

  showEditProductSpinner = false;


  usuarioActivo: string;

  coords: any;


  cart = [];
  numero = 1;


  productosDelCarrito = [];


  fecha = new Date();

  @Input() doctorUid: string;
  @Input() eventId: string;
  @Input() doctorName: string;
  @Input() doctorLastName: string;
  @Input() doctorCedula: string;
  @Input() doctorTel: number;
  @Input() doctorMail: string;



  constructor( private firebaseAuthService: FirebaseAuthService,
               // private db: AngularFirestore,
              private firestore: Firestore,
               private navParams: NavParams,
               private cartService: CartService,
               private modalCtrl: ModalController,
               private helperService: HelperService,
               private firestoreDbService: FirestoreDbService,
               private widgetUtilService: WidgetUtilService,
               private router: Router) {

    // console.log('ID del Doctor en el modal',navParams.get('doctorUid'))
    // this.getUserProfile();
  }

  async ngOnInit() {




    this.cart = this.cartService.getCart();
    // this.getGeo();

    console.log('Doctor UID', this.doctorUid);
    console.log('Contenido del carrito', this.cart);

    const storageRawData = await Preferences.get({ key: 'user' });
    this.userInStorage = await JSON.parse(storageRawData.value);
    this.usuarioActivo = this.userInStorage.uid;


  }
/*

  getUserProfile() {

    this.profileAvailable = false;
    this.firebaseAuthService.getAuthState().subscribe(user => {
      if (user) {
        this.profileInfo = user.toJSON();
      }
      // console.log('informacion del perfil', this.profileInfo);
      this.profileAvailable = true;
    }, (error) => {
      this.profileAvailable = true;
      this.widgetUtilService.presentToastError(error.message);
    });
  }
*/


  decreaseCartItem(product) {
    this.cartService.decreaseProduct(product);
  }
  increaseCartItem(product) {
    this.cartService.addProduct(product);
  }
  removeCartItem(product) {
    this.cartService.removeProduct(product);
  }

  getTotal() {
    return this.cart.reduce((i, j) => i + j.amount, 0);
  }

  closeModal() {

    this.modalCtrl.dismiss();
  }
  //
  // async getGeo() {
  //
  //   const coordinates = await Geolocation.getCurrentPosition();
  //   this.coords = coordinates;
  //   if (coordinates) {
  //     console.log('Tenemos coordenadas');
  //     this.coords = coordinates;
  //
  //   } else {
  //     console.log('No tenemos coordenadas');
  //
  //     this.coords = 'Sin Coordenadas';
  //
  //   }
  //   // const coords = `${resp.coords.latitude},${resp.coords.longitude}`;
  //   console.log('Current position:', coordinates);
  //
  // }



  /* clearCart(){}


     async updateProduct(){
         try {
             this.showEditProductSpinner = true;
             const updateProductDetails = {};
             for (const formField in this.editDoctorForm.controls) {
                 const control = this.editDoctorForm.controls[formField];
                 if (control.dirty){

                     updateProductDetails[formField] = control.value;

                 }
             }
             console.log('updateProductDetails', updateProductDetails);
             await this.firestoreDbService.updateData('doctores', this.doctorUid, updateProductDetails);
             await this.getDoctorDetail();
             await this.openEditDoctorForm();
             this.widgetUtilService.presentToast('Producto actualizada exitosamente')
             this.showEditProductSpinner = false;
             this.showEditDoctorForm = false;
         }catch (error) {
             this.widgetUtilService.presentToastError(error.message);
             this.showEditProductSpinner = false;
         }
     }*/




  // Version anterior de firebase
//   async checkout() {
//
//
//     try {
//       this.showEditProductSpinner = true;
//
//
//
//
//       // Esta funcion es para actualizar todos los productos modificados en la version anterior
//       /*var arrayLength = this.cart.length;
//        for (var i = 0; i < arrayLength; i++) {
//
//            //Do something
//            const stockModificado = this.cart[i].amount;
//
//            const decrement = firebase.firestore.FieldValue.increment(-stockModificado);
//            // console.log('Constante StockModificado', stockModificado);
//            const updateProductDetails = {cantidad: decrement};
//            // console.log('Arreglo del carrito', this.cart[i].id);
//            await this.firestoreDbService.updateData('Producto', this.cart[i].id, updateProductDetails);
//
//
//
//
//
//        }
//
// */
//
//       /* this.cart.forEach(function (item) {
//
//            let objetoNombre = Object(item.nombre);
//            console.log('Consola del item', objetoNombre);
//        });
// */
//
//       // Esta funcion es para actualizar todos los productos modificados en la version For...in
//
//       const today  = this.fecha.valueOf();
//
//       for (const i in this.cart) {
//         if (this.cart.hasOwnProperty(i)) {
//           console.log('Data del ID', this.cart[i]);
//
//
//
//           // Inicia el Batch
//           const batch = this.db.firestore.batch();
//
//
//           // Revisar si se cambia el nombre de muestras a entregas
//           // Actualiza la informacion del representante con su nuevo equipo y unidad de negocio
//           const muestraMedico = this.db.firestore.collection('medicos').doc(this.doctorUid).collection('muestras').doc();
//
//           const muestraRepresentante = this.db.firestore.collection('representantes').doc(this.usuarioActivo).collection('muestras').doc();
//           const reporteEvento = this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').doc(this.doctorUid);
//
//           const reporteProductoEvento = this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').doc(this.doctorUid).collection('muestras').doc();
//
//           const producto = this.db.firestore.collection('eventos').doc(this.eventId).collection('productos').doc(this.cart[i].id);
//
//
//           // console.log('For in demo', this.cart[i])
//
//
//           // Do something
//           const stockModificado = this.cart[i].amount;
//           const decrement = firebase.firestore.FieldValue.increment(-stockModificado);
//
//           console.log('Constante StockModificado', stockModificado);
//           const updateProductDetails = {cantidad: decrement};
//           // this.db.firestore.collection('eventos').doc(this.eventId).collection('productos').doc(this.cart[i].id).update(updateProductDetails);
//           batch.update(producto, updateProductDetails);
//
//           batch.set( reporteProductoEvento, {
//
//             muestra: this.cart[i].nombre,
//             cantidad: this.cart[i].amount,
//             fecha: today,
//             representanteUid: this.userInStorage.uid,
//
//
//
//
//
//           });
//           batch.set(reporteEvento, {
//
//             representanteUid: this.userInStorage.uid,
//             representante: this.userInStorage.nombre + ' ' + this.userInStorage.apellido,
//             representanteNombre: this.userInStorage.nombre,
//             representanteApellido: this.userInStorage.apellido,
//             representanteUdn: this.userInStorage.udn,
//             representanteEquipo: this.userInStorage.equipo,
//             representanteEmail: this.userInStorage.correo,
//             fecha: today,
//             medico: this.doctorName + '' + this.doctorLastName,
//             medicoNombre: this.doctorName,
//             medicoApellido: this.doctorLastName,
//             medicoCedula: this.doctorCedula,
//             localizacion: this.coords
//           });
//
//
//           batch.set( muestraMedico, {
//
//             Muestra: this.cart[i].nombre,
//             Cantidad: this.cart[i].amount,
//             Fecha: today,
//             representanteUid: this.userInStorage.uid,
//             costo: this.cart[i].costo,
//             costoTotal: this.cart[i].costo * this.cart[i].amount
//
//           });
//
//           /*
//
//                   batch.set( muestraMedico, {
//
//                   });
//                   batch.set( muestraRepresentante, {
//
//                   });
//           */
//
//
//
//           /*
//                   // console.log('Arreglo del carrito', this.cart[i].id);
//                   await this.firestoreDbService.updateData('Producto', this.cart[i].id, updateProductDetails);
//                   await this.firestoreDbService.updateDataChild('medicos', this.doctorUid, 'muestras',  {
//                     Muestra: this.cart[i].nombre,
//                     Cantidad: this.cart[i].amount,
//                     Fecha: today,
//                     Usuario: {uid: this.profileInfo.uid, email: this.profileInfo.email, lastLoginAt: this.profileInfo.lastLoginAt, emailVerified: this.profileInfo.emailVerified},
//                     Localizacion: this.coords
//
//                   });
//                   await this.firestoreDbService.insertData('reporte', {
//                     Muestra: this.cart[i].nombre,
//                     Cantidad: this.cart[i].amount,
//                     Fecha: today,
//                     Medico: [this.doctorName, this.doctorLastName, this.doctorCedula, this.doctorMail, this.doctorTel, this.doctorUid],
//                     Usuario: {uid: this.profileInfo.uid, email: this.profileInfo.email, lastLoginAt: this.profileInfo.lastLoginAt, emailVerified: this.profileInfo.emailVerified},
//                     Localizacion: this.coords
//                   });*/
//
//           // Commit the batch
//           batch.commit().then(async () => {
//             console.log('Batch completado');
//             this.router.navigate(['/muestra-medica']);
//             this.widgetUtilService.presentToast('Muestras reportadas satisfactoriamente');
//
//             this.showEditProductSpinner = false;
//             // ...
//           });
//
//
//
//
//
//         }
//
//
//
//       }
//
//
//
//
//
//
//     } catch (error) {
//       this.widgetUtilService.presentToastError(error.message);
//       console.log(error.message);
//       this.showEditProductSpinner = false;
//     }
//
//     await this.modalCtrl.dismiss({
//       Nombre: this.cart[0].nombre,
//       Cantidad: this.cart[0].cantidad
//     });
//
//
//
//   }



  // Revisar y comprobar funcionamiento de esta funcion

  async checkout() {
    try {
      this.showEditProductSpinner = true;
      const today = new Date().valueOf();
      const batch = writeBatch(this.firestore);

      // Preparar los datos del reporte del evento fuera del bucle, para asegurarse de que se establecen una sola vez
      const reporteEventoRef = doc(this.firestore, `eventos/${this.eventId}/reporte/${this.doctorUid}`);
      batch.set(reporteEventoRef, {
        representanteUid: this.userInStorage.uid,
        representante: this.userInStorage.nombre + ' ' + this.userInStorage.apellido,
        representanteNombre: this.userInStorage.nombre,
        representanteApellido: this.userInStorage.apellido,
        representanteUdn: this.userInStorage.udn,
        representanteEquipo: this.userInStorage.equipo,
        representanteEmail: this.userInStorage.correo,
        fecha: today,
        medico: this.doctorName + ' ' + this.doctorLastName, // Asegúrate de tener un espacio entre el nombre y el apellido
        medicoNombre: this.doctorName,
        medicoApellido: this.doctorLastName,
        medicoCedula: this.doctorCedula,
        // localizacion: this.coords
      }, { merge: true }); // Considera usar { merge: true } para actualizar o crear si no existe

      for (let item of this.cart) {
        console.log('ID del producto: ', item.id);

        const stockModificado = item.amount;
        const decrement = increment(-stockModificado);

        const productoRef = doc(this.firestore, `eventos/${this.eventId}/productos/${item.id}`);
        const reporteProductoEventoRef = doc(this.firestore, `eventos/${this.eventId}/reporte/${this.doctorUid}/muestras/${item.id}`); // Asegúrate de que este path sea correcto
        const interaccionMuestraRef = doc(this.firestore, `interacciones/${this.doctorUid}/muestras/${item.id}`); // Asegúrate de que este path sea correcto

        // Actualizar el stock del producto
        batch.update(productoRef, { cantidad: decrement });

        // Crear o actualizar el reporte del producto específico en el evento
        batch.set(reporteProductoEventoRef, {
          muestra: item.nombre, // Asegúrate de que estos campos existan en tu objeto item
          cantidad: increment(item.amount),
          fecha: today,
          representanteUid: this.userInStorage.uid,
          timestamp: firebase.firestore.FieldValue.serverTimestamp()

        }, { merge: true }); // Considera usar { merge: true } para actualizar o crear si no existe

        // Crear o actualizar el reporte del producto específico en las interacciones
        batch.set(interaccionMuestraRef, {
          muestra: item.nombre, // Asegúrate de que estos campos existan en tu objeto item
          cantidad: increment(item.amount),
          fecha: today,
          representanteUid: this.userInStorage.uid,
          timestamp: firebase.firestore.FieldValue.serverTimestamp()

        }, { merge: true }); // Considera usar { merge: true } para actualizar o crear si no existe
      }

      await batch.commit();
      this.showEditProductSpinner = false;
      await this.modalCtrl.dismiss()
      await this.router.navigate(['/muestra-medica']); // Asegúrate de que esta ruta sea correcta
      await this.widgetUtilService.presentToast('Muestras reportadas satisfactoriamente');
    } catch (error) {
      await this.widgetUtilService.presentToastError(error.message);
      console.error(error.message);
      this.showEditProductSpinner = false;
    }
  }









}
