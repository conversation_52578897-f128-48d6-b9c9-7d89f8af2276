<ion-header>
  <ion-toolbar color="primary">
    <ion-title >{{nombre}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/muestra-medica/events"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>

  <ion-grid class="ion-no-margin ion-no-padding">
    <ion-row class="ion-no-margin ion-no-padding">
      <ion-col size="12" size-lg="8" class="ion-no-margin ion-no-padding">
        <ion-item color="tertiary">
          <ion-label class="ion-text-center">
            <h1 style="font-weight: bold">Fecha del evento:</h1>
            <p>El evento inicia en: </p><p><b>{{eventData.fechaInicio | date: 'medium'}}</b></p>
            <p>El evento finaliza en: </p><p><b>{{eventData.fechaFinal | date: 'medium'}}</b></p>

          </ion-label>
        </ion-item>

        <ion-segment value="inventario">
          <ion-segment-button value="representantes" (click)="selectOption('representantes')">
            <ion-label>Representantes</ion-label>
          </ion-segment-button>
          <ion-segment-button value="inventario" (click)="selectOption('inventario')">
            <ion-label>Inventario</ion-label>
          </ion-segment-button>
          <ion-segment-button value="reporte" (click)="selectOption('reporte')">
            <ion-label>Reporte</ion-label>
          </ion-segment-button>
        </ion-segment>

        <ion-list *ngIf="isSelected('representantes')" @fadeAnimation>
          <ion-item>
            <ion-label class="ion-text-wrap">
              <h1>Se asignaron a los siguientes representantes:</h1>

            </ion-label>
          </ion-item>
          <ion-item id="agregarRepresentante" button="true" color="primary" (click)="addRepresentante()">
            <ion-label>Agregar mas epresentantes</ion-label>
            <ion-icon slot="start" name="add"  ></ion-icon>
          </ion-item>
          <ion-item *ngFor="let representante of representantesAsignados; let i = index" >
            <ion-icon slot="start" name="person" color="primary"></ion-icon>
            <ion-label>
              <h3>{{i + 1}} {{representante.email}}</h3>
              <p>
                {{representante.nombre}} {{representante.apellido}}
              </p>
              <p>
                UDN: {{representante.udn}}
              </p>
              <p>
                No. de empleado: {{representante.nEmpleado}}
              </p>
            </ion-label>
            <ion-icon slot="end" name="close-circle" (click)="removeRepresentante(representante.id)" *ngIf="isAdmin && representantesAsignados.length > 1"></ion-icon>


          </ion-item>

        </ion-list>

        <ion-list *ngIf="isSelected('inventario')" @fadeAnimation>
          <ion-item >
            <ion-label>
              <h1>
                Inventario del evento:
              </h1>
            </ion-label>
          </ion-item>
          <ion-item (click)="addProduct()" *ngIf="isAdmin" button="true" color="primary">
            <ion-label>Agregar producto</ion-label>
            <ion-icon slot="start" name="add"  ></ion-icon>
          </ion-item>
          <ion-item-sliding *ngFor="let product of productsArray">

            <ion-item>
              <ion-icon slot="start" name="logo-dropbox" color="primary"></ion-icon>
              <ion-label>
                <h2>{{product.nombre}}</h2>
                <h3>{{product.cantidad}} piezas</h3>
                <p>{{product.nota}}</p>
                <p>SKU: {{product.sku}}</p>
              </ion-label>
            </ion-item>

            <ion-item-options side="end" *ngIf="isAdmin">
              <ion-item-option color="secondary" (click)="editProduct(product.id)">
                <ion-icon slot="icon-only" name="pencil"></ion-icon>
              </ion-item-option>
              <ion-item-option color="danger" (click)="deleteProduct(product.id)" >
                <ion-icon slot="icon-only" name="trash"></ion-icon>
              </ion-item-option>
            </ion-item-options>
          </ion-item-sliding>

        </ion-list>
      </ion-col>
    </ion-row>
  </ion-grid>




  <ion-grid class="ion-no-padding"  *ngIf="isSelected('reporte')" @fadeAnimation >
    <ion-row >
      <ion-col>
        <ion-list>
          <ion-item>
            <ion-label class="ion-text-wrap ion-text-center">
              <h1>{{medicosAtendidos}} medicos atendidos</h1>
            </ion-label>
          </ion-item>
          <ion-item color="primary" button="true">
            <ion-icon slot="start" name="download"></ion-icon>
            <ion-label (click)="getReportCSV()">
              Descargar Reporte
            </ion-label>
          </ion-item>
          <ion-item  *ngFor="let entrega of entregasArray | orderBy: '-fecha'; index as i" class="reporte" (click)="showSamples(entrega.id, entrega.localizacion)">
            <ion-label>
              <h2>Nombre: {{ entrega.medico }}</h2>
              <p>Cedula: {{ entrega.medicoCedula  }}</p>
              <p>Fecha de entrega: {{ entrega.fecha | date: 'medium' }}</p>
              <p>Quien le entrego: {{ entrega.representante  }}</p>
            </ion-label>
          </ion-item>

        </ion-list>
      </ion-col>
    </ion-row>
  </ion-grid>



  <ion-modal #agregarRepresentanteModal
             [initialBreakpoint]="0.5"
             [breakpoints]="[0.5, 1]"
             [backdropDismiss]="false"
  >
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="end" style="padding-top: 12px">
            <ion-button (click)="agregarRepresentanteModal.dismiss()">Cerrar</ion-button>

          </ion-buttons>
          <ion-searchbar
            style="padding-top: 12px"
            clear-icon="trash-bin"
            placeholder="Busca por nombre o correo"
            (click)="agregarRepresentanteModal.setCurrentBreakpoint(1)"
            [debounce]="1500"
            (ionInput)="searchUser($event)" >
            <ion-icon name="information-circle" color="primary" slot="start" size="large" id="hover-info" ></ion-icon>
          </ion-searchbar>
        </ion-toolbar>


      </ion-header>
      <ion-content class="ion-padding ion-text-center">
        <ion-chip outline  *ngFor="let email of representantesAsignados" >
          <ion-label>{{email.email}}</ion-label>
          <ion-icon name="close-circle" (click)="removeRepresentante(email.id)"></ion-icon>
        </ion-chip>
        <p class="ion-text-center">Aqui se encuentran los represntantes asignados al evento, si quieres remover usuarios o agregar mas solo buscalos y da click en actualizar evento</p>

        <ion-button size="block" (click)="updateEvent()">
          Actualizar Evento
        </ion-button>


        <ion-popover trigger="hover-info" triggerAction="hover">
          <ng-template>
            <ion-content class="ion-padding">Te recomendamos buscar el usuario por correo, pero si decides buscar por nombre escribe el nombre y da click en buscar, los nombres usualemnte se encuentran con la primer letra en mayuscula, intenta usar acentos</ion-content>
          </ng-template>
        </ion-popover>

        <ion-list>
          <ion-item *ngFor="let representante of representanteResponse" (click)="agregarRepresentante(representante)" >
            <ion-icon slot="start" name="mail" color="primary"></ion-icon>
            <ion-label>
              <h2>{{representante.email}}</h2>
              <p>Nombre: {{representante.nombre}} {{representante.apellido}}
              <p>Equipo: {{representante.equipo}}</p>
              <p>UDN: {{representante.udn}}</p>
              <p>No. de empleado: {{representante.nEmpleado}}</p>
            </ion-label>
          </ion-item>
        </ion-list>





      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>
