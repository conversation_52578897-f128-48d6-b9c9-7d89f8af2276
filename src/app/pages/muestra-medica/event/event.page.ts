import {Component, OnInit, ViewChild} from '@angular/core';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {ActivatedRoute, Router} from '@angular/router';
import {
  Firestore,
  doc,
  docData,
  collection,
  getDoc,
  writeBatch,
  query,
  where,
  getDocs,
  deleteDoc,
  onSnapshot
} from '@angular/fire/firestore';

// import {Plugins} from '@capacitor/core';
import {CartPage} from '../cart/cart.page';
import {ActionSheetController, IonModal, ModalController} from '@ionic/angular';
import {ShowSamplesPage} from '../show-samples/show-samples.page';
// const { Storage, BarcodeScanner } = Plugins;


// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {animate, style, transition, trigger} from "@angular/animations";
// import { Browser } from '@capacitor/browser';
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import { Device } from '@capacitor/device';
// import { Geolocation } from '@capacitor/geolocation';
@Component({
  selector: 'app-event',
  templateUrl: './event.page.html',
  styleUrls: ['./event.page.scss'],
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.3s 0.3s ease-in', style({ opacity: 1 })) // Agregar un retraso de 0.3s
      ]),
      transition(':leave', [
        animate('0.3s ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class EventPage implements OnInit {
  @ViewChild('agregarRepresentanteModal') agregarRepresentanteModal: IonModal;

  private unsubscribeFromProducts?: () => void; // Ahora es opcional y se inicializa como undefined

  selectedOption: string = 'inventario';

  id: any;
  eventData: any = [];
  nombre: any;


  searchTerm: string;

  linkRegistro: any;
  elementType = 'img';


  userInStorage: any;

  isAdmin = false;
  showAddProductSpinner = false;


  arrayData = [];
  subArray = [['Nombre', 'Cédula', 'Fecha', 'Representante']];
  superArray: any;
  products: any;
  productsArray: any;
  entregasArray: any[];
  medicosAtendidos: any;
  fechaInicio: any;
  fechaFinal: any;
  representantesAsignados: any;




  representantes: any;
  representanteResponse = [];
  representantesArray = [];
  representantesAsignadosEmail = [];
  constructor(private firestoreDbService: FirestoreDbService,
              private router: Router,
              private activatedRoute: ActivatedRoute,
              // private db: AngularFirestore,
              private firestore: Firestore,
              private modalCtrl: ModalController,
              private actionSheetCtrl: ActionSheetController,
              private widgetUtilService: WidgetUtilService,
  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado del Id importado', result);
      this.id = result.id;
      console.log('resultado del Id', this.id);
      this.linkRegistro = `https://app.sanferconecta.com/muestra-medica/registry/${this.id}`;

    });
  }



  async ngOnInit() {
    const storageRawData = await Preferences.get({ key: 'user' });
    this.userInStorage = await JSON.parse(storageRawData.value);

    this.getEventData();
    this.getReportData();
    this.checkIsAdmin( this.userInStorage);

  }

  ngOnDestroy() {
    this.unsubscribeFromProductData();
  }


  async getEventData() {

    // console.log('resultado del Id despues de ejecutar', this.id);

    this.eventData = await this.firestoreDbService.getDataById('eventos', this.id);
    this.nombre = this.eventData.nombre;
    console.log(this.eventData, 'Data del Evento');


    this.fechaInicio = this.eventData.fechaInicio.split('T')[0];
    this.fechaFinal = this.eventData.fechaFinal.split('T')[0];
    this.representantesAsignados = this.eventData.representantesAsignados;
    this.representantesArray = this.eventData.representantesArray;
    console.log(this.representantesArray[0], 'Representantes Array');


    console.log(this.representantesAsignados[0], 'Representantes Asignados');

    // this.getProductData();
    this.subscribeToProductData()



    // console.log('Consulta de los medicos en el representante ejecutada con el ID: ', this.id);
    // this.firestoreDbService.getAllDataInside('representantes', this.id, 'medicos').subscribe(result => {
    //   console.log('Resultado de la consulta de los medicos registrados por el representante', result);
    //   this.medicosList = result;
    //
    //
    //
    // }, (error) => {
    // });
    // console.log('Resultado de la consulta en variable', this.medicosList);

    // console.log('Consulta Ejecutada');
  }

  removeRepresentante(id) {
    console.log(id);
    this.representantesAsignados = this.representantesAsignados.filter(person => person.id !== id);

    this.representantesArray = this.representantesArray.filter(person => person !== id);
    console.log('Representantes Asignados', this.representantesAsignados);


    console.log('Representantes Array', this.representantesArray);
// [ 1, 2, 4, 5 ]
  }

  async updateEvent() {
    console.log('Click en el boton de actualizar')

    try {
      this.showAddProductSpinner = true;

      const eventId = await this.firestoreDbService.insertDataUserMerge('eventos', this.id,  {
        representantesAsignados: this.representantesAsignados,
        representantesArray: this.representantesArray
      }).then((docRef) => {
        this.showAddProductSpinner = false;
        this.widgetUtilService.presentToast('Representante agregado exitosamente');
        this.modalCtrl.dismiss();
      });


    } catch (error) {
      console.log(error);
      this.widgetUtilService.presentToastError(error.message);
      this.showAddProductSpinner = false;

    }
  }



  async addRepresentante() {

    console.log('Agregar Representante');
    await this.agregarRepresentanteModal.present()
  }


  addProduct() {
    // /muestra-medica/add-product
    this.router.navigate(['/muestra-medica/add-product', this.id]);
  }
  editProduct(id) {
    console.log(id);

  }

  // Version anterior de Firebase
  // async deleteProduct(id) {
  //   console.log(id);
  //
  //   await this.db.firestore.collection('eventos').doc(this.id).collection('productos').doc(id).delete().then(() => {
  //     console.log('Document successfully deleted!');
  //     this.getProductData();
  //   }).catch((error) => {
  //     console.error('Error removing document: ', error);
  //   });
  //
  //
  //
  // }


  async deleteProduct(productId: string) {
    try {
      const productDocRef = doc(this.firestore, `eventos/${this.id}/productos/${productId}`);
      await deleteDoc(productDocRef);

      console.log('Document successfully deleted!');
      this.getProductData();
    } catch (error) {
      console.error('Error removing document: ', error);
    }
  }






  // Version anterior de Firebase
  // async getProductData() {
  //   this.products = await this.db.firestore.collection('eventos')
  //       .doc(this.id)
  //       .collection('productos')
  //       .get()
  //       .then((querySnapshot) => {
  //         this.productsArray = querySnapshot.docs.map((doc) => {
  //           return { id: doc.id, ...doc.data() };
  //         });
  //         console.log(this.productsArray);
  //
  //       });
  // }

  async getProductData() {
    try {
      const productosRef = collection(this.firestore, `eventos/${this.id}/productos`);
      const querySnapshot = await getDocs(productosRef);

      this.productsArray = querySnapshot.docs.map((docSnapshot) => ({
        id: docSnapshot.id,
        ...docSnapshot.data()
      }));

      console.log('Products:', this.productsArray);
    } catch (error) {
      console.log('Error getting products:', error);
    }
  }

  // Método para iniciar la escucha en tiempo real
  subscribeToProductData() {
    const productosRef = collection(this.firestore, `eventos/${this.id}/productos`);

    this.unsubscribeFromProducts = onSnapshot(productosRef, (querySnapshot) => {
      this.productsArray = querySnapshot.docs.map(docSnapshot => ({
        id: docSnapshot.id,
        ...docSnapshot.data()
      }));

      console.log('Products:', this.productsArray);
    }, (error) => {
      console.log('Error getting products:', error);
    });
  }

// Método para cancelar la suscripción
  unsubscribeFromProductData() {
    if (this.unsubscribeFromProducts) {
      this.unsubscribeFromProducts();
      console.log('Unsubscribed from product updates.');
    }
  }


  async getReportData() {
    try {
      const reportColRef = collection(this.firestore, `eventos/${this.id}/reporte`);
      const querySnapshot = await getDocs(reportColRef);

      this.entregasArray = querySnapshot.docs.map(docSnapshot => ({
        id: docSnapshot.id,
        ...docSnapshot.data()
      }));

      console.log('Reporte del Evento: ', this.entregasArray);
      this.medicosAtendidos = this.entregasArray.length;
    } catch (error) {
      console.error('Error fetching report data: ', error);
    }
  }

  async getReportCSV() {


    const dataJSON = this.entregasArray;
    console.log(dataJSON);
    for (const entrega of this.entregasArray) {

      this.arrayData = [entrega.medico, entrega.medicoCedula, new Date(entrega.fecha), entrega.representante];
      console.log(this.arrayData);
      //
      this.subArray.push(this.arrayData);
      console.log(this.subArray);




      // this.subArray.push(entrega);
      // this.superArray.push(this.subArray.concat());



      // console.log(this.superArray);



    }

    // Construct the comma seperated string
    // If a column values contains a comma then surround the column value by double quotes
    const csv = this.subArray.map(row => row.map(item => (typeof item === 'string' && item.indexOf(',') >= 0) ? `"${item}"` : String(item)).join(',')).join('\n');
    console.log(csv);


    this.downloadBlob(csv, 'export.csv', 'text/csv;charset=utf-8;');


  }

   downloadBlob(content, filename, contentType) {
    // Create a blob
    var blob = new Blob([content], { type: contentType });
    var url = URL.createObjectURL(blob);

    // Create a link to download it
    var pom = document.createElement('a');
    pom.href = url;
    pom.setAttribute('download', filename);
    pom.click();
  }



  // Version anterior de firebase
  // async checkIsAdmin(userInStorage) {
  //   console.log('Es administrador despues de la funcion: ', userInStorage);
  //   if (userInStorage.isAdmin) {
  //     console.log('Inicio del Admin', userInStorage.isAdmin);
  //
  //
  //     const representante = await this.db.collection('representantes').doc(userInStorage.uid).ref.get();
  //     const representanteAdministrador = representante.data();
  //     this.isAdmin = representanteAdministrador.isAdmin;
  //
  //     console.log('Firestore nos comunica que es: ', this.isAdmin);
  //
  //     return this.isAdmin;
  //   }
  // }



  async checkIsAdmin(userInStorage) {
    console.log('Es administrador despues de la funcion: ', userInStorage);

    if (userInStorage.isAdmin) {
      console.log('Inicio del Admin', userInStorage.isAdmin);

      try {
        const representanteRef = doc(this.firestore, `representantes/${userInStorage.uid}`);
        const representanteSnapshot = await getDoc(representanteRef);
        const representanteAdministrador: any = representanteSnapshot.data();

        this.isAdmin = representanteAdministrador.isAdmin || false;
        console.log('Firestore nos comunica que es: ', this.isAdmin);

        return this.isAdmin;
      } catch (error) {
        console.error('Error accessing Firestore:', error);
        return false;
      }
    }

    return false;
  }


  async showSamples(id, localizacion) {

    const modal = await this.modalCtrl.create({
      component: ShowSamplesPage,
      cssClass: 'cart-modal',
      componentProps: {
        localizacion,
        doctorId: id,
        id: this.id
      }
    });


    modal.onWillDismiss().then(() => {

    });

    await modal.present();
    const { data } = await modal.onDidDismiss();

    const modalData = data;
    console.log('Informacion de carrito', modalData);


  }


//   async searchUser(event) {
//     const searchVal = event.detail.value; // search value from <input>
//
//     console.log(event.detail.value);
//     if (event.detail.value.length >= 3) {
//
//       console.log('mayor de 3 caracteres', searchVal);
//       this.representanteResponse = [];
//       this.representantes = this.db.firestore.collection('representantes');
//
//       const docRef = await this.representantes.where('nombre', '==', searchVal).get()
//           .then((querySnapshot) => {
//             querySnapshot.forEach((doc) => {
//               // doc.data() is never undefined for query doc snapshots
//               console.log(doc.id, ' => ', doc.data());
//
//               this.representanteResponse.push({id: doc.id, data: doc.data()});
//
//
//               console.log(this.representanteResponse);
//             });
//           })
//           .catch((error) => {
//             console.log('Error getting documents: ', error);
//           });
//       if(event.detail.value.includes('@')){
//         console.log('Es un correo')
//         const docRef = await this.representantes.where('email', '==', searchVal).get()
//             .then((querySnapshot) => {
//               querySnapshot.forEach((doc) => {
//                 // doc.data() is never undefined for query doc snapshots
//                 console.log(doc.id, ' => ', doc.data());
//
//                 this.representanteResponse.push({id: doc.id, data: doc.data()});
//
//
//                 console.log(this.representanteResponse);
//               });
//             })
//             .catch((error) => {
//               console.log('Error getting documents: ', error);
//             });
//       }
//
//
//
//     }
//
// // Create a reference to the cities collection
//
// // Create a query against the collection.
//
//
//   }



  // async searchUser(event: any) {
  //   const searchVal = event.detail.value; // search value from <input>
  //
  //   if (searchVal.length >= 3) {
  //     console.log('Searching for:', searchVal);
  //     this.representanteResponse = [];
  //     const representantesRef = collection(this.firestore, 'representantes');
  //
  //     try {
  //       // Create a query against the collection based on the search value
  //       let q;
  //       if (searchVal.includes('@')) {
  //         console.log('Searching by email');
  //         q = query(representantesRef, where('email', '==', searchVal));
  //       } else {
  //         console.log('Searching by name');
  //         q = query(representantesRef, where('nombre', '==', searchVal));
  //       }
  //
  //       const querySnapshot = await getDocs(q);
  //       querySnapshot.forEach((docSnapshot) => {
  //         console.log(docSnapshot.id, '=>', docSnapshot.data());
  //         this.representanteResponse.push({ id: docSnapshot.id, data: docSnapshot.data() });
  //       });
  //
  //       console.log('Search results:', this.representanteResponse);
  //     } catch (error) {
  //       console.log('Error getting documents:', error);
  //     }
  //   }
  // }

  async searchUser(event: any): Promise<void> {
    const searchVal: string = event.detail.value; // Obtener el valor de búsqueda del evento de entrada
    console.log('Search value', searchVal)
    this.representanteResponse = []
    if (searchVal.length >= 3) {
      // Asegurarse de que el valor de búsqueda tenga al menos 3 caracteres
      try {
        // Referencia a la colección de representantes
        const representantesRef = collection(this.firestore, 'representantes');
        let querySnapshot;

        if (searchVal.includes('@')) {
          // Búsqueda por email
          const q = query(representantesRef, where('email', '==', searchVal));
          querySnapshot = await getDocs(q);
        } else {
          // Búsqueda por nombre
          const q = query(representantesRef, where('nombre', '==', searchVal));
          querySnapshot = await getDocs(q);
        }

        const representanteResponse = [];
        querySnapshot.forEach((doc) => {
          representanteResponse.push({ id: doc.id, ...doc.data() });
        });

        if (representanteResponse.length > 0) {
          console.log('Usuario(s) encontrado(s):', representanteResponse);
          this.representanteResponse = representanteResponse
          // Aquí podrías hacer algo con los usuarios encontrados, como mostrarlos en la UI
        } else {
          console.log('No se encontraron usuarios con ese criterio de búsqueda.');
        }
      } catch (error) {
        console.error('Error al buscar usuarios:', error);
      }
    } else {
      console.log('Por favor, ingresa al menos 3 caracteres para buscar.');
    }
  }

  selectOption(optionId: string) {

    this.selectedOption = optionId;
  }

  isSelected(optionId: string): boolean {
    return this.selectedOption === optionId;
  }
  agregarRepresentante(data) {

    if (!this.representantesAsignados.some(el => el.id === data.id)) {
      this.representantesAsignados.push(data);
      this.representantesArray.push(data.id);

      console.log('Representantes Asignados', this.representantesAsignados);
      console.log('Representantes Array', this.representantesArray);



    } else {
      this.widgetUtilService.presentToastError('Ya seleccionaste este representante')
    }




    // console.log(this.representantesArray);


  }
  async searchValue (event: any) {
    this.searchTerm = event.detail.value
  }
}


