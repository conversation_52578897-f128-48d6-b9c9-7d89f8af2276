<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Todos los eventos</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/muestra-medica"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-grid fixed>
    <ion-row class="ion-justify-content-center" *ngIf="doctorAvailable">
      <ion-col size="12" size-lg="6" size-md="8">
        <ion-row>
          <ion-col size="12">
            <ion-button color="primary" class="ion-float-right" routerLink="/muestra-medica/add-event">
              <ion-icon name="add-circle" ></ion-icon> Agregar Evento
            </ion-button>
          </ion-col>
        </ion-row>
        <h2>Se encontraron {{eventList.length}} eventos listados</h2>

        <ion-list *ngFor="let event of eventList | orderBy: '-fechaInicio'">

          <ion-item-sliding *ngIf="event.fechaFinal.split('T')[0] >= fecha">
            <ion-item-options side="start">
              <ion-item-option (click)="deleteProduct(event.id)" color="danger" expandable *ngIf="isAdmin">
                Borrar
              </ion-item-option>
            </ion-item-options>

            <ion-item  button detail="true" (click)="openEvent(event.id)" >
              <ion-icon name="timer-outline" class="ion-padding" color="success"></ion-icon>

              <ion-label>
                <h2>{{event.nombre}}</h2>
                <h3>Inicia el: {{event.fechaInicio.split('T')[0]}}</h3>
                <h3>Acaba el: {{event.fechaFinal.split('T')[0]}}</h3>
              </ion-label>
            </ion-item>

            <ion-item-options side="end">
              <ion-item-option (click)="editProduct(event.id)" color="tertiary" expandable *ngIf="isAdmin">
                Editar
              </ion-item-option>
            </ion-item-options>
          </ion-item-sliding>




        </ion-list>

        <h1>Eventos anteriores</h1>
        <ion-list *ngFor="let event of eventList | orderBy: '-fechaInicio'">

          <ion-item-sliding *ngIf="event.fechaFinal.split('T')[0] < fecha">
            <ion-item-options side="start">
              <ion-item-option (click)="deleteProduct(event.id)" color="danger" expandable *ngIf="isAdmin">
                Borrar
              </ion-item-option>
            </ion-item-options>

            <ion-item  button detail="true" (click)="openEvent(event.id)" >
              <ion-icon name="lock-closed-outline" class="ion-padding" color="danger"></ion-icon>

              <ion-label>
                <h2>{{event.nombre}}</h2>
                <h3>Inicio el: {{event.fechaInicio.split('T')[0]}}</h3>
                <h3>Acabo el: {{event.fechaFinal.split('T')[0]}}</h3>

              </ion-label>
            </ion-item>

            <ion-item-options side="end">
              <ion-item-option (click)="editProduct(event.id)" color="tertiary" expandable *ngIf="isAdmin">
                Editar
              </ion-item-option>
            </ion-item-options>
          </ion-item-sliding>




        </ion-list>
        <p *ngIf="!eventList.length">No se encontraron productos</p>

      </ion-col>
    </ion-row>
    <ion-spinner name="crescent" color="primary" class="page-loader" *ngIf="!doctorAvailable"></ion-spinner>
  </ion-grid>
</ion-content>
