import { Component, OnInit } from '@angular/core';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {Router} from '@angular/router';
import {Firestore, doc, docData, collection, getDoc, writeBatch, deleteDoc} from '@angular/fire/firestore';

// import {Plugins} from '@capacitor/core';
// const { Storage, BarcodeScanner } = Plugins;


// Actualizacion de Capacitor a 3.0
import { Preferences } from '@capacitor/preferences';

@Component({
  selector: 'app-events',
  templateUrl: './events.page.html',
  styleUrls: ['./events.page.scss'],
})
export class EventsPage implements OnInit {
  isAdmin = false;


  userInStorage: any;

  fecha = new Date().toISOString().split('T')[0];
  eventList: any;
  doctorAvailable = false;
  textoBuscar = '';

  constructor(public firestoreDbService: FirestoreDbService,
              private widgetUtilService: WidgetUtilService,
              private router: Router,
              private firestore: Firestore) { }

  async ngOnInit() {
    this.getEvents();
    console.log(this.fecha);

    const storageRawData = await Preferences.get({ key: 'user' });
    this.userInStorage = await JSON.parse(storageRawData.value);

    this.checkIsAdmin( this.userInStorage);

  }

  buscar( event ) {
    // console.log(event)
    this.textoBuscar = event.detail.value;
  }
  getEvents(event = null) {
    this.doctorAvailable = false;

    this.firestoreDbService.getAllData('eventos').subscribe(result => {
      console.log('result', result);
      this.eventList = result;
      this.doctorAvailable = true;


      this.handleRefresher(event);

    }, (error) => {
      this.widgetUtilService.presentToastError(error.message);
      this.doctorAvailable = true;


      this.handleRefresher(event);

    });

  }

  handleRefresher(event) {
    if (event) {
      event.target.complete();
    }
  }
  doRefresh(event) {

    this.getEvents(event);



  }

  openEvent(id) {
    this.router.navigate(['/muestra-medica/event', id]);

  }

  editProduct(id) {
    console.log(id);

  }

  // Version anterior de Firebase
  // async deleteProduct(id) {
  //   console.log(id);
  //
  //   await this.db.firestore.collection('eventos').doc(id).delete().then(() => {
  //     console.log('Document successfully deleted!');
  //     this.getEvents();
  //   }).catch((error) => {
  //     console.error('Error removing document: ', error);
  //   });
  //
  //
  //
  // }




  async deleteProduct(id: string) {
    console.log('Deleting product with ID:', id);

    try {
      const productRef = doc(this.firestore, `eventos/${id}`);
      await deleteDoc(productRef);
      console.log('Document successfully deleted!');

      // Call a function to update the UI or fetch new data
      this.getEvents();  // Ensure this function is defined and does what you need
    } catch (error) {
      console.error('Error removing document: ', error);
    }
  }
// Version anterior de Firebase
//   async checkIsAdmin(userInStorage) {
//     console.log('Es administrador despues de la funcion: ', userInStorage);
//     if (userInStorage.isAdmin) {
//       console.log('Inicio del Admin', userInStorage.isAdmin);
//
//
//       const representante = await this.db.collection('representantes').doc(userInStorage.uid).ref.get();
//       const representanteAdministrador = representante.data();
//       this.isAdmin = representanteAdministrador.isAdmin;
//
//       console.log('Firestore nos comunica que es: ', this.isAdmin);
//
//       return this.isAdmin;
//     }
//   }


  async checkIsAdmin(userInStorage) {
    console.log('Es administrador despues de la funcion: ', userInStorage);

    if (userInStorage.isAdmin) {
      console.log('Inicio del Admin', userInStorage.isAdmin);

      try {
        const representanteRef = doc(this.firestore, `representantes/${userInStorage.uid}`);
        const representanteSnapshot = await getDoc(representanteRef);
        const representanteAdministrador = representanteSnapshot.data();

        // this.isAdmin = representanteAdministrador?.isAdmin || false;
        console.log('Firestore nos comunica que es: ', this.isAdmin);

        return this.isAdmin;
      } catch (error) {
        console.error('Error accessing Firestore:', error);
        return false;
      }
    }

    return false;
  }

}
