import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { MuestraMedicaPage } from './muestra-medica.page';

const routes: Routes = [
  {
    path: '',
    component: MuestraMedicaPage
  },
  {
    path: 'add-product/:id',
    loadChildren: () => import('./add-product/add-product.module').then( m => m.AddProductPageModule)
  },
  {
    path: 'add-event',
    loadChildren: () => import('./add-event/add-event.module').then( m => m.AddEventPageModule)
  },
  {
    path: 'events',
    loadChildren: () => import('./events/events.module').then( m => m.EventsPageModule)
  },
  {
    path: 'event/:id',
    loadChildren: () => import('./event/event.module').then( m => m.EventPageModule)
  },
  {
    path: 'scanner/:id',
    loadChildren: () => import('./scanner/scanner.module').then( m => m.ScannerPageModule)
  },
  {
    path: 'select-products/:id/:id2',
    loadChildren: () => import('./select-products/select-products.module').then( m => m.SelectProductsPageModule)
  },
  {
    path: 'cart/:id',
    loadChildren: () => import('./cart/cart.module').then( m => m.CartPageModule)
  },
  {
    path: 'show-samples',
    loadChildren: () => import('./show-samples/show-samples.module').then( m => m.ShowSamplesPageModule)
  },
  {
    path: 'registry/:id',
    loadChildren: () => import('./registry/registry.module').then( m => m.RegistryPageModule)
  },
  {
    path: 'select-products-new/:id/:id2',
    loadChildren: () => import('./select-products-new/select-products-new.module').then( m => m.SelectProductsNewPageModule)
  },
  {
    path: 'cart-new',
    loadChildren: () => import('./cart-new/cart-new.module').then( m => m.CartNewPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MuestraMedicaPageRoutingModule {}
