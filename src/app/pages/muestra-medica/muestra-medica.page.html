<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Entrega muestras medicas</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content >
<ion-grid>
  <ion-row class="ion-justify-content-center centrar_imagen">
    <ion-col size="12"size-xl="8" size-lg="8" >

      <h2 *ngIf="isAdmin">Hola {{nombre}} {{apellido}} puedes crear y ver eventos:</h2>
      <ion-button expand="block"  routerLink="/muestra-medica/add-event" *ngIf="isAdmin">
        Agregar Evento
      </ion-button>
      <ion-button expand="block" color="secondary" routerLink="/muestra-medica/events" *ngIf="isAdmin">
        Ver Eventos
      </ion-button>

      <h2 *ngIf="!isAdmin"><PERSON>la {{nombre}} {{apellido}}  tienes asignado los siguientes eventos, selecciona el evento donde quieres entregar la muestra médica:</h2>

      <ion-list *ngFor="let event of eventsResponse" >

        <ion-item button (click)="openScanner(event.id)" *ngIf="event.data.fechaFinal.split('T')[0] > fechaHoy">
          <ion-icon name="timer-outline" class="ion-padding" color="success"></ion-icon>

          <ion-label class="ion-text-wrap">
            <ion-text>
              <h1>{{event.data.nombre}}</h1>
            </ion-text>
            <p>Fecha de inicio: {{event.data.fechaInicio.split('T')[0]}}</p>
            <ion-text >
              <p>Fecha de finalización: {{event.data.fechaFinal.split('T')[0]}}</p>
            </ion-text>
          </ion-label>
        </ion-item>
      </ion-list>

      <h1>Eventos finalizados</h1>

      <ion-list *ngFor="let event of eventsResponse" >

        <ion-item button (click)="openEvent(event.id)" *ngIf="event.data.fechaFinal.split('T')[0] < fechaHoy">
          <ion-icon name="lock-closed-outline" class="ion-padding" color="danger"></ion-icon>

          <ion-label class="ion-text-wrap">
            <ion-text>
              <h1>{{event.data.nombre}}</h1>
            </ion-text>
            <ion-text >
              <p>Finalizo el: {{event.data.fechaFinal.split('T')[0]}}</p>
            </ion-text>
          </ion-label>
        </ion-item>
      </ion-list>

    </ion-col>
  </ion-row>
</ion-grid>





</ion-content>
