import { Component, OnInit } from '@angular/core';
import {WidgetUtilService} from '../../services/widget-util.service';
import {FirestoreDbService} from '../../services/firestore-db.service';
// import {FirebaseAnalyticsService} from '../../services/firebase-analitycs.service';
import {Router} from '@angular/router';
import {Firestore, doc, docData, collection, getDoc, writeBatch, where, query, getDocs} from '@angular/fire/firestore';

import {AlertController} from '@ionic/angular';
import firebase from 'firebase/compat/app';
// import {Plugins} from '@capacitor/core';

// const { Storage, BarcodeScanner } = Plugins;

// Actualizacion de Capacitor a 3.0
import { Preferences } from '@capacitor/preferences';
import {AnalyticsService} from "../../services/analytics.service";


const increment = firebase.firestore.FieldValue.increment(1);
const decrement = firebase.firestore.FieldValue.increment(1);


@Component({
  selector: 'app-muestra-medica',
  templateUrl: './muestra-medica.page.html',
  styleUrls: ['./muestra-medica.page.scss'],
})


export class MuestraMedicaPage implements OnInit {

  userInStorage: any;

  date: any;
  fecha = new Date();
  fechaHoy = new Date().toISOString().split('T')[0];


  uidMedico: any;

  datosMedico: any;
  eventos: any;


  nombre: any;
  apellido: any;

  isAdmin = false;


  eventsResponse = [];


  constructor(
      private widgetUtilService: WidgetUtilService,
      private firestoreDbService: FirestoreDbService,
      // private fbas: FirebaseAnalyticsService,
      private router: Router,
      // private db: AngularFirestore,
      private firestore: Firestore,

      private analyticsService: AnalyticsService,


      public alertController: AlertController,

  ) { }

  async ngOnInit() {
    const storageRawData = await Preferences.get({ key: 'user' });
    this.userInStorage = await JSON.parse(storageRawData.value);

    this.nombre = this.userInStorage.nombre;
    this.apellido = this.userInStorage.apellido;

    await this.analyticsService.setCurrentScreen('Muestra-medica' );

    this.lookForUser();
    this.checkIsAdmin(this.userInStorage);

  }








  // Version anterior de Firebase
  // async lookForUser() {
  //
  //
  //   console.log('UID', this.userInStorage.uid);
  //
  //   this.eventsResponse = [];
  //   this.eventos = this.db.firestore.collection('eventos');
  //   const docRef = await this.eventos.where('representantesArray', 'array-contains', this.userInStorage.uid).get()
  //       .then((querySnapshot) => {
  //         querySnapshot.forEach((doc) => {
  //           // doc.data() is never undefined for query doc snapshots
  //           console.log(doc.id, ' => ', doc.data());
  //
  //           this.eventsResponse.push({id: doc.id, data: doc.data()});
  //
  //
  //           console.log(this.eventsResponse);
  //         });
  //       })
  //       .catch((error) => {
  //         console.log('Error getting documents: ', error);
  //       });
  // }



  async lookForUser() {
    try {
      console.log('UID', this.userInStorage.uid);

      const eventosRef = collection(this.firestore, 'eventos');
      const q = query(eventosRef, where('representantesArray', 'array-contains', this.userInStorage.uid));
      const querySnapshot = await getDocs(q);

      this.eventsResponse = querySnapshot.docs.map(docSnapshot => ({
        id: docSnapshot.id,
        data: docSnapshot.data()
      }));

      console.log(this.eventsResponse);
    } catch (error) {
      console.error('Error getting documents: ', error);
    }
  }
  openEvent(id) {
    console.log(id);
    this.router.navigate(['/muestra-medica/event', id]);

  }
  // Varsion anterior Firebase
  // async checkIsAdmin(userInStorage) {
  //   console.log('Es administrador despues de la funcion: ', userInStorage);
  //   if (userInStorage.isAdmin) {
  //     console.log('Inicio del Admin', userInStorage.isAdmin);
  //
  //
  //     const representante = await this.db.collection('representantes').doc(userInStorage.uid).ref.get();
  //     const representanteAdministrador = representante.data();
  //     this.isAdmin = representanteAdministrador.isAdmin;
  //
  //     console.log('Firestore nos comunica que es: ', this.isAdmin);
  //
  //     return this.isAdmin;
  //   }
  // }


  async checkIsAdmin(userInStorage) {
    console.log('Es administrador despues de la funcion: ', userInStorage);

    if (userInStorage.isAdmin) {
      console.log('Inicio del Admin', userInStorage.isAdmin);

      try {
        const representanteRef = doc(this.firestore, `representantes/${userInStorage.uid}`);
        const representanteSnapshot = await getDoc(representanteRef);
        const representanteAdministrador: any = representanteSnapshot.data();

        this.isAdmin = representanteAdministrador.isAdmin || false;
        console.log('Firestore nos comunica que es: ', this.isAdmin);

        return this.isAdmin;
      } catch (error) {
        console.error('Error accessing Firestore:', error);
        return false;
      }
    }

    return false;
  }


  openScanner(id) {
    console.log(id);
    this.router.navigate(['/muestra-medica/scanner', id]);



  }
}
