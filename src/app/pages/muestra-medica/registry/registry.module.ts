import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RegistryPageRoutingModule } from './registry-routing.module';

import { RegistryPage } from './registry.page';
import {NgArrayPipesModule} from "ngx-pipes";

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        ReactiveFormsModule,
        RegistryPageRoutingModule,
        NgArrayPipesModule
    ],
  declarations: [RegistryPage]
})
export class RegistryPageModule {}
