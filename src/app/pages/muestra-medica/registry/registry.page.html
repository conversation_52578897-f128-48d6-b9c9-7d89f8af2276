<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button color="primary" text="Regresar" defaultHref="/muestra-medica/scanner"></ion-back-button>
    </ion-buttons>

    <ion-title slot="" color="primary">Registrate en </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-grid >
    <ion-row class="ion-justify-content-center centrar_imagen">
      <ion-col size="12"  size-md="8" size-lg="8" size-sm="12" size-xs="12">
        <!--        <ion-card class="title-promo">-->
        <!--          <ion-card-content>-->
        <!--            <ion-card-title class="title-promo">-->
        <!--              Regístrate-->
        <!--            </ion-card-title>-->
        <!--          </ion-card-content>-->
        <!--        </ion-card>-->

        <ion-button expand="block" size="small" color="light" routerLink="/aviso-privacidad">
          <ion-icon slot="start" name="information-circle" color="primary"></ion-icon>
          <ion-label>
            Aviso de Privacidad
          </ion-label>
        </ion-button>
        <form [formGroup]="signupForm" autocomplete="off">
          <ion-card class="ion-no-padding ion-no-margin" style="box-shadow: none">

            <ion-card-header>
              Ingresa tu nombre y tus apellidos
              <ion-icon slot="start" name="person-outline" color="primary"></ion-icon>
            </ion-card-header>

            <!--// Nombre===============================-->
            <ion-item lines="none">
              <ion-text color="danger">*</ion-text>
              <ion-input
                helperText="Ingresa tu nombre"
                [errorText]=" formError.nombre"
                id="nombre"
                label-placement="floating"
                type="text"
                formControlName="nombre"
                label="Nombre">
              </ion-input>
            </ion-item>

            <!--// Apellido  Paterno===============================-->
            <ion-item lines="none">

              <ion-text color="danger">*</ion-text>
              <ion-input
                helperText="Ingresa tu Apellido Paterno"
                [errorText]=" formError.paterno"
                id="paterno"
                label-placement="floating"
                type="text"
                formControlName="paterno"
                label="Apellido Paterno">
              </ion-input>
            </ion-item>
            <!--// Apellido Materno===============================-->
            <ion-item lines="none">

              <ion-text color="danger">*</ion-text>
              <ion-input
                helperText="Ingresa tu Apellido Paterno"
                [errorText]=" formError.materno"
                id="materno"
                label-placement="floating"
                type="text"
                formControlName="materno"
                label="Apellido Materno">
              </ion-input>
            </ion-item>
          </ion-card>


          <!--// Cedula===============================-->
          <ion-item style="display: none">
            <ion-icon
              slot="start"
              name="call-outline"
              color="primary">
            </ion-icon>
            <ion-input
              [errorText]=" formError.cedula"
              label-placement="floating"
              id="cedula"
              class="ion-text-wrap"
              type="tel"
              formControlName="cedula"
              label="Cedula Profesional">
            </ion-input>
          </ion-item>

          <!--// Boton de validacion de la cedula===============================-->
          <ion-button
            [disabled]="nombre.value.length < 3 || paterno.value.length < 4"
            (click)="lookForCedula(nombre.value, paterno.value, materno.value)"
            expand="block"
            class="ion-margin-vertical button-cedula">
            <ion-icon
              slot="icon-only"
              name="search">
            </ion-icon>

            Verificar cédula

          </ion-button>


          <!--// Modal de la Cedula===============================-->
          <ion-modal
            #modalCedula
            [handle]="true"
            handleBehavior="none"
            [initialBreakpoint]="0.75"
            [breakpoints]="[0, 0.75, 1]"
            [backdropDismiss]="true"
          >
            <ng-template>
              <ion-content class="ion-padding">
                <ion-searchbar placeholder="Buscar" (click)="modalCedula.setCurrentBreakpoint(1)" (ionChange)="buscar( $event )"></ion-searchbar>
                <p class="ion-text-center">A continuacion se listan las cedulas encontradas con tu nombre, se encontraron <span *ngIf="sepData.length === 1000">mas de </span>{{sepData.length}} resultados, busca tu cedula y seleccionala, puedes utilizar el campo de busqueda si asi lo requieres</p>

                <ion-list>
                  <ion-item (click)="obtenerDatos(cedula)" *ngFor="let cedula of sepData |  filterBy: ['idCedula', 'nombre', 'desins']: textoBuscar">
                    <!--                        <ion-avatar slot="start">-->
                    <!--                          <ion-icon name="person-outline"></ion-icon>-->
                    <!--                        </ion-avatar>-->
                    <ion-label class="ion-text-wrap">
                      <h2 style="font-weight: bold;">{{cedula.nombre}} {{cedula.paterno}} {{cedula.materno}}</h2>
                      <h3><ion-icon name="ribbon" size="small" color="primary"></ion-icon> Cédula profesional: {{cedula.idCedula}}</h3>
                      <h3><ion-icon name="school" size="small" color="primary"></ion-icon> Titulo: {{cedula.titulo}}</h3>
                      <p>Institución: {{cedula.desins}}</p>
                      <p>Año de registro: {{cedula.anioreg}}</p>
                    </ion-label>
                  </ion-item>

                  <!--                      <ion-card class="cedula" (click)="obtenerDatos(cedula)" *ngFor="let cedula of cedulaData">-->
                  <!--                        <ion-card-content>-->
                  <!--                          <h1 style="font-weight: bold; color: #044e5e"><ion-icon name="person"></ion-icon> {{cedula.nombre}}  {{cedula.paterno}} {{cedula.materno}}</h1>-->
                  <!--                          <ion-card-subtitle color="primary"><ion-icon name="ribbon" size="small"></ion-icon> Cédula profesional: {{cedula.idCedula}}</ion-card-subtitle>-->
                  <!--                          <ion-card-subtitle><ion-icon name="school" size="small"></ion-icon> Titulo: {{cedula.titulo}}</ion-card-subtitle>-->
                  <!--                          <ion-card-subtitle class="small_text">Institución: {{cedula.desins}}-->
                  <!--                            <br>Año de registro: {{cedula.anioreg}}</ion-card-subtitle>-->
                  <!--                        </ion-card-content>-->

                  <!--                      </ion-card>-->
                </ion-list>
              </ion-content>
            </ng-template>
          </ion-modal>




          <!--// Titulo===============================-->
          <ion-item lines="none">
            <ion-icon
              slot="start"
              name="school-outline"
              color="primary">

            </ion-icon>
            <ion-input
              helperText="Titulo: Este campo se valida de forma automática al verificar tu cedula profesional"
              [errorText]=" formError.titulo"
              id="titulo"
              class="ion-text-wrap"
              label-placement="floating"
              type="text"
              formControlName="titulo"
              label="Titulo"
            >
            </ion-input>
          </ion-item>

          <!--// Telefono===============================-->
          <ion-item lines="none">
            <ion-icon
              slot="start"
              name="call-outline"
              color="primary">
            </ion-icon>
            <ion-text color="danger">*</ion-text>
            <ion-input
              [counter]="true"
              maxlength="10"
              helperText="Ingresa tu telefono a 10 digitos"
              [errorText]=" formError.telefono"
              label-placement="floating"
              type="tel"
              id="telefono"
              formControlName="telefono"
              label="Telefono">
            </ion-input>

          </ion-item>

          <!--// Email===============================-->
          <ion-item lines="none">
            <ion-icon
              slot="start"
              name="mail-outline"
              color="primary">

            </ion-icon>
            <ion-text color="danger">*</ion-text>
            <ion-input
              helperText="Ingresa tu correo electronico"
              [errorText]=" formError.email"
              label-placement="floating"
              type="email"
              id="email"
              formControlName="email"
              label="Email">
            </ion-input>
          </ion-item>




          <ion-button expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid || clicked === true" (click)="signup()"> <ion-spinner name="dots" slot="start" *ngIf="showSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Registrarse </ion-button>

        </form>
        <ion-button expand="block" size="small" class="ion-margin-top" color="light" routerLink="/login" > <ion-icon name="log-in" slot="end" ></ion-icon> Iniciar sesión </ion-button>



      </ion-col>
    </ion-row>
  </ion-grid>

</ion-content>
