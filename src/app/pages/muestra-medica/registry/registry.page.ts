import {Component, OnInit, ViewChild} from '@angular/core';
import {environment} from '../../../../environments/environment';
import {AbstractControl, FormControl, FormGroup, ValidatorFn, Validators} from '@angular/forms';
import {SIGNUP, SIGNUPRECOMENDADO} from '../../../constants/formValidationMessage';
import {ActivatedRoute, Router} from '@angular/router';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {HelperService} from '../../../services/helper.service';
import {FirebaseAuthService} from '../../../services/firebase-auth.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {AlertController, IonModal, LoadingController, ModalController, Platform} from '@ionic/angular';
import {StrapiService} from '../../../services/strapi.service';
// import {Plugins} from '@capacitor/core';
// import {CedulaPage} from '../../cedula/cedula.page';
import {Firestore, doc, docData, collection, getDoc, writeBatch, addDoc} from '@angular/fire/firestore';
import {CapacitorHttp, HttpResponse} from "@capacitor/core";


// const { Storage, Http, Device, Browser } = Plugins;
//

@Component({
  selector: 'app-registry',
  templateUrl: './registry.page.html',
  styleUrls: ['./registry.page.scss'],
})
export class RegistryPage implements OnInit {

  @ViewChild('modalCedula') modalCedula: IonModal;


  usuarioCreado = {};

// Sep data for Cedulas
  sepData:any = [];
  cedulaData: any = {};
  textoBuscar: string = '';

  profileInfo: any = {};
  profileAvailable = false;

  clicked = false;

  fecha = new Date();
  signupForm: any = FormGroup;
  nombre: any = FormControl;
  paterno: any = FormControl;
  materno: any = FormControl;
  cedula: any = FormControl;
  titulo: any = FormControl;
  email: any = FormControl;
  telefono: any = FormControl;
  // password: FormControl;
  formError: any = {
    nombre: '',
    paterno: '',
    materno: '',
    cedula: '',
    titulo: '',
    email: '',
    telefono: '',
    // password: '',
  };

  titulosPermitidos = [];


  nombreRecabado = {};

  carrito: any = {};

  version = environment.version;


  validationMessage: any = SIGNUP;
  showSignupSpinner = false;
  showSpinner = false;


  eventId: any;



  constructor(private helperService: HelperService,
              private router: Router,
              private firebaseAuthService: FirebaseAuthService,
              private firestoreDbService: FirestoreDbService,
              private widgetUtilService: WidgetUtilService,
              private modalCtrl: ModalController,
              private strapi: StrapiService,
              // private fbas: FirebaseAnalyticsService,
              private activatedRoute: ActivatedRoute,
              private loadingCtrl: LoadingController,
              private firestore: Firestore,
  ) {

    this.activatedRoute.params.subscribe((result: any) => {
      console.log('resultado de id', result);
      this.eventId = result.id;
      console.log('Event ID', this.eventId);



    });
  }

  async ngOnInit() {
    this.createFormControl();
    this.createForm();
    /*try {
      const retPrueba = Http.request({
        method: 'POST',
        url: 'https://sanferconecta.live/sep',
        data: {url: 'http://search.sep.gob.mx/solr/cedulasCore/select?&q=omar&rows=1&wt=json'},
        headers: {
          'Content-Type': 'application/json'
        },
      });

      this.widgetUtilService.presentToast('Conexion exitosa con la Secretaría de Educación Pública');
    } catch (e) {
      this.widgetUtilService.presentToastError('No pudimos contactar con el servidor de la Secretaría de Educación Pública');

    }*/


    await this.strapi.getContenido('controles')
        .subscribe(async resp => {
          // console.log('Controles', resp.titulos);
          const arreglo = [];
          for ( const titulo of resp.titulos) {
            arreglo.push(titulo.titulo);
          }
          this.titulosPermitidos = await arreglo;

          // console.log('Titulos permitidos', this.titulosPermitidos);


        });

  }

  resetForm() {
    this.signupForm.reset();
    this.formError = {
      nombre: '',
      cedula: '',
      titulo: '',
      paterno: '',
      materno: '',
      email: '',
      // password: '',
      telefono: ''
    };
  }

  // Version anterior de Firebase
  // async signup() {
  //   try {
  //     this.showSignupSpinner = true;
  //
  //     const eventId = await this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').add({
  //
  //
  //       medico: this.nombre.value + ' ' + this.apellido.value,
  //       medicoNombre: this.nombre.value,
  //       medicoApellido: this.apellido.value,
  //       medicoCedula: this.cedula.value,
  //       medicoTitulo: this.titulo.value,
  //       medicoTipo: this.carrito.Tipo,
  //       medicoGenero: this.carrito.Genero,
  //       medicoEmail: this.email.value,
  //       medicoTelefono: this.telefono.value,
  //       medicoInstitucion: this.carrito.Institucion,
  //       medicoAnioRegistro: this.carrito.AnioRegistro,
  //
  //
  //
  //     }).then((docRef) => {
  //       console.log('Document written with ID: ', docRef.id);
  //       this.widgetUtilService.presentToast('Medico agregado en el evento exitosamente');
  //       this.router.navigate(['/muestra-medica/select-products-new', this.eventId, docRef.id], {replaceUrl: true });
  //       this.showSignupSpinner = false;
  //       this.resetForm();
  //     });
  //
  //
  //
  //
  //
  //
  //   } catch (error) {
  //     console.log('Error', error);
  //     this.showSignupSpinner = false;
  //     this.widgetUtilService.presentToastError(error);
  //   }
  //
  // }


// Registro de medicos, validando cedula profesional
  async signup() {
    try {
      this.showSignupSpinner = true;

      const reporteRef = collection(this.firestore, `eventos/${this.eventId}/reporte`);
      const docRef = await addDoc(reporteRef, {
        medico: `${this.nombre.value} ${this.paterno.value} ${this.materno.value}`,
        medicoNombre: this.nombre.value,
        medicoPaterno: this.paterno.value,
        medicoMaterno: this.materno.value,
        medicoCedula: this.cedula.value,
        medicoTitulo: this.titulo.value,
        medicoTipo: this.carrito.Tipo,
        medicoGenero: this.carrito.Genero,
        medicoEmail: this.email.value,
        medicoTelefono: this.telefono.value,
        medicoInstitucion: this.carrito.Institucion,
        medicoAnioRegistro: this.carrito.AnioRegistro,
      });

      console.log('Document written with ID: ', docRef.id);
      await this.widgetUtilService.presentToast('Medico agregado en el evento exitosamente');
      await this.router.navigate(['/muestra-medica/select-products-new', this.eventId, docRef.id], { replaceUrl: true });
      this.showSignupSpinner = false;
      this.resetForm(); // Make sure resetForm() is implemented as per your requirement
    } catch (error) {
      console.error('Error', error);
      this.showSignupSpinner = false;
      await this.widgetUtilService.presentToastError('Error during signup');
    }
  }

  createFormControl() {
    this.nombre = new FormControl('', [
      Validators.required
    ]);
    this.paterno = new FormControl('', [
      Validators.required
    ]);
    this.materno = new FormControl('', [
      Validators.required
    ]);
    this.cedula = new FormControl('', [
      Validators.required
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.email
    ]);
    this.telefono = new FormControl('', [
      Validators.required,
      Validators.minLength(10)

    ]);
    /*this.password = new FormControl('', [
      Validators.required,
      Validators.minLength(8)
    ]);*/
    this.titulo = new FormControl('', [
      // this.tituloMedico
    ]);

    // this.linea = new FormControl('', []);
    // this.equipo = new FormControl('', []);
  }
  createForm() {
    this.signupForm = new FormGroup({
      nombre: this.nombre,
      paterno: this.paterno,
      materno: this.materno,
      cedula: this.cedula,
      titulo: this.titulo,
      email: this.email,
      telefono: this.telefono,

    });
    this.signupForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
  }

  onFormValueChanged(data) {
    // console.log('Data: ', data);
    // console.log(this.signupForm);
    this.formError = this.helperService.prepareValidationMessage(this.signupForm, this.validationMessage, this.formError);
    // console.log(this.formError);
  }


  async lookForCedula(nombre: string, paterno: string, materno: string){
    console.log('Nueva Ruta');
    await this.modalCedula.present()
    const loading = await this.loadingCtrl.create();
    await loading.present();

    // Intenta contactar con la SEP
    try {
      const options = {
        url: 'https://sanferconecta.live/sep',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          maxResult: "1000",
          nombre,
          paterno,
          materno,
          idCedula: ""}
      };
      const response: HttpResponse = await CapacitorHttp.post(options);
      // console.log('Retorno RAW: ', response);
      await loading.dismiss();


      this.sepData = response.data.items;
      // console.log('Headers ', response.headers);
      // console.log('Retorno en forma de arreglo: ', this.sepData);
      // await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa! <br><h3>Selecciona tu cedula</h3><br>I');
    } catch (e) {

      // Si hay un error intenta conectar nuevamente ocn la sep

      console.log('Error', e);

    }



  }
  buscar( event: any ) {
    this.textoBuscar = event.detail.value;
    console.log(this.textoBuscar)

  }


  async obtenerDatos(cedula: any) {
    // Asegúrate de que `cedula` tenga un tipo adecuado en lugar de `any`.
    this.cedulaData = {
      Nombre: cedula.nombre,
      Paterno: cedula.paterno,
      Materno: cedula.materno,
      Cedula: cedula.idCedula,
      Titulo: cedula.titulo,
      Institucion: cedula.desins,
      AnioRegistro: cedula.anioreg,
      Tipo: cedula.tipo,
      Genero: cedula.sexo
    };
    await this.modalCtrl.dismiss();

    const verificacion = this.titulosPermitidos.some(el => this.cedulaData.Titulo.includes(el));

    this.signupForm.patchValue({
      nombre: this.cedulaData.Nombre,
      apellido: `${this.cedulaData.Paterno} ${this.cedulaData.Materno}`,
      cedula: this.cedulaData.Cedula,
      titulo: this.cedulaData.Titulo
    });

    if (verificacion) {
      await this.widgetUtilService.presentToast('¡Cedula verificada! Puedes registrarte');
    } else {
      await this.widgetUtilService.presentToastError('Disculpe esta aplicación es solo para los profesionales de la Salud, si se equivoco de cedula profesional intente nuevamente');
    }
  }
}
