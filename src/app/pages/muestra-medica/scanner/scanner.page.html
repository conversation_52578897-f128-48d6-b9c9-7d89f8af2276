<ion-header id="scannerHeader">
  <ion-toolbar color="primary">
    <ion-title >{{nombre}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content id="scannerContent">


<!--  <ion-card>-->


<!--    <ion-card-content class="ion-text-center">-->
<!--      <ion-button size="block" (click)="registrarMedico()">-->
<!--        Registrar medico-->
<!--        <ion-icon slot="end" name="medkit"></ion-icon>-->
<!--      </ion-button>-->
<!--      Si el médico no tiene registro en Sanfer Conecta y no cuenta con su codigo QR entonces da click en el botón de arriba-->

<!--    </ion-card-content>-->
<!--  </ion-card>-->

  <ion-card>


    <ion-card-content class="ion-text-center">


      <ion-button size="block" (click)="startScanning()">
        Escanear codigo  <ion-icon slot="end" name="qr-code"></ion-icon>
      </ion-button>
      Si cancelaste el scanner puedes dar click en el botón de arriba

    </ion-card-content>
  </ion-card>



</ion-content>
