import {Component, NgZone, OnInit} from '@angular/core';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {FirestoreDbService} from '../../../services/firestore-db.service';
// import {FirebaseAnalyticsService} from '../../../services/firebase-analitycs.service';
import {ActivatedRoute, Router} from '@angular/router';
import {Firestore, doc, docData, collection, getDoc, writeBatch, query, where, getDocs} from '@angular/fire/firestore';

import {AlertController} from '@ionic/angular';
// import {Plugins} from '@capacitor/core';
import firebase from 'firebase/compat/app';

// const { Storage, BarcodeScanner } = Plugins;

// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
// import { BarcodeScanner, SupportedFormat } from '@capacitor-community/barcode-scanner';
import {
  Barcode,
  BarcodeFormat,
  BarcodeScanner,
  LensFacing,
  StartScanOptions
} from '@capacitor-mlkit/barcode-scanning';
import { FilePicker } from '@capawesome/capacitor-file-picker';

import { Preferences } from '@capacitor/preferences';
import {Subscription} from "rxjs";
import {UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {BarcodeScanningModalComponent} from "./barcode-scanning-modal.component";
import {DialogService} from "../../../services/dialog.service";


const increment = firebase.firestore.FieldValue.increment(1);
const decrement = firebase.firestore.FieldValue.increment(1);
@Component({
  selector: 'app-scanner',
  templateUrl: './scanner.page.html',
  styleUrls: ['./scanner.page.scss'],
})
export class ScannerPage implements OnInit {
  eventId: any;

  userInStorage: any;

  date: any;
  fecha = new Date();

  uidMedico: any;

  datosMedico: any;
  events: any;
  event: any;

  eventName: any;

  nombre: any;
  apellido: any;
  nEmpleado: any;
  equipo: any;
  titulo: any;
  createdAt: any;
  telefono: any;
  correo: any;
  role: any;
  uid: any;
  cedula: any;
  genero: any;
  fuenteDelRegistro: string;

  eventsResponse = [];



  public readonly barcodeFormat = BarcodeFormat;
  public readonly lensFacing = LensFacing;

  public formGroup = new UntypedFormGroup({
    formats: new UntypedFormControl([]),
    lensFacing: new UntypedFormControl(LensFacing.Back),
    googleBarcodeScannerModuleInstallState: new UntypedFormControl(0),
    googleBarcodeScannerModuleInstallProgress: new UntypedFormControl(0),
  });
  public barcodes: Barcode[] = [];
  public isSupported = false;
  public isPermissionGranted = false;

  constructor(

      private widgetUtilService: WidgetUtilService,
      private firestoreDbService: FirestoreDbService,
      // private scannerService: ScannerService,
      private router: Router,
      // private db: AngularFirestore,
              private firestore: Firestore,
      private activatedRoute: ActivatedRoute,
      private readonly dialogService: DialogService,
      private readonly ngZone: NgZone,



      public alertController: AlertController,

  ) {

    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado del Id importado', result);
      this.eventId = result.id;
      console.log('resultado del Id', this.eventId);

    });
  }

  async ngOnInit() {

    await this.eventInfo();
    BarcodeScanner.isSupported().then((result) => {
      this.isSupported = result.supported;
    });
    BarcodeScanner.checkPermissions().then((result) => {
      this.isPermissionGranted = result.camera === 'granted';
    });
    BarcodeScanner.removeAllListeners().then(() => {
      BarcodeScanner.addListener(
        'googleBarcodeScannerModuleInstallProgress',
        (event) => {
          this.ngZone.run(() => {
            console.log('googleBarcodeScannerModuleInstallProgress', event);
            const { state, progress } = event;
            this.formGroup.patchValue({
              googleBarcodeScannerModuleInstallState: state,
              googleBarcodeScannerModuleInstallProgress: progress,
            });
          });
        },
      );
    });
    await this.startScanning();
  }



  async registrarMedico(){
    await this.router.navigate(['/muestra-medica/registry', this.eventId]);
  }



  async startScanning() {
    const permisionResponse = await BarcodeScanner.checkPermissions()
    console.log('Respuesta del permiso', permisionResponse)

    const alertRequest = await this.alertController.create({
      header: 'No tenemos permiso de usar tu cámara',
      message: 'Por favor permitenos usar tu camara para escanear los códigos QR',
      buttons: [{
        text: 'Aún no',
        role: 'cancel'
      },
        {
          text: 'Permitir',
          handler: async () => {
            await BarcodeScanner.requestPermissions();
          }
        }]
    });
    const alertDenied = await this.alertController.create({
      header: 'Bloqueaste los permisos para la camara',
      message: 'Por favor permitenos usar tu camara para escanear los códigos QR da click en abrir configuraciones y busca permisos para usar la camara y activalos',
      buttons: [{
        text: 'No',
        role: 'cancel'
      },
        {
          text: 'Abrir Configuraciones',
          handler: async () => {
            await BarcodeScanner.openSettings();
          }
        }]
    });

    switch (permisionResponse.camera) {
      case 'granted':
        console.log('Abrir vinculo')
        await this.startScan()
        break;
      case 'prompt':
        console.log('Abrir vinculo')
        await alertRequest.present();
        break;
      case 'denied':
        await alertDenied.present();
        break;

    }
  }


  public async startScan(): Promise<void> {
    const formats = this.formGroup.get('formats')?.value || [];
    const lensFacing =
      this.formGroup.get('lensFacing')?.value || LensFacing.Back;
    const element = await this.dialogService.showModal({
      component: BarcodeScanningModalComponent,
      // Set `visibility` to `visible` to show the modal (see `src/theme/variables.scss`)
      cssClass: 'barcode-scanning-modal',
      showBackdrop: false,
      componentProps: {
        formats: formats,
        lensFacing: lensFacing,
      },
    });
    element.onDidDismiss().then(async (result) => {
      const barcode: Barcode | undefined = result.data?.barcode;
      if (barcode) {
        // this.barcodes = [barcode];
        // console.log(this.barcodes)
        await this.getUserInfo(barcode)

      }
    });
  }


  public async installGoogleBarcodeScannerModule(): Promise<void> {
    await BarcodeScanner.installGoogleBarcodeScannerModule();
  }

  async getUserInfo(barcode: Barcode) {

    try {
      const medicoRef = doc(this.firestore, `medicos/${barcode.rawValue}`);
      const docSnapshot = await getDoc(medicoRef);

      if (docSnapshot.exists()) {
        console.log('Document Existe');
        await this.router.navigate(['/muestra-medica/select-products', this.eventId, barcode.rawValue]);
      } else {
        const alert = await this.alertController.create({
          header: `Código Incorrecto`,
          subHeader: 'Intenta escaneando otro código',
          message: `Puede ser que hayas escaneado un código invalido, intenta nuevamente`,
          backdropDismiss: false,
          buttons: [
            { text: 'Registrar usuario', handler: () => this.registrarMedico() },
            { text: 'Intentar nuevamente', handler: () => this.startScan() }]
        });

        await alert.present();
        const { role } = await alert.onDidDismiss();

      }
    } catch (e) {
      console.error('Error scanning:', e);
      await this.widgetUtilService.presentToastError(`Código invalido, intenta nuevamente`);
    }

  }



  async eventInfo() {
    try {
      const eventRef = doc(this.firestore, `eventos/${this.eventId}`);
      const docSnapshot = await getDoc(eventRef);

      if (docSnapshot.exists()) {
        console.log('Data del documento', docSnapshot.data());
        this.event = docSnapshot.data();
        this.nombre = this.event.nombre;
      } else {
        console.log('No such document!');
      }
    } catch (error) {
      console.error('Error getting document:', error);
    }
  }



}
