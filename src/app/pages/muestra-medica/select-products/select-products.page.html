<ion-header no-border>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button color="primary"  defaultHref="/muestra-medica/scanner"></ion-back-button>
    </ion-buttons>
    <ion-title color="primary" ><span style="font-size: small">Estas atendiendo a</span> <br> {{doctorDetail.nombre}} {{doctorDetail.apellido}}</ion-title>
    <ion-buttons slot="end">
      <ion-button  color="primary" (click)="editarDoctor()"> <ion-icon name="create" slot="icon-only"></ion-icon></ion-button>
    </ion-buttons>


  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card color="primary">
    <ion-card-content>
      <p class="ion-text-center small_text">Estas entregando muestras en el evento:</p>
      <h1 class="ion-text-center">{{eventName}}</h1>
    </ion-card-content>
  </ion-card>
  <ion-fab vertical="center" horizontal="end" slot="fixed">
    <ion-fab-button (click)="openCart()" #cart>
      <div class="cart-length">{{cartItemCount | async}}</div>
      <ion-icon name="cart" class="cart-icon"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <ion-grid>
    <ion-row class="ion-justify-content-center centrar_imagen">
      <ion-col size="12" size-lg="8" size-md="8">
        <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
          <ion-refresher-content></ion-refresher-content>
        </ion-refresher>




        <ion-list>
          <ion-item *ngFor="let producto of productList" >
            <ion-button fill="clear" (click)="addToCart(producto)">
              <ion-icon name="flask" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-label class="ion-text-wrap">
              <h2>{{producto.nombre}}</h2>
              <h3></h3>
              <p>Disponible {{producto.cantidad}} muestras</p>


            </ion-label>
            <ion-button fill="clear" (click)="addToCart(producto)">
              <ion-icon name="add-circle" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-item>
        </ion-list>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
