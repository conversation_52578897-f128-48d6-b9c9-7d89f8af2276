import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {BehaviorSubject} from 'rxjs';
import {ActivatedRoute, Router} from '@angular/router';
import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {HelperService} from '../../../services/helper.service';
import {AlertController, ModalController} from '@ionic/angular';
import {CartPage} from '../cart/cart.page';
import {CartService} from '../../../services/cart.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch} from '@angular/fire/firestore';


@Component({
  selector: 'app-select-products',
  templateUrl: './select-products.page.html',
  styleUrls: ['./select-products.page.scss'],
})
export class SelectProductsPage implements OnInit {


  events: any;
  event: any;
  eventName: any;

  cart = [];
  products = [];
  cartItemCount: BehaviorSubject<number>;

  carrito: Array<any> = [] ;

  productList: Array<any> = [];
  productAvailable  = false;

  // data: Product[] = [];

  doctorUid: any;
  eventId: any;
  doctorAvailable = false;
  doctorDetail: any = {};
  doctorDetailList: Array<any> = [];
  elementType: 'url' | 'canvas' | 'img' = 'img';

  @ViewChild('cart', {static: false, read: ElementRef})fab: ElementRef;


  constructor(private activatedRoute: ActivatedRoute,
              private firestoreDbService: FirestoreDbService,
              private widgetUtilService: WidgetUtilService,
              private helperService: HelperService,
              private router: Router,
              // private db: AngularFirestore,
              private firestore: Firestore,
              public alertController: AlertController,

              private cartService: CartService,
              private modalCtrl: ModalController) {
    this.activatedRoute.params.subscribe((result: any) => {
      console.log('resultado de id', result);
      this.eventId = result.id;
      console.log('Event ID', this.eventId);

      this.doctorUid = result.id2;
      console.log('Doctor id', this.doctorUid);

      this.getDoctorDetail();
      this.getProductList();
      this.getEventInfo();
      this.verifyDeliver();

    });

  }


  ngOnInit() {

    this.products = this.productList;
    this.cart = this.cartService.getCart();
    this.cartItemCount = this.cartService.getCartItemCount();

  }

  // getEventInfo() {
  //   this.events = this.db.firestore.collection('eventos').doc(this.eventId).get().then((doc) => {
  //     if (doc.exists) {
  //       console.log('Data del documento', doc.data());
  //       this.event = doc.data();
  //       this.eventName = this.event.nombre;
  //     } else {
  //       // doc.data() will be undefined in this case
  //       console.log('No such document!');
  //     }
  //   }).catch((error) => {
  //     console.log('Error getting document:', error);
  //   });
  // }

  async getEventInfo() {
    const eventRef = doc(this.firestore, `eventos/${this.eventId}`);

    try {
      const docSnapshot = await getDoc(eventRef);

      if (docSnapshot.exists()) {
        console.log('Data del documento', docSnapshot.data());
        this.event = docSnapshot.data();
        this.eventName = this.event.nombre;
      } else {
        console.log('No such document!');
      }
    } catch (error) {
      console.error('Error getting document:', error);
      // Handle the error appropriately
    }
  }

  getProductList(event = null) {
    this.productAvailable = false;
    this.firestoreDbService.getAllDataInside('eventos', this.eventId, 'productos').subscribe(result => {
      console.log('Productos listados', result);
      this.productList = result;
      this.productAvailable = true;
      this.handleRefresher(event);

    }, (error) => {
      this.widgetUtilService.presentToastError(error.message);

      this.productAvailable = true;

      this.handleRefresher(event);

    });

  }

  handleRefresher(event) {
    if (event) {
      event.target.complete();
    }
  }

  doRefresh(event) {

    this.getProductList(event);



  }


  async getDoctorDetail() {
    try {
      this.doctorAvailable = false;
      const result = await this.firestoreDbService.getDataById('medicos', this.doctorUid);
      // console.log('Detalles del doctor', result);
      this.doctorDetail = result;
      this.doctorDetailList = [];
      for (const key in this.doctorDetail) {
        this.doctorDetailList.push({
          name: key,
          value: this.doctorDetail[key]
        });
      }
      this.cartItemCount.next(0);
      this.cart.splice(0);

      this.doctorAvailable = true;

    } catch (error) {

      console.log(error);
      this.widgetUtilService.presentToastError(error.message);
      this.doctorAvailable = true;

    }
  }

  editarDoctor() {
    this.router.navigate(['/doctor', this.doctorUid]);
  }




  // Carrito Codigo

  addToCart(product) {

    this.animateCSS('tada');
    this.cartService.addProduct(product);
  }


  async openCart() {
    this.animateCSS('bounceOutLeft', true);

    const modal = await this.modalCtrl.create({
      component: CartPage,
      cssClass: 'cart-modal',
      componentProps: {
        doctorUid: this.doctorUid,
        eventId: this.eventId,
        doctorName: this.doctorDetail.nombre,
        doctorLastName: this.doctorDetail.apellido,
        doctorCedula: this.doctorDetail.cedula,
        doctorTel: this.doctorDetail.telefono,
        cartCount: this.cartItemCount,
        doctorMail: this.doctorDetail.email
      }
    });

    modal.onWillDismiss().then(() => {
      this.fab.nativeElement.classList.remove('animated', 'bounceOutLeft');
      this.animateCSS('bounceInLeft');
    });

    await modal.present();
    let { data } = await modal.onDidDismiss();

    this.carrito = data;
    console.log('Informacion de carrito', this.carrito);


  }


  animateCSS(animationName, keepAnimated = false) {
    const node = this.fab.nativeElement;
    node.classList.add('animated', animationName);

    // https://github.com/daneden/animate.css
    function handleAnimationEnd() {
      if (!keepAnimated) {
        node.classList.remove('animated', animationName);
      }
      node.removeEventListener('animationend', handleAnimationEnd);
    }
    node.addEventListener('animationend', handleAnimationEnd);
  }

  // Version anterior de Firebase
  // async verifyDeliver() {
  //
  //   console.log('Verificando usuario', 'this.eventId: ', this.eventId, 'this.doctorUid: ', this.doctorUid);
  //   const medico = await this.db.firestore.collection('eventos').doc(this.eventId).collection('reporte').doc(this.doctorUid).get().then(async (docSnapshot) => {
  //     console.log('Document ', docSnapshot);
  //     if (docSnapshot.exists) {
  //       console.log('Document Existe');
  //
  //
  //
  //
  //
  //
  //       const alert = await this.alertController.create({
  //         // cssClass: 'my-custom-class',
  //         header: `Ya se entregaron muestras a ${this.doctorDetail.nombre} ${this.doctorDetail.apellido}`,
  //         subHeader: '¿Estas seguro que quieres entregar más muestras al medico?',
  //         message: `Durante el evento: ${this.eventName} ya has entregado muestras a este medico`,
  //         backdropDismiss: false,
  //         buttons: [
  //           {
  //             text: 'Cancelar',
  //             role: 'cancelar',
  //             cssClass: 'secondary',
  //             handler: (blah) => {
  //               console.log('Confirm Cancel: blah');
  //               this.router.navigate(['/muestra-medica']);
  //             }
  //           }, {
  //             text: 'Quiero entregar +',
  //             handler: async () => {
  //               console.log('Confirm Okay');
  //
  //             }
  //           }
  //         ]
  //       });
  //
  //       await alert.present();
  //
  //       const { role } = await alert.onDidDismiss();
  //
  //
  //     }
  //   });
  //
  //
  //
  // }

  async verifyDeliver() {
    console.log('Verificando usuario', 'this.eventId: ', this.eventId, 'this.doctorUid: ', this.doctorUid);

    const medicoRef = doc(this.firestore, `eventos/${this.eventId}/reporte/${this.doctorUid}`);

    try {
      const docSnapshot = await getDoc(medicoRef);
      console.log('Document ', docSnapshot);

      if (docSnapshot.exists()) {
        console.log('Document Existe');

        const alert = await this.alertController.create({
          // Other configurations
          header: `Ya se entregaron muestras a ${this.doctorDetail.nombre} ${this.doctorDetail.apellido}`,
          subHeader: '¿Estas seguro que quieres entregar más muestras al medico?',
          message: `Durante el evento: ${this.eventName} ya has entregado muestras a este medico`,
          // Other configurations

          backdropDismiss: false,
          buttons: [
            {
              text: 'Cancelar',
              role: 'cancelar',
              cssClass: 'secondary',
              handler: (blah) => {
                console.log('Confirm Cancel: blah');
                this.router.navigate(['/muestra-medica']);
              }
            }, {
              text: 'Quiero entregar +',
              handler: async () => {
                console.log('Confirm Okay');

              }
            }
          ]
        });

        await alert.present();
        const { role } = await alert.onDidDismiss();

      }
    } catch (error) {
      console.error('Error fetching document: ', error);
      // Handle error appropriately
    }
  }
}
