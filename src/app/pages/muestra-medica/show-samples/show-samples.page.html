<ion-header>
  <ion-toolbar>

    <ion-title><ion-icon name="cart"  color="primary"></ion-icon>Muestras entregadas </ion-title>
    <ion-buttons slot="end"><ion-button (click)="closeModal()" ><ion-icon slot="icon-only" name="close"></ion-icon></ion-button></ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-grid class="ion-margin reporte" >
    <ion-row class="reporte">
      <ion-col class="reporte">Cantidad</ion-col>
      <ion-col class="reporte">Muestra</ion-col>
      <ion-col class="reporte">Fecha</ion-col>

    </ion-row>
    <ion-row *ngFor="let entrega of samplesArray" class="reporte"  (click)="showDetail(entrega.id)">
      <ion-col class="reporte">{{ entrega.cantidad }}</ion-col>
      <ion-col class="reporte">{{ entrega.muestra }}</ion-col>
      <ion-col class="reporte">{{ entrega.fecha | date: 'medium' }}</ion-col>

    </ion-row>
  </ion-grid>
</ion-content>
