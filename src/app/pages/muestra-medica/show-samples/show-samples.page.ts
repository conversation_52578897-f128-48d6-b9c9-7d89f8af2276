import {Component, Input, OnInit} from '@angular/core';
import {ModalController, NavParams} from '@ionic/angular';
// import {FirebaseAuthService} from '../../../providers/firebase-auth.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch, getDocs} from '@angular/fire/firestore';

// import {CartService} from '../../../services/cart.service';
import {HelperService} from '../../../services/helper.service';
// import {FirestoreDbService} from '../../../services/firestore-db.service';
import {WidgetUtilService} from '../../../services/widget-util.service';
import {Router} from '@angular/router';

@Component({
  selector: 'app-show-samples',
  templateUrl: './show-samples.page.html',
  styleUrls: ['./show-samples.page.scss'],
})
export class ShowSamplesPage implements OnInit {
  @Input() id: string;
  @Input() doctorId: string;


  samples: any;
  samplesArray: any;
  constructor(
      // private db: AngularFirestore,
              private firestore: Firestore,
      private modalCtrl: ModalController,
      private helperService: HelperService,
      // private firestoreDbService: FirestoreDbService,
      private widgetUtilService: WidgetUtilService,
      private router: Router

  ) { }

  ngOnInit() {
    console.log('ID Obtenido', this.id);
    this.getSamplesData();
  }


  // Version anterior de Firebase
  // async getSamplesData() {
  //   this.samples = await this.db.firestore.collection('eventos')
  //       .doc(this.id)
  //       .collection('reporte').doc(this.doctorId).collection('muestras')
  //       .get()
  //       .then((querySnapshot) => {
  //         this.samplesArray = querySnapshot.docs.map((doc) => {
  //           return { id: doc.id, ...doc.data() };
  //         });
  //         console.log('Reporte del Evento: ', this.samplesArray);
  //
  //       });
  // }


  async getSamplesData() {
    try {
      const muestrasColRef = collection(this.firestore, `eventos/${this.id}/reporte/${this.doctorId}/muestras`);
      const querySnapshot = await getDocs(muestrasColRef);

      this.samplesArray = querySnapshot.docs.map(docSnapshot => {
        return { id: docSnapshot.id, ...docSnapshot.data() };
      });

      console.log('Reporte del Evento: ', this.samplesArray);
    } catch (error) {
      console.error('Error fetching samples data: ', error);
      // Handle the error appropriately
    }
  }

  async closeModal() {
    await this.modalCtrl.dismiss();
  }
  showDetail(id) {
    console.log(id);
  }
}
