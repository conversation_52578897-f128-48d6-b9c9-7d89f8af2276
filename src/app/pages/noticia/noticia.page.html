<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let noticia of noticias">{{noticia.Titulo}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/noticias"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content >
  <ion-grid [fixed]="true">
    <ion-card style="margin: 10px; border-radius: 20px">
      <ion-img *ngIf="img" src="{{strapiUrl}}{{img.url}}"></ion-img>
    </ion-card>
    <div class="ion-padding container">
      <h4>{{categoria}}</h4>
      <h1>{{titulo}}</h1>
      <span class="date"> {{createdAt | date}}</span>
      <!--    <div class="separador">    -->
      <!--    </div>-->
      <p>{{abstracto}}</p>
      <div class="content" *ngIf="noticiaMarked" [innerHTML]="noticiaMarked"></div>
      <ion-button size="small" *ngIf="source" (click)="openBrowser(source)">Visitar fuente de la informacion</ion-button>

    </div>
  </ion-grid>



</ion-content>
