import { Component, OnInit } from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';
import {ActivatedRoute} from '@angular/router';
import {RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {Firestore, doc, docData, collection, getDoc, writeBatch, addDoc} from '@angular/fire/firestore';
import {marked} from "marked";
// import {Plugins} from '@capacitor/core';
//
// const { Storage } = Plugins;

// Actualizacion de Capacitor a 3.0
// import { Storage } from '@capacitor/storage';
import {serverTimestamp} from '@angular/fire/database';
import {Preferences} from "@capacitor/preferences";
import {ContentService} from "../../services/content.service";
import {BrowserService} from "../../services/browser.service";
import {InteractionService} from "../../services/interaction.service";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-noticia',
  templateUrl: './noticia.page.html',
  styleUrls: ['./noticia.page.scss'],
})
export class NoticiaPage implements OnInit {

  slug: string;
  strapiUrl = environment.strapiURL;
  noticias: RespuestaStrapi[] = [];
  source: string;
  uid: any;

  noticiaM: any;
  noticiaP: any;
  noticeLog: any;
  user: any;
  titulo: any;
  // noticia: any;
  noticiaMarked: any;
  abstracto: any;
  categoria: any;
  img: any;
  createdAt: any;

  constructor(private strapi: StrapiService,
              private activatedRoute: ActivatedRoute,
              // private db: AngularFirestore,
              private firestore: Firestore,
              private content: ContentService,
              private browser: BrowserService,
              private interaction: InteractionService,
              private analyticsService: AnalyticsService,



  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.slug = result.slug;
    });

  }





  async ngOnInit() {
    await this.getUserData();
    await this.getArticleData();
    await this.analyticsService.setCurrentScreen(`noticias/${this.slug}`);

  }


  // Obtiene la informacion del curso en strapi
  async getArticleData(){
    const articleResponse = await this.content.strapiGet(`noticias?slug=${this.slug}`)

    const articleData = articleResponse.data[0];
    this.titulo = articleData.Titulo;
    // this.noticia = articleData.Noticias;
    this.noticiaMarked = marked(articleData.Noticia);
    this.abstracto = articleData.Abstracto;
    this.categoria = articleData.categorias_de_noticia.categoria;
    this.source = articleData.source
    console.log('Categoria all: ', articleData)
    this.img = articleData.Imagen_principal;

    this.createdAt = articleData.created_at;

    await this.setInteraction(articleData);

  }

  async setInteraction(noticia) {


    const data= {
      titulo: noticia.Titulo,
      // aditionalData: noticia.PrincipioActivo,
      imagen: this.img.url,
      categoria: this.categoria,
      // slug: this.slug,
      type: 'noticia'
    }

    console.log(data)

    // const slug = this.interaction.slugify(producto.Nombre)
    await this.interaction.registrarInteraccion(this.uid, `/noticias/${this.slug}`, data);
  }

  async getUserData(){
    const ret = await Preferences.get({ key: 'user' });
    this.user = JSON.parse(ret.value);
    this.uid = this.user.uid;

  }
  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }

}
