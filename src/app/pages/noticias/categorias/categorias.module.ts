import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CategoriasPageRoutingModule } from './categorias-routing.module';

import { CategoriasPage } from './categorias.page';
import {NgPipesModule} from 'ngx-pipes';
import {PipesModule} from '../../../pipes/pipes.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CategoriasPageRoutingModule,
    NgPipesModule,
    PipesModule,


  ],
  declarations: [CategoriasPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]

})
export class CategoriasPageModule {}
