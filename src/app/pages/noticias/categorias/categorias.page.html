<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{categoria}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/noticias"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-searchbar animated placeholder="Buscar Noticia" (ionInput)="buscar( $event )"></ion-searchbar>

</ion-header>

<ion-content>

  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-grid>
    <ion-row>
      <ion-col size="12" size-lg="1" size-xl="2"></ion-col>

      <ion-col size="12" size-lg="10" size-xl="8">




        <ion-grid >
          <ion-row >

            <ion-col size="12" size-xl="3" size-lg="4" size-md="6" size-sm="6" size-xs="6" *ngFor="let noticia of noticias | orderBy: orderBy | filterBy: ['Titulo', 'categorias_de_noticia.categoria', 'Abstracto'] : textoBuscar: false ; let i = index">
              <ion-card button="true" (click)="openArticle(noticia.slug)"  class="newsCard"  mode="md">
                <ion-img *ngIf="noticia.Imagen_principal.url" src="{{strapiUrl}}{{noticia.Imagen_principal.url}}"></ion-img>
                <ion-card-header class="newsCardHeader">
                  <!--            <ion-card-title>{{noticia.Titulo}}</ion-card-title>-->

                  <ion-card-title color="primary" *ngIf="noticia.Titulo" class="title-container">
                    <span class="title-text">{{noticia.Titulo }}</span>
                    <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
                  </ion-card-title>


                </ion-card-header>


                <ion-card-content class="textCardContent">
            <span class="textContent">
              {{noticia.Abstracto}}
            </span>

                  <ion-card-subtitle class="newsCardDate"><ion-icon name="calendar"></ion-icon> {{noticia.fecha | date}}</ion-card-subtitle>


                </ion-card-content>

              </ion-card>
            </ion-col>
          </ion-row>
        </ion-grid>

      </ion-col>
      <ion-col size="12" size-lg="1" size-xl="2"></ion-col>

    </ion-row>

  </ion-grid>




</ion-content>
