import { Component, OnInit } from '@angular/core';
import {StrapiService} from '../../../services/strapi.service';
import {ActivatedRoute, Router} from '@angular/router';
import {Firestore, doc, docData, collection, getDoc, writeBatch} from '@angular/fire/firestore';

import {ActionSheetController} from '@ionic/angular';
import {environment} from '../../../../environments/environment';

@Component({
  selector: 'app-categorias',
  templateUrl: './categorias.page.html',
  styleUrls: ['./categorias.page.scss'],
})
export class CategoriasPage implements OnInit {

  strapiUrl = environment.strapiURL;

  slug: any;
  categoria: any;
  noticias: any;

  slideOpts = {
    initialSlide: 0,
    speed: 400,
    pager: true,
    autoplay: true

  };
  activado: any;
  slides: any;

  textoBuscar = '';

  searchBy = 'Titulo';

  order = 'updated_at';
  ordenadoPor = 'Fecha de actualización';
  upDown = true;
  upDownText = '-';
  constructor(private strapi: StrapiService,
              private activatedRoute: ActivatedRoute,
              // private db: AngularFirestore,
              private firestore: Firestore,
              public actionSheetController: ActionSheetController,
              private router: Router,


  ) {
    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.slug = result.id;
    });
  }

  ngOnInit() {
    console.log(this.slug);


    this.strapi.getCategoriaNoticias(`${this.slug}`).subscribe(resp => {

      this.categoria = resp[0].categoria;
      this.noticias = resp[0].noticias;



      console.log('Respuesta Completa', resp);
      console.log('Categoria this', this.categoria);



      console.log('Cursos this', this.noticias);




    });

    this.strapi.getContenido('slides/2').subscribe(resp => {
      this.activado = resp.Activado;
      this.slides = resp.Slides;
      // console.log('Slides Respuesta', this.slides);

    });

  }

  handleRefresher(event) {
    if (event) {
      event.target.complete();
    }
  }

  doRefresh(event) {

    this.cargarInfo();



  }


  openArticle(slug: string) {
    this.router.navigate(['/noticias', slug]);
  }

  cargarInfo() {
    this.strapi.getContenido(`categoria-de-noticias/${this.slug}`)
        .subscribe(resp => {
          this.categoria = resp[0].categoria;
          this.noticias = resp[0].noticias;

          // console.log('Datos API', resp);
        });
    this.handleRefresher(event);
  }



  async orderBy() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Ordenar por:',
      // cssClass: 'my-custom-class',
      buttons: [{
        text: 'Titulo del curso',
        role: 'Titulo',
        handler: () => {
        }
      }, {
        text: 'Autor del curso',
        role: 'autor',
        handler: () => {
        }
      }, {
        text: 'Fecha de publicación',
        role: 'fecha',
        handler: () => {
        }
      }, {
        text: 'Fecha de actualización',
        role: 'updated_at',
        handler: () => {
        }
      }, {
        text: 'Duración del curso',
        role: 'duracion',
        handler: () => {
        }
      }]
    });
    await actionSheet.present();

    const { role } = await actionSheet.onDidDismiss();
    this.order = role;

    // if (this.order === 'autor') {
    //   this.ordenadoPor = 'Autor';
    // }



    switch (this.order) {
      case this.order = 'Titulo':
        this.ordenadoPor = 'Titulo del curso';
        break;
      case this.order = 'autor':
        this.ordenadoPor = 'Autor';
        break;

      case this.order = 'fecha':
        this.ordenadoPor = 'Fecha de publicacion';
        break;
      case this.order = 'updated_at':
        this.ordenadoPor = 'Fecha de actualización';
        break;
      case this.order = 'duracion':
        this.ordenadoPor = 'Duracion del curso';
        break;

    }



  }

  upDownOperator() {

    if (this.upDown === true) {
      this.upDownText = '-';
    } else {
      this.upDownText = '';

    }

  }
  buscar( event ) {
    // console.log(event)
    this.textoBuscar = event.detail.value;
  }
}
