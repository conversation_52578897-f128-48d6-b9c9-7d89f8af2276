import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { NoticiasPage } from './noticias.page';
import {NgPipesModule} from 'ngx-pipes';
import {PipesModule} from '../../pipes/pipes.module';
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";

const routes: Routes = [
  {
    path: '',
    component: NoticiasPage
  }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        NgPipesModule,
        PipesModule,
        RouterModule.forChild(routes),
        SharedComponentsModule
    ],
  declarations: [NoticiasPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]

})
export class NoticiasPageModule {}
