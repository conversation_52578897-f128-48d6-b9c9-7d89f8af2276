<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Noticias <PERSON></ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="openSearchModal()">
        <ion-icon name="search" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
<!--  <ion-searchbar animated placeholder="Buscar Nota" (ionChange)="buscar( $event )"></ion-searchbar>-->

</ion-header>

<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="noticiasRefresh($event)"  *ngIf="userInStorage.role === 'medico'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>


  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col>
        <swiper-container pagination="true" slides-per-view="1"  class="ion-no-padding ion-no-margin swiperStyleNews" >
          <swiper-slide *ngFor="let slide of slideNoticias">

            <ion-card class="mainSlideCard">
              <ion-img *ngIf="slide.type === 'image'" (click)="openBrowser(slide.link)" [src]="strapiUrl + slide.image.url"></ion-img>
              <app-youtube-player *ngIf="slide.type === 'video'" [videoId]="slide.videoURL" [autoplay]="false"></app-youtube-player>
            </ion-card>
          </swiper-slide>
        </swiper-container>
        <swiper-container slides-per-view="auto"  slides-offset-before="10" slides-offset-after="10">
          <swiper-slide class="chips" *ngFor="let categoria of categorias | orderBy: '-fecha'">

            <ion-chip  (click)="openCategory(categoria.slug)">
              <ion-label>{{categoria.categoria}}</ion-label>
              <ion-icon color="primary" name="arrow-forward"></ion-icon>
            </ion-chip>
          </swiper-slide>
        </swiper-container>

        <ion-button *ngIf="searchInProgress" (click)="limpiarBusqueda();"><ion-icon name="close"></ion-icon> Borrar resultados</ion-button>

      </ion-col>

    </ion-row>
    <ion-row >

      <ion-col size="12" size-xl="3" size-lg="4" size-md="6" size-sm="6" size-xs="6" *ngFor="let noticia of noticias | orderBy: orderBy | filterBy: ['Titulo', 'categorias_de_noticia.categoria', 'Abstracto'] : textoBuscar: false ; let i = index">

        <ion-card button="true" (click)="openArticle(noticia.slug)"  class="newsCard"  mode="md">
          <ion-img *ngIf="noticia.Imagen_principal.url" src="{{strapiUrl}}{{noticia.Imagen_principal.url}}"></ion-img>
          <ion-card-header class="newsCardHeader">
<!--            <ion-card-title>{{noticia.Titulo}}</ion-card-title>-->

            <ion-card-title color="primary" *ngIf="noticia.Titulo" class="title-container">
              <span class="title-text">{{noticia.Titulo }}</span>
              <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
            </ion-card-title>

            <ion-card-subtitle color="secondary"  class="newsCardSubtitle">{{noticia.categorias_de_noticia.categoria}}             <ion-icon  name="arrow-forward" color="primary" class="category-icon"></ion-icon>
            </ion-card-subtitle>


          </ion-card-header>


          <ion-card-content class="textCardContent">
            <span class="textContent">
              {{noticia.Abstracto}}
            </span>

            <ion-card-subtitle class="newsCardDate"><ion-icon name="calendar"></ion-icon> {{noticia.fecha | date}}</ion-card-subtitle>


          </ion-card-content>

        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>


  <ion-modal
    #searchModal
    [isOpen]="false"
    [initialBreakpoint]="0.25"
    [breakpoints]="[0, 0.25]"

  >
    <ng-template>
      <ion-content class="ion-padding" color="primary">
        <!--        <ion-searchbar placeholder="Search" (click)="searchModal.setCurrentBreakpoint(0.5)"></ion-searchbar>-->
        <ion-searchbar #searchBar color="light" placeholder="Buscar noticia" (click)="searchModal.setCurrentBreakpoint(0.25)" (ionInput)="buscar( $event )"></ion-searchbar>

        <div class="ion-text-center">
          <ion-chip class="chipWhite">
            <ion-label>Ordenar y buscar por:</ion-label>
          </ion-chip>


          <ion-chip (click)="selecionado('-fecha')" [ngStyle]="{'color':orderBy === '-fecha' ? 'white' : '', 'background-color':orderBy === '-fecha' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === '-fecha' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas recientes</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('fecha')" [ngStyle]="{'color':orderBy === 'fecha' ? 'white' : '', 'background-color':orderBy === 'fecha' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'fecha' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas antiguas</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('Titulo')" [ngStyle]="{'color':orderBy === 'Titulo' ? 'white' : '', 'background-color':orderBy === 'Titulo' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'Titulo' ? 'red' : ''}"></ion-icon>
            <ion-label>Alfabeticamente</ion-label>
          </ion-chip>
          <ion-chip  (click)="selecionado('categorias_de_noticia.categoria')" [ngStyle]="{'color':orderBy === 'categorias_de_noticia.categoria' ? 'white' : '', 'background-color':orderBy === 'categorias_de_noticia.categoria' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'categorias_de_noticia.categoria' ? 'red' : ''}"></ion-icon>
            <ion-label>Categoria</ion-label>
          </ion-chip>
        </div>

      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>
