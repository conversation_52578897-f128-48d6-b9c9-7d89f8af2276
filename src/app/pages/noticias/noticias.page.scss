
swiper-slide.chips {
  width: auto;
  flex-shrink: 0; /* Esto asegura que los elementos no se reduzcan */
}

// Estilos para el swiper general
.mySwiper {
  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; // Asegura que todo se mantenga dentro de los bordes redondeados
  }
}

.textCardContent{
  .textContent {
    font-size: 12px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; // Limit to four lines
    overflow: hidden; // Hide overflow
    text-overflow: ellipsis; // Add ellipsis at the end

  }

}

.mainSlideCard{
  margin-top: 10px;
  margin-bottom: 10px;

  border-radius: 16px; // Radio de borde que se asemeje al diseño

  //border-radius: 0px 0px 16px 16px; // Radio de borde que se asemeje al diseño

}

// Estilos específicos para ion-card
ion-card.newsCard {
  height: auto; // o una altura fija dependiendo del diseño
  margin: 0;

  // Redondear las esquinas
  border-radius: 16px; // Radio de borde que se asemeje al diseño

  // Sombra para la tarjeta
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12); // Ajustar según la imagen

  // Estilo para la imagen de la tarjeta
  ion-img {
    width: 100%; // Asegura que la imagen cubra todo el ancho de la tarjeta
    height: 150px; // Altura fija para la imagen, ajustar según necesidad
    object-fit: cover; // Asegura que la imagen cubra todo el espacio sin deformarse
    border-top-left-radius: 16px; // Radio de borde superior izquierdo
    border-top-right-radius: 16px; // Radio de borde superior derecho
  }

  ion-card-title {
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 8px; // Espacio debajo del título
  }



  .newsCardHeader {
    padding-top: 0;
    padding-bottom: 0;
  }

  .newsCardDate {
    margin-top:8px;
    font-size: 8px;
    //padding-bottom: 5px;

  }
  .newsCardSubtitle{
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 10px;

  }



  .title-container {
    padding: 10px 0 0px 0;
    display: flex; // Use Flexbox for layout
    align-items: center; // Align items vertically in the center

    .title-text {
      flex-grow: 1; // Allows the text to expand
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; // Limit to two lines
      overflow: hidden; // Hide overflow
      text-overflow: ellipsis; // Add ellipsis to overflow
      margin-right: 10px; // Add some margin before the icon
    }

    .title-icon {
      // Style as needed
    }
  }
  // Estilos para el contenido de la tarjeta
  .card-content {
    //padding: 16px; // Espaciado interior para el contenido

    // Estilos para el título
    h2 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px; // Espacio debajo del título
    }

    // Estilos para la descripción o el cuerpo del texto
    p {
      font-size: 14px;
      color: #666; // Color de texto gris, ajustar según diseño
    }

    // Estilos para el pie de tarjeta, donde podría ir la información del autor
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px; // Espacio encima del pie de tarjeta

      // Estilos para el autor o fuente de la noticia
      .author {
        font-size: 12px;
        font-weight: bold;
        color: #333; // Color más oscuro para el texto del autor
      }

      // Estilos para los detalles como tiempo de lectura y votos
      .details {
        font-size: 12px;
        color: #999; // Color más claro para los detalles
      }
    }
  }
}

