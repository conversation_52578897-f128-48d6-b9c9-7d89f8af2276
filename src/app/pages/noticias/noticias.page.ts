import {Component, OnInit, ViewChild} from '@angular/core';
import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';
import {RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {Router} from '@angular/router';
import {WidgetUtilService} from "../../services/widget-util.service";
import {Preferences} from "@capacitor/preferences";
import {ContentService} from "../../services/content.service";
import {Browser} from "@capacitor/browser";
import {BrowserService} from "../../services/browser.service";
import {IonInput, IonModal} from "@ionic/angular";
import {AnalyticsService} from "../../services/analytics.service";
// import {WidgetUtilService} from '../../providers/widget-util.service';
// import {Plugins} from '@capacitor/core';
//
// const { Storage } = Plugins;



// Actualizacion de Capacitor a 3.0
// import { Storage } from '@capacitor/storage';

@Component({
  selector: 'app-noticias',
  templateUrl: './noticias.page.html',
  styleUrls: ['./noticias.page.scss'],
})
export class NoticiasPage implements OnInit {
  @ViewChild('searchModal') searchModal: IonModal;
  @ViewChild('searchBar') searchBar!: IonInput;

  userInStorage: any = [];
  orderBy: string = '-fecha';
  searchInProgress: boolean = false;

  userContent: any;
  slideNoticias: any

  strapiUrl = environment.strapiURL;

  textoBuscar = '';
  slides: any = [];

  categorias: any;

  noticias: any;

  news: any = []

  slideOpts = {
    loop: false,
    speed: 800,
    slidesPerView: 'auto',
    grabCursor: true,
    spaceBetween: 2,
    initialSlide: 0,
    keyboardControl: false,
    resizeReInit: true,
    autoplayDisableOnInteraction: false,
    pagination: '.swiper-pagination',
    paginationClickable: false,
    nextButton: '.swiper-next',
    prevButton: '.swiper-prev'
  };

  slideChips = {
    loop: false,
    speed: 800,
    slidesPerView: 'auto',
    grabCursor: true,
    spaceBetween: 2,
    initialSlide: 0,
    keyboardControl: false,
    resizeReInit: true,
    autoplayDisableOnInteraction: false,
    pagination: '.swiper-pagination',
    paginationClickable: false,
    nextButton: '.swiper-next',
    prevButton: '.swiper-prev'

  };

  constructor(
    private strapi: StrapiService,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private content: ContentService,
    private browser: BrowserService,
    private analyticsService: AnalyticsService,


  ) {}

  async ngOnInit() {
    await this.widgetUtilService.presentLoading();

    await this.getUserContent();
    await this.getUserInStorage();
    await this.widgetUtilService.dismissLoader();
    await this.analyticsService.setCurrentScreen('Noticias' );



  }

  openArticle(slug: string) {
    this.router.navigate(['/noticias', slug]);
  }


  buscar( event: any ) {
    if(event.detail.value == '' || null) {
      console.log('busqueda no realizada')
      this.searchInProgress = false
    } else {
      this.searchInProgress = true
      console.log(event, 'Busqueda iniciada searchInProgress', this.searchInProgress);
      this.textoBuscar = event.detail.value;
    }

  }


  openCategory(slug) {
    this.router.navigate(['/categorias', slug]);
  }

  async getUserInStorage(){
    const ret = await Preferences.get({ key: 'user' });
    this.userInStorage = JSON.parse(ret.value);

    console.log(this.userInStorage.role)

  }




  async noticiasRefresh(event: any) {

    // console.log('contenido actualizado en el home', event)
    await this.content.getUser(this.userInStorage);
    await this.content.getNews(300);
    await this.getUserContent();
    event.target.complete();
  }



  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    this.userContent = await JSON.parse(userContent.value);
    const banners = this.userContent.banners
    // console.log('Cursos loaded: ', banners)

    // Slides de la academia
    this.slideNoticias = banners.slideNoticias

    const bannersOriginalArray = banners.banners
    const bannersFiltrados = bannersOriginalArray.filter((resp: any) => {
      return resp.location === 'noticias';
    })

    this.slideNoticias = bannersFiltrados

    const news = await this.content.getNews(300);
    // const newsStorage = await Preferences.get({ key: 'news' });
    // const news = JSON.parse(newsStorage.value);

    this.noticias = news.noticias
    this.categorias = news.categorias

    console.log('Categorias: ', this.categorias);
    console.log('Noticias: ', this.noticias);



    // await this.widgetUtilService.dismissLoader();
  }

  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }

  async openSearchModal() {
    await this.searchModal.present();
    await this.searchBar.setFocus();

  }

  selecionado(event: string) {
    this.orderBy = event;

  }


  limpiarBusqueda () {
    this.searchInProgress = false
    this.textoBuscar = ''

  }
}
