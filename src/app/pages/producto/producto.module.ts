import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ProductoPageRoutingModule } from './producto-routing.module';

import { ProductoPage } from './producto.page';
import {NgArrayPipesModule, NgStringPipesModule} from "ngx-pipes";
import {PipesModule} from "../../pipes/pipes.module";

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        ProductoPageRoutingModule,
        NgArrayPipesModule,
        NgStringPipesModule,
        PipesModule
    ],
  declarations: [ProductoPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]

})
export class ProductoPageModule {}
