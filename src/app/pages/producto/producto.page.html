<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{productName}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/vademecum"></ion-back-button>
    </ion-buttons>

    <ion-icon slot="end" name="download" size="large" (click)="descargarPDF()"></ion-icon>

  </ion-toolbar>
</ion-header>

<ion-content  >
  <ion-grid [fixed]="true">


    <ion-fab *ngIf="modeloSwitch">
      <ion-fab-button (click)="img3d=!img3d" [ngClass]="img3d ? 'img2d' : 'img3d'">
        <ng-container *ngIf="img3d">
          2D
        </ng-container>
        <ng-container *ngIf="!img3d">
          3D
        </ng-container>
      </ion-fab-button>
    </ion-fab>



    <!--    <ion-button (click)="compartir()" expand="block" >Compartir {{producto.Nombre}} con el paciente</ion-button>-->

    <ng-container *ngIf="img3d">
      <iframe [src]='modeloURL | safeUrl' frameborder='0' width='100%' height='350px'></iframe><!--    <ion-img class="centrar_imagen" style="max-width: 400px!important" *ngIf="producto.Imagen_de_producto[0]" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
    </ng-container>
    <ng-container *ngIf="!img3d">
<!--      <swiper-container *ngIf="slideVademecumSwitch" pagination="true" slides-per-view="1"  class="ion-no-padding ion-no-margin swiperStyleSanfer" >-->
<!--        <swiper-slide *ngFor="let slide of slideVademecum">-->
<!--          <ion-img class="centrar_imagen" style="margin-bottom: 20px;"  (click)="openBrowser(slide.link)" src="{{strapiUrl}}{{slide.image.url}}"></ion-img>-->
<!--        </swiper-slide>-->
<!--      </swiper-container>-->
      <!--      <ion-slides pager="true" [options]="slideOpts" style="max-width: 800px" >-->
      <!--        <ion-slide>-->
      <!--          <ion-img style="margin-bottom: 40px" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
      <!--        </ion-slide>-->
      <!--        <ion-slide>-->
      <!--          <ion-img style="margin-bottom: 40px" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
      <!--        </ion-slide>-->
      <!--      </ion-slides>-->
<!--      <ion-fab slot="fixed" vertical="top" horizontal="end" [edge]="true">-->
<!--        <ion-fab-button (click)="compartir()">-->
<!--          <ion-icon name="share"></ion-icon>-->
<!--        </ion-fab-button>-->
<!--      </ion-fab>-->
      <ion-img class="centrar_imagen" style="max-width: 400px!important" *ngIf="producto.Imagen_de_producto" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>
    </ng-container>



  <ion-list style="padding-bottom: 10px">
    <ion-item color="primary">
      <ion-icon name="medkit-outline" slot="start"></ion-icon>
      <ion-label class="ion-text-wrap">
        <h1 class="productName" >{{producto.Nombre}}</h1>
      </ion-label>
    </ion-item>
    <ion-item >
      <ion-icon name="water-outline" slot="start" color="primary"></ion-icon>
      <ion-label class="ion-text-wrap">
        <h1>Denominación genérica:</h1>
        <p>{{producto.PrincipioActivo}}</p>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-icon name="flask-outline" slot="start" color="primary"></ion-icon>
      <ion-label class="ion-text-wrap">
        <h1>Forma farmacéutica y formulación:</h1>
        <p>{{producto.Concentracion}}</p>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-icon name="document-text-outline" slot="start" color="primary"></ion-icon>
      <ion-label class="ion-text-wrap">
        <h1>Descripcion:</h1>
        <p>{{producto.Descripcion}}</p>
      </ion-label>
    </ion-item>

    <ion-item *ngIf="multiProduct" color="primary">
      <ion-icon name="albums-outline" slot="start" ></ion-icon>
      <ion-label class="ion-text-wrap">
        <h1>Presentaciones:</h1>
      </ion-label>
    </ion-item>
  </ion-list>

  <ion-list *ngIf="!multiProduct">
    <div *ngFor="let item of producto.ipp">
      <ion-item button detail="false" (click)="toggleSection(item.id)" [ngClass]="{'expanded': expanded === item.id, 'collapsed': expanded !== item.id}" >
        <ion-icon slot="start" name="chevron-forward-circle-outline" *ngIf="expanded !== item.id" color="primary"></ion-icon>
        <ion-icon slot="start" name="chevron-down-circle" *ngIf="expanded === item.id"  color="primary"></ion-icon>
        <ion-label class="ion-text-wrap">
          <h1>{{ item.title | ucwords}}</h1>
          <div *ngIf="expanded === item.id">
            <img style="padding-top: 10px; padding-bottom: 10px" *ngIf="item.image" [src]="strapiUrl+item.image.url">
            <p *ngIf="item.description" style="text-align: justify;   text-justify: auto;" [innerHTML]="item.description | mark"></p>
          </div>
        </ion-label>
      </ion-item>
    </div>
  </ion-list>

<!--      <span class="text-primary"><ion-icon name="water"></ion-icon> Denominación genérica: </span>{{producto.PrincipioActivo}}-->
<!--      <br><span class="text-primary"><ion-icon name="flask"></ion-icon> Forma farmacéutica y formulación: </span><br> {{producto.Concentracion}}<br>-->
<!--  <span class="text-primary"><ion-icon name="water"></ion-icon> Descripcion: </span>{{producto.Descripcion}}-->


  <swiper-container *ngIf="multiProduct" pagination="true" space-between="5"  slides-per-view="2.5" slides-offset-before="10" slides-offset-after="15" class="swiperStyleSanfer" >
          <swiper-slide  *ngFor="let medicamento of medicamentos">

            <ion-card button="true" (click)="openInfoModal(medicamento)"  mode="md" style="margin-bottom: 20px">
              <ion-img src="{{strapiUrl}}{{medicamento.Imagen_de_producto[0].url}}"></ion-img>
              <ion-card-header>

                <ion-card-title color="primary"  *ngIf="medicamento.Nombre" class="title-container">

                  {{(medicamento.Nombre.length>13)? (medicamento.Nombre | slice:0:13)+'...':(medicamento.Nombre) }}

                  <ion-icon color="primary"  name="arrow-forward"></ion-icon>
                </ion-card-title>

                <ion-card-subtitle color="secondary" *ngIf="medicamento.PrincipioActivo">{{ (medicamento.PrincipioActivo.length>20)? (medicamento.PrincipioActivo | slice:0:20)+'...':(medicamento.PrincipioActivo) }}</ion-card-subtitle>

              </ion-card-header>
              <ion-card-content>

<!--                {{medicamento.Descripcion}}-->
              </ion-card-content>
            </ion-card>
          </swiper-slide>
        </swiper-container>




  <ion-item >
    <ion-icon name="barcode-outline" slot="start" color="primary"></ion-icon>
    <ion-label class="ion-text-wrap">
      <p>EAN: {{producto.SKU}}</p>
    </ion-label>
  </ion-item>
  <ion-item >
    <ion-icon name="book-outline" slot="start" color="primary"></ion-icon>
    <ion-label class="ion-text-wrap">
      <p>Registro sanitario: {{producto.Registro_sanitario}}</p>
    </ion-label>
  </ion-item>

  <ion-button *ngIf="multiProduct" size="large" expand="block" (click)="descargarPDF()" >Descargar IPPs</ion-button>

  <!--  <span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>{{producto.SKU}}<br>-->
<!--  <ion-card-subtitle><span class="text-primary"><ion-icon name="book"></ion-icon> Registro sanitario:</span> {{producto.Registro_sanitario}}</ion-card-subtitle>-->


  <!--      <ng-container *ngFor="let info of producto.ipp">-->
<!--&lt;!&ndash;        (click)="openInfoModal(info)"&ndash;&gt;-->
<!--        <ion-card type="button" button="true" style="margin: 5px 0; " class=" ion-no-margin" color="primary">-->

<!--          <ion-card-content class="ion-no-padding">-->
<!--            <ion-grid>-->
<!--              <ion-row>-->
<!--                <ion-col class="ion-no-padding" size="0.5" size-md="1">-->


<!--                </ion-col>-->
<!--                <ion-col class="ion-no-padding" size="11" size-md="10" >-->
<!--                  {{ info.title | ucwords }}-->
<!--                </ion-col>-->
<!--                <ion-col class="ion-no-padding" size=".5" size-md="1" >-->
<!--                    <ion-icon-->
<!--                      color=""-->
<!--                      style="-->
<!--                        text-align: right;-->
<!--                        height: 100%;-->
<!--                        /*display: block;*/-->
<!--                        vertical-align: middle;"-->
<!--                      name="arrow-forward"></ion-icon>-->



<!--                </ion-col>-->
<!--              </ion-row>-->
<!--            </ion-grid>-->
<!--          </ion-card-content>-->
<!--        </ion-card>-->


<!--      </ng-container>-->



  <ion-button *ngIf="!multiProduct" size="large" expand="block" (click)="descargarPDF()" >Descargar IPP</ion-button>





  <!--      <ion-button *ngIf="producto.BP == true" (click)="openPrecio(producto.id, producto)" >Buscar mejor precio</ion-button>-->

  </ion-grid>



  <ion-modal
    #infoModal
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="1"
    [breakpoints]="[0, 1]"

    [backdropDismiss]="true"
  >
    <ng-template>

<!--      <ion-header *ngIf="!ippImage">-->
<!--        <ion-toolbar [color]="ippContent.color" class="ion-text-center">-->
<!--          {{ ippContent.title }}-->
<!--          <ion-buttons slot="end">-->
<!--            <ion-button (click)="cancel()">-->
<!--              <ion-icon name="close"></ion-icon>-->
<!--            </ion-button>-->
<!--          </ion-buttons>-->
<!--        </ion-toolbar>-->
<!--      </ion-header>-->
      <ion-content  [color]="ippContent.color">
        <ion-fab slot="fixed" vertical="top" horizontal="start">
          <ion-fab-button (click)="compartir()">
            <ion-icon name="share"></ion-icon>
          </ion-fab-button>
        </ion-fab>
        <ion-fab slot="fixed" vertical="top" horizontal="end">
          <ion-fab-button (click)="closeModal()">
            <ion-icon name="close"></ion-icon>
          </ion-fab-button>
        </ion-fab>
        <ion-img *ngIf="ippImage" [src]="ippImage"></ion-img>

        <ion-list style="padding-bottom: 10px">
          <ion-item color="primary">
            <ion-icon name="medkit-outline" slot="start"></ion-icon>
            <ion-label class="ion-text-wrap">
              <h1 style="font-weight: bold">{{ippContent.Nombre}}</h1>
            </ion-label>
          </ion-item>
          <ion-item >
            <ion-icon name="water-outline" slot="start" color="primary"></ion-icon>
            <ion-label class="ion-text-wrap">
              <h1>Denominación genérica:</h1>
              <p>{{ippContent.PrincipioActivo}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-icon name="flask-outline" slot="start" color="primary"></ion-icon>
            <ion-label class="ion-text-wrap">
              <h1>Forma farmacéutica y formulación:</h1>
              <p>{{ippContent.Concentracion}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-icon name="document-text-outline" slot="start" color="primary"></ion-icon>
            <ion-label class="ion-text-wrap">
              <h1>Descripcion:</h1>
              <p>{{ippContent.Descripcion}}</p>
            </ion-label>
          </ion-item>
        </ion-list>
        <ion-list>
          <div *ngFor="let item of ippContent.ipp">
            <ion-item button detail="false" (click)="toggleSection(item.id)" [ngClass]="{'expanded': expanded === item.id, 'collapsed': expanded !== item.id}" >
              <ion-icon slot="start" name="chevron-forward-circle-outline" *ngIf="expanded !== item.id" color="primary"></ion-icon>
              <ion-icon slot="start" name="chevron-down-circle" *ngIf="expanded === item.id"  color="primary"></ion-icon>
              <ion-label class="ion-text-wrap">
                <h1>{{ item.title | ucwords}}</h1>
                <div *ngIf="expanded === item.id">
                  <img style="padding-top: 10px; padding-bottom: 10px" *ngIf="item.image" [src]="strapiUrl+item.image.url">
                  <p *ngIf="item.description" style="text-align: justify;   text-justify: auto;" [innerHTML]="item.description | mark"></p>
                </div>
              </ion-label>
            </ion-item>
          </div>
        </ion-list>
        <ion-item >
          <ion-icon name="barcode-outline" slot="start" color="primary"></ion-icon>
          <ion-label class="ion-text-wrap">
            <p>EAN: {{ippContent.SKU}}</p>
          </ion-label>
        </ion-item>
        <ion-item >
          <ion-icon name="book-outline" slot="start" color="primary"></ion-icon>
          <ion-label class="ion-text-wrap">
            <p>Registro sanitario: {{ippContent.Registro_sanitario}}</p>
          </ion-label>
        </ion-item>

        <ion-button *ngIf="multiProduct" size="large" expand="block" (click)="descargarPDF()" >Descargar IPP</ion-button>

<!--        <h1 *ngIf="ippImage">-->
<!--          {{ ippContent.title }}-->
<!--        </h1>-->

<!--        <div *ngIf="textContent"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></div>-->

<!--        {{ippContent.description}}-->
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>

