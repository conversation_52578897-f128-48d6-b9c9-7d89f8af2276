//ion-card{
//
//  margin: 10px 0px!important;
//  padding: 0;
//  box-shadow:
//    //rgba(255, 0, 0, 0.2) -2px 2px 5px -2px, rgba(255, 0, 0, 0.14) -2px 2px 5px -2px, rgba(255, 0, 0, 0.12) 2px 2px 5px 2px
//    rgba(16, 131, 156, 0.2) -2px 2px 5px -2px, rgba(16, 131, 156, 0.14) -2px 2px 5px -2px, rgba(16, 131, 156, 0.12) 2px 2px 5px 2px
//
//    //16, 131, 156
//  //box-shadow:none !important
//
//}

h1 {

  font-size: 18px;
}
h1.productName {
  font-size: 24px;
  font-weight: bold;
}
p {
  font-size: 14px;
  color: #2e2f2f;
}


ion-card{

  margin: 5px;
  padding: 0;
  box-shadow:
    rgb(255 0 0 / 20%) 0px 3px 5px -2px,
    rgb(255 0 0 / 14%) 0px 3px 0px 0px,
    rgb(255 0 0 / 12%) 0px 1px 5px 1px;
  //box-shadow:none !important

}

.imagen-producto {
  height: 220px; /* Altura fija para todas las imágenes */
  width: 100%; /* Ancho responsivo basado en el ion-card */
  overflow: hidden; /* Oculta cualquier parte de la imagen que exceda el tamaño del contenedor */
  display: flex;
  align-items: center; /* Centra la imagen verticalmente */
  justify-content: center; /* Centra la imagen horizontalmente */
}

.imagen-producto img {
  max-height: 100%; /* Asegura que la altura de la imagen no exceda la del contenedor */
  max-width: 100%; /* Permite que la imagen se escale de manera responsiva */
  object-fit: cover; /* Asegura que la imagen cubra todo el contenedor sin distorsionarse */
}
ion-card-header{
  padding: 0 5px 0 5px;
}

ion-card-title{

  font-size: 14px;
}

ion-card-subtitle{
  margin-top: 0px;
  font-size: 8px;

}

ion-card-content {
  font-size: 10px;
  padding: 5px;

}


.expanded h1{
  /* Estilos cuando el ítem está expandido */
  color: var(--ion-color-primary);
  font-weight: bold;
}

//.expanded ion-item{
//  /* Estilos cuando el ítem está expandido */
//  background: var(--ion-color-primary);
//}

.collapsed {
  /* Estilos cuando el ítem está colapsado */
}
