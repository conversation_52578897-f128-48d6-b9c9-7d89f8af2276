import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {IonModal, ModalController, Platform} from "@ionic/angular";
import {environment} from "../../../environments/environment";
import {RespuestaStrapi} from "../../interfaces/strapiInterfaces";
import {StrapiService} from "../../services/strapi.service";
import {ActivatedRoute, Router} from "@angular/router";
import {FirebaseAuthService} from "../../services/firebase-auth.service";
import {WidgetUtilService} from "../../services/widget-util.service";
import {Preferences} from "@capacitor/preferences";
import {marked} from "marked";
import {VademecumPromocionesPage} from "../vademecum-promociones/vademecum-promociones.page";
import {VademecumCompartirPage} from "../vademecum-compartir/vademecum-compartir.page";
import {<PERSON>rowser} from "@capacitor/browser";
import {Directory, Filesystem} from "@capacitor/filesystem";
import {Share} from "@capacitor/share";
import {OverlayEventDetail} from "@ionic/core/components";
// import * as pdfMake from "pdfmake/build/pdfmake";
// import * as pdfFonts from 'pdfmake/build/vfs_fonts';
import {ContentService} from "../../services/content.service";
import {DataService} from "../../services/data.service";
import {BrowserService} from "../../services/browser.service";
import {PdfgeneratorService} from "../../services/pdfgenerator.service";
import {VademecumsResponse} from "../../models/vademecums.model";
import {InteractionService} from "../../services/interaction.service";
import {AnalyticsService} from "../../services/analytics.service";

// import * as pdfMake from 'pdfmake/build/pdfmake';
// import * as pdfFonts from 'pdfmake/build/vfs_fonts';
//
// function configurePdfMake() {
//   (pdfMake as any).vfs = pdfFonts.pdfMake.vfs;
// }

@Component({
  selector: 'app-producto',
  templateUrl: './producto.page.html',
  styleUrls: ['./producto.page.scss'],
})
export class ProductoPage implements OnInit, OnDestroy {
  @ViewChild(IonModal) modal: IonModal;
  @ViewChild('infoModal') infoModal: IonModal;

  expanded: number | null = null;


  modeloSwitch: boolean = false;
  modeloURL: string;
  multiProduct: boolean = false;
  medicamentos: any;
  ippContent: any = {};
  ippImage: string;
  textContent: any;


  message = 'This modal example uses triggers to automatically open a modal when the button is clicked.';
  name: string;


  user: any;
  uid: any;

  id = '';
  slug: string;
  strapiUrl = environment.strapiURL;
  producto: any = {};
  productName: string;
  // productoMarked: any;
  // presnetacion: RespuestaStrapi[] = [];
  presentaciones: RespuestaStrapi[] = [];
  adicionales: RespuestaStrapi[] = [];

  codigoB64: any;
  imagen: any;
  imgenProductoBase64: any;
  informacion: any;
  pdfObj = null;


  isLoggedIn = false;


  img3d = false;

  clase: any;

  coords: string;

  slideOpts = {
    slidesPerView: 1,
    slidesPerGroup: 1,
    slidesPerColumn: 1,
    centeredSlides: true,


    pager: true,
    coverflowEffect: {
      rotate: 50,
      stretch: 0,
      depth: 100,
      modifier: 1,
      slideShadows: true,
    },

    autoplay: true
  };

  constructor(
    private strapi: StrapiService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalCtrl: ModalController,
    private firebaseAuthService: FirebaseAuthService,
    private plt: Platform,
    // private file: File,
    private widgetUtilService: WidgetUtilService,
    private content: ContentService,
    private dataService: DataService,
    private browserService: BrowserService,
    private pdfGenerator: PdfgeneratorService,
    private platform: Platform,
    private interaction: InteractionService,
    private analyticsService: AnalyticsService,


    // private fbas: FirebaseAnalyticsService

  ) {
    // configurePdfMake();


    this.getAuthState();

    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.id = '2';
      this.slug = result.slug
    });
  }





  async ngOnInit() {
    await this.getUserInStorage();
    await this.getMedicamento();
    await this.analyticsService.setCurrentScreen(`productos/${this.slug}`);
  }



  async getMedicamento(){
    const productResponse = await this.content.strapiGet(`vademecums?slug=${this.slug}`)

    // console.log('Respuesta de vademecums', productResponse)
    const producto = productResponse.data[0];


    this.medicamentos = producto.medicamentos
    // console.log('Producto Raw: ', producto);


    if(this.medicamentos.length > 1) {
      // console.log('Arreglo de mas de un medicamento', this.medicamentos)
      this.multiProduct = true
    } else {
      // console.log('Arreglo de solo un medicamento', this.medicamentos)
      this.multiProduct = false


    }
    // console.log(vademecum);
    console.log(this.id);
    this.producto = producto.medicamentos[0];
    this.imagen = this.producto.Imagen_de_producto[0]
    console.log('Producto: ', this.producto);
    console.log('Producto: ', producto)
    this.modeloSwitch = this.producto .modeloSwitch
    this.modeloURL = this.producto .modeloURL
    console.log('Modelo: ', this.modeloSwitch, this.modeloURL)

    this.productName = this.producto.Nombre

    // this.productoMarked = marked(producto.Descripcion);

    this.presentaciones = producto.Presentacion;
    this.adicionales = producto.Adicional;

    if(this.user.role !== 'noRegistrado') {
      await this.setInteraction(this.producto )
    }


  }

  async setInteraction(producto) {


    const data= {
      titulo: producto.Nombre,
      aditionalData:  producto.PrincipioActivo,
      imagen: this.imagen.url,
      // categoria: this.category,
      slug: this.slug,
      type: 'medicamento'
    }

    console.log()
    await this.interaction.registrarInteraccion(this.uid, `/productos/${this.slug}`, data);
  }




  openPrecio(id, contenido) {

    // this.strapi.setData(id, contenido);
    this.router.navigateByUrl(`/vademecum-producto-precio/${id}`);

    // this.router.navigate(['/vademecum-producto-precio', ProductoID]);
  }


  async compartir() {
    const modal  = await this.modalCtrl.create({
      component: VademecumCompartirPage,
      cssClass: 'bannerVertical',

      componentProps: {
        Info: this.producto
      }
    });

    await modal.present();

  }
  getAuthState() {

    this.firebaseAuthService.getAuthState().subscribe(user => {
      // console.log('User auth state', user ? user.toJSON(): null);
      if (user) {
        this.isLoggedIn = true;
      } else {
        this.isLoggedIn = false;

      }


    });

  }


  async abrirSitiodeCompra(url: string) {
    await Browser.open({ url: `${url}` });

  }



  convertToDataURLviaCanvas(url, outputFormat) {
    return new Promise( (resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = function() {
        let canvas = <HTMLCanvasElement> document.createElement('CANVAS'),
          ctx = canvas.getContext('2d'),
          dataURL;
        canvas.height = img.height;
        canvas.width = img.width;
        ctx.drawImage(img, 0, 0);
        dataURL = canvas.toDataURL(outputFormat);
        canvas = null;
        resolve(dataURL);
      };
      img.src = url;
    });
  }


  async descargarPDF() {

    const ids = []
    for(let medicamento of this.medicamentos) {
      console.log(medicamento)
      ids.push({id: medicamento.id})


    }
    console.log('Arreglo de ids', ids)
    // const data = this.medicamentos
    // const pdf = this.pdfGenerator.generateIpp([{'id': 123}])


    const data: any = await this.pdfGenerator.generateIpp(ids);

    console.log('Data de respuesta', data);
    const blob = data.blob
    const base64OnlyData = data.base64
    const fileName = this.interaction.slugify(this.productName)
    await this.analyticsService.logEvent('ipp_descargada', {producto: this.productName, file_name: `${fileName}.pdf` })

    if (this.platform.is('ios') || this.platform.is('android')) {
      // Si es iOS, usa la función para guardar y abrir el archivo en iOS
      await this.descargarPdfIOS(base64OnlyData, `${fileName}.pdf`);
    } else {
      // Para otras plataformas (web, Android), usa el método de descarga estándar
      await this.descargarPdfStandard(blob, `${fileName}.pdf`);
    }





  }

  private async descargarPdfStandard(blob: Blob, fileName: string) {
    // ... código de la función para descargar en otras plataformas ...

    console.log('Blob: ', blob)
    try {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.productName}.pdf`; // Nombre del archivo
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
    } catch (error) {
      console.error('Error al descargar el PDF:', error);
    }
  }

  async descargarPdfIOS(base64OnlyData: any, fileName: string) {
    console.log('Base 64: ', base64OnlyData)

    try {
      // Escribir el Blob en un archivo en el sistema de archivos del

      // Quitar el prefijo de la URL de datos (data URL prefix) para obtener solo los datos base64
      // const base64OnlyData = base64Data.split(',')[1];

      // Escribir los datos en base64 en el sistema de archivos del dispositivo
      const writeFileResponse = await Filesystem.writeFile({
        path: fileName,
        data: base64OnlyData,
        directory: Directory.Documents,
        recursive: true
      });

      // Obtener la URI del archivo guardado
      const uri = writeFileResponse.uri;


      console.log('Archivo PDF guardado: ', uri);

      // Utilizar el plugin Share para abrir la ventana de compartir
      await Share.share({
        title: 'Compartir PDF',
        url: uri,
        dialogTitle: 'Compartir PDF con',
      });

      // Abrir el archivo con una herramienta nativa
      // Aquí puedes usar un plugin como Capacitor Browser o algo similar
      // para abrir el archivo, o compartirlo con otras aplicaciones
      // Ejemplo: await Browser.open({ url: uri });

      // await this.browserService.openBrowser(uri)

      console.log('Archivo PDF guardado y abierto con éxito: ', uri);
    } catch (error) {
      console.error('Error al guardar y abrir el PDF en iOS: ', error);
    }
  }
  cancel() {
    this.modal.dismiss(null, 'cancel');
  }

  confirm() {
    this.modal.dismiss(this.name, 'confirm');
  }



  ngOnDestroy() {
    console.log('datos del servicio  borrados')
    this.dataService.clearData(); // Limpia los datos cuando el componente se destruye
  }

  async openInfoModal(info: any) {
    console.log('Informacion del modal', info)
    this.ippContent = info


    this.textContent = undefined
    if (this.ippContent.description !== null && this.ippContent.description !== undefined) {
      this.textContent = marked(this.ippContent.description)
    }
    if (info.Imagen_de_producto) {
      this.ippImage = this.strapiUrl + this.ippContent.Imagen_de_producto[0].url
      console.log('Se agrego ippImage', this.ippImage)
    } else {
      this.ippImage = null
      console.log('ippImage Null', this.ippImage)
    }

    await this.infoModal.present();



  }

  async openBrowser(url: string) {
    await this.browserService.openBrowser(url);
  }

  toggleSection(id: number) {
    if (this.expanded === id) {
      this.expanded = null;
    } else {
      this.expanded = id;

    }
  }

  async closeModal(){
    await this.infoModal.dismiss();

  }

  async getUserInStorage(){
    const ret = await Preferences.get({ key: 'user' });
    // this.user = JSON.parse(ret.value);
    // this.uid = this.user.uid;
    console.log(ret)
    if (ret.value !== null) {
      this.user = JSON.parse(ret.value);
      this.uid = this.user.uid;
      console.log('Usuario: ', this.user)
    } else {
      console.log('No hay Usuario: ', this.user)
      this.user = {role: 'noRegistrado'}
      console.log('Usuario no registrado:', this.user)
      // this.user = [{role: 'noRegistrado'}]
    }

  }







  protected readonly close = close;
}
