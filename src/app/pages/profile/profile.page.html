
<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/inicio"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="role === 'medico'">Mi cuenta <ion-icon name="person"></ion-icon></ion-title>

    <ion-title *ngIf="role === 'representante'">Mi perfil <ion-icon name="person"></ion-icon></ion-title>

  </ion-toolbar>
</ion-header>

<ion-content *ngIf="doctorAvailable">


  <ion-grid fixed *ngIf="role === 'medico'">
    <ion-row class="ion-justify-content-center" *ngIf="profileAvailable">
      <ion-col size="12" size-xs="12" size-md="8" size-lg="8" class="ion-text-center">
        <ion-img class="centrar_imagen ion-padding-top" style="width: 40%; "  src="/assets/sanfer_conecta_oficial.svg"></ion-img>

        <!--          <img style="height: 100px" [src]="'https://avatars.dicebear.com/api/avataaars/' + nombreCompleto + '.svg?b=%23ff2600&scale=101&style=circle&topChance=100&facialHairColor[]=auburn&clothes[]=blazer&clothesColor[]=white&eyes[]=default&eyebrow[]=default&mouth[]=smile&skin[]=pale'">-->


        <ion-card class="card">


          <ion-card-content>
            <ion-row>

              <ion-col>
                <ion-list>

                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label class="ion-text-wrap">
                      <p>Nombre:</p>
                      <h2>{{doctorDetail.nombre}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person-outline" size="small" class="ion-padding"></ion-icon>
                    <ion-label class="ion-text-wrap">
                      <p>Apellido Paterno:</p>
                      <h2>{{doctorDetail.paterno}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person-outline" size="small" class="ion-padding"></ion-icon>
                    <ion-label class="ion-text-wrap">
                      <p>Apellido Materno:</p>
                      <h2>{{doctorDetail.materno}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon name="gift" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Fecha de nacimiento:</p>
                      {{doctorDetail.fechaNacimiento | date}}

                    </ion-label>
                  </ion-item>
                  <ion-item >
                    <ion-icon name="school" color="primary" size="small"  class="ion-padding"></ion-icon>
                    <ion-label color="primary" class="ion-text-wrap">
                      <p>Titulo Profesional:</p>
                      <p class="small_text">{{doctorDetail.titulo}}</p>
                    </ion-label>
                  </ion-item>

                  <ion-item>
                    <ion-icon name="ribbon" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Cédula profesional</p>
                      {{doctorDetail.cedula}}
                    </ion-label>
                  </ion-item>

                  <ion-item>
                    <ion-icon name="school" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label color="primary" class="ion-text-wrap">
                      <p>Institución:</p>
                      <h2>{{doctorDetail.institucion}}</h2>
                    </ion-label>
                  </ion-item>

                  <ion-item>
                    <ion-icon name="calendar" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Año de titulación</p>
                      {{doctorDetail.anioRegistro}}
                    </ion-label>

                  </ion-item>
                  <ion-item *ngIf="doctorDetail.genero === 1">
                    <ion-icon name="male-female" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Genero:</p>
                      Masculino <ion-icon name="man" color="tertiary"></ion-icon>

                    </ion-label>

                  </ion-item>
                  <ion-item *ngIf="doctorDetail.genero === 2">
                    <ion-icon name="male-female" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Genero:</p>
                      Femenino <ion-icon name="woman" color="tertiary"></ion-icon>

                    </ion-label>
                  </ion-item>

                  <ion-item>
                    <ion-icon name="call" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Telefono:</p>
                      {{doctorDetail.telefono}}

                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-icon name="mail" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Email:</p>
                      {{doctorDetail.email}}
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-button (click)="updateEmail()">Cambiar correo</ion-button>
                  </ion-item>
                </ion-list>
              </ion-col>
              <ion-col size="12">
                <ion-button (click)="signOut()" expand="block">Cerrar sesión</ion-button>
              </ion-col>
            </ion-row>
          </ion-card-content>
        </ion-card>




        <ion-card class="ion-text-center" color="primary">
          <ion-card-content>
            <ion-icon name="briefcase" size="large"></ion-icon>
            <p>Representantes Asignados:</p>
            <h1>{{representantesList.length}}</h1>
            <ion-icon name="caret-down-circle-outline" ></ion-icon>
          </ion-card-content>

        </ion-card>


        <ion-card >
          <ion-card-content>
            <ion-list>
              <ion-item lines="full" button detail="true" (click)="abrirRepresentante(medico.id)"  *ngFor="let medico of representantesList | orderBy: '-createdAt'">
                <ion-img class="centrar_imagen ion-padding" style="width: 30%; "  src="/assets/logosFv/{{medico.equipo}}.svg"></ion-img>

                <ion-label class="ion-text-wrap">
                  <ion-text color="primary">
                    <h2>{{medico.nombre}} {{medico.apellido}}</h2>
                  </ion-text>
                  <ion-text color="secondary">
                    <p>Equipo: {{medico.equipo}}</p>
                  </ion-text>
                  <p>Unidad de Negocio: {{medico.udn}}</p>
                  <p>Numero de Empleado: {{medico.nEmpleado}}</p>
                </ion-label>
              </ion-item>
            </ion-list>
          </ion-card-content>
        </ion-card>

        <ion-card>
          <ion-card-header>
            Eliminar permanentemente mi cuenta de Sanfer Conecta
          </ion-card-header>
          <ion-card-content>
            <ion-button size="block" (click)="eliminarMiCuenta()">Eliminar mi cuenta</ion-button>

          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-spinner name="lines" class="pade-loader" *ngIf="!profileAvailable"></ion-spinner>
  </ion-grid>

  <ion-grid fixed *ngIf="role === 'representante'">
    <ion-row class="ion-justify-content-center" *ngIf="profileAvailable">
      <ion-col size="12" size-xs="12" size-md="8" size-lg="8">








        <ion-card class="card">

          <ion-card-header>
            <!--            <ion-img class="centrar_imagen" style="width: 40%; "  src="/assets/sanfer_conecta_oficial.svg"></ion-img>-->

            <!-- <ion-card-title color="primary" class="ion-text-center">{{doctorDetail.nombre}}
             </ion-card-title>-->
            <ion-img *ngIf="equipo === 'Atlantese4=CW4'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/atlantes.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Argonautas2g?B3M'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/argonautas.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Minotaurosj3cW!Q'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/minotauros.svg"></ion-img>
            <ion-img *ngIf="equipo === 'ApolosS}5$aC'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/apolos.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Poseidonesv4R?ys'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/poseidon.svg"></ion-img>
            <ion-img *ngIf="equipo === 'CiclopesbY9/6H'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/ciclopes.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Cronos=5B?bf'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/cronos.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Titanes6cyF{@'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/titanes.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Pegasos38vM<#'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/pegasos.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Perseus=E9n?#'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/perseus.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Midas4@Tjw)'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/midas.svg"></ion-img>
            <ion-img *ngIf="equipo === 'HorusM3tR+B'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/horus.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Ignis5M#Xqm'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/ignis.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Aeris5buS@b'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/aeris.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Terra$4b}Nh'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/terra.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Aqua6*bK%+'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/aqua.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Hades2;{vWj'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/hades.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Hermes3J2#yS'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/hermes.svg"></ion-img>
            <ion-img *ngIf="equipo === 'ComercialKa(^%C*4S8EH'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/capacitacion.svg"></ion-img>
            <ion-img *ngIf="equipo === 'Directores22PSZy2k,X#)'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/capacitacion.svg"></ion-img>
            <ion-img *ngIf="equipo === 'VulcanosWa=8V5'" class="centrar_imagen" style="width: 25%; "  src="/assets/logosFv/vulcanos.svg"></ion-img>


          </ion-card-header>



          <ion-card-content>
            <ion-row>
              <ion-col size="12">

                <!--                <ion-img class="user-avatar centrar_imagen" src="{{profileInfo.photoURL || 'assets/user_avatar_amg.png'}}"></ion-img>-->
              </ion-col>
              <ion-col>
                <p style="text-align: center">Id de representante</p>
                <h2 style="text-align: center; color: red" >{{doctorDetail.uid}}</h2>

                <ion-button expand="block" routerLink="/historial">Mi Historial</ion-button>



                <ion-list>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Unidad de Negocios:</p>
                      <h2>{{doctorDetail.udn}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Equipo:</p>
                      <h2>{{doctorDetail.equipo}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Numero de Empleado:</p>
                      <h2>{{doctorDetail.nEmpleado}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Nombre:</p>
                      <h2>{{doctorDetail.nombre}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person-outline" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Apellido:</p>
                      <h2>{{doctorDetail.apellido}}</h2>
                    </ion-label>

                  </ion-item>



                  <ion-item>
                    <ion-icon name="call" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Telefono:</p>
                      {{doctorDetail.telefono}}

                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-icon name="mail" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Email:</p>
                      {{doctorDetail.email}}
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-button (click)="updateEmail()">Cambiar correo</ion-button>
                  </ion-item>
                </ion-list>

                <ion-button size="block" color="secondary" (click)="changeTeam(doctorDetail.uid)">Cambio de equipo</ion-button>



              </ion-col>
              <ion-col size="12">
                <ion-button (click)="signOut()" expand="block">Cerrar sesión</ion-button>
              </ion-col>
            </ion-row>
          </ion-card-content>
        </ion-card>

        <ion-card class="ion-text-center" color="primary">
          <ion-card-content>
            <ion-icon name="medkit" size="large"></ion-icon>
            <p>Médicos asignados:</p>
            <h1>{{medicosList.length}}</h1>
            <ion-icon name="caret-down-circle-outline" ></ion-icon>
          </ion-card-content>

        </ion-card>
        <ion-card >
          <ion-card-content>
            <ion-list>
              <ion-item lines="full" button detail="true" (click)="abrirMedico(medico.id)"  *ngFor="let medico of medicosList | orderBy: '-createdAt'">
                <ion-label class="ion-text-wrap">
                  <ion-text color="primary">
                    <h2>{{medico.nombre}} {{medico.apellido}}</h2>
                  </ion-text>
                  <ion-text color="secondary">
                    <p>Céddula: {{medico.cedula}}</p>
                  </ion-text>
                  <p>Titulo: {{medico.titulo}}</p>
                  <p>Registrado en: {{medico.createdAt | date: 'medium'}}</p>
                </ion-label>
              </ion-item>
            </ion-list>
          </ion-card-content>
        </ion-card>
        <ion-card>
          <ion-card-header>
            Eliminar permanentemente mi cuenta de Sanfer Conecta
          </ion-card-header>
          <ion-card-content>
            <ion-button size="block" (click)="eliminarMiCuenta()">Eliminar mi cuenta</ion-button>

          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-spinner name="lines" class="pade-loader" *ngIf="!profileAvailable"></ion-spinner>
  </ion-grid>

  <ion-grid fixed *ngIf="role === 'productividad'">
    <ion-row class="ion-justify-content-center" *ngIf="profileAvailable">
      <ion-col size="12" size-xs="12" size-md="8" size-lg="8">

        <ion-card class="card">

          <ion-card-header>

            <ion-img class="centrar_imagen" style="width: 40%; "  src="/assets/logosFv/capacitacion.svg"></ion-img>

          </ion-card-header>
          <ion-card-content>
            <ion-row>
              <ion-col size="12">

              </ion-col>
              <ion-col>
                <p style="text-align: center">Id de representante</p>
                <h2 style="text-align: center; color: red" >{{doctorDetail.uid}}</h2>
                <ion-list>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Unidad de Negocios:</p>
                      <h2>{{doctorDetail.udn}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Equipo:</p>
                      <h2>{{doctorDetail.equipo}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Numero de Empleado:</p>
                      <h2>{{doctorDetail.nEmpleado}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Nombre:</p>
                      <h2>{{doctorDetail.nombre}}</h2>
                    </ion-label>

                  </ion-item>
                  <ion-item>
                    <ion-icon color="primary" name="person-outline" size="small" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Apellido:</p>
                      <h2>{{doctorDetail.apellido}}</h2>
                    </ion-label>

                  </ion-item>



                  <ion-item>
                    <ion-icon name="call" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Telefono:</p>
                      {{doctorDetail.telefono}}

                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-icon name="mail" size="small" color="primary" class="ion-padding"></ion-icon>
                    <ion-label>
                      <p>Email:</p>
                      {{doctorDetail.correo}}
                    </ion-label>
                  </ion-item>
                </ion-list>


              </ion-col>
              <ion-col size="12">
                <ion-button (click)="signOut()" expand="block">Cerrar sesión</ion-button>
              </ion-col>
            </ion-row>
          </ion-card-content>
        </ion-card>
        <ion-card>
          <ion-card-header>
            Eliminar permanentemente mi cuenta de Sanfer Conecta
          </ion-card-header>
          <ion-card-content>

            <ion-button size="block" (click)="eliminarMiCuenta()">Eliminar mi cuenta</ion-button>

          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-spinner name="lines" class="pade-loader" *ngIf="!profileAvailable"></ion-spinner>
  </ion-grid>



</ion-content>
