// profile.page.ts

import { Component, OnInit } from '@angular/core';
import { AlertController, LoadingController } from '@ionic/angular';
import { Router } from '@angular/router';

// Servicios personalizados
import { FirebaseAuthService } from '../../services/firebase-auth.service';
import { FirestoreDbService } from '../../services/firestore-db.service';
import { WidgetUtilService } from '../../services/widget-util.service';
import { AuthService } from '../../services/auth.service';
import { AnalyticsService } from '../../services/analytics.service';

// Firebase y AngularFire
import {
  Firestore,
  doc,
  collection,
  getDoc,
  writeBatch,
  increment,
  serverTimestamp,
  getDocs,
  query,
  orderBy,
  setDoc,
  updateDoc,
} from '@angular/fire/firestore';
import {
  Auth,
  getAuth,
  onAuthStateChanged,
  updateEmail,
  EmailAuthProvider,
  reauthenticateWithCredential,
  sendEmailVerification,
  deleteUser,
  User,
} from '@angular/fire/auth';
import { Functions, httpsCallable } from '@angular/fire/functions';

// Capacitor
import { Preferences } from '@capacitor/preferences';

// Otros imports
import { HttpResponse } from '@capacitor/core';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
})
export class ProfilePage implements OnInit {
  public isAdmin = false;
  public nombre = '';
  cedula = '';
  nombreCompleto: string;

  doctorID = '';
  doctorAvailable = false;
  doctorDetail: any = {};
  doctorDetailList: Array<any> = [];

  medicoUID = '';

  uuid = '';

  profileInfo: any = {};
  profileAvailable = false;
  role = 'medico';
  equipo: any;

  representantesList: Array<any> = [];
  medicosList: Array<any> = [];

  constructor(
    private firebaseAuthService: FirebaseAuthService,
    private firestoreDbService: FirestoreDbService,
    private widgetUtilService: WidgetUtilService,
    private router: Router,
    private authService: AuthService,
    private analyticsService: AnalyticsService,
    private functions: Functions,
    private firestore: Firestore,
    private loadingController: LoadingController,
    public alertController: AlertController
  ) {
    this.getUserProfile();
  }

  async ngOnInit() {
    await this.getMedico();
    await this.checkRole();
    await this.analyticsService.setCurrentScreen('Profile');
  }

  async getMedico() {
    const ret = await Preferences.get({ key: 'user' });
    const user = JSON.parse(ret.value);
    this.uuid = user.uid;
    console.log('User ID', user.uid);

    await this.getRepresentantes(user.uid);
    await this.getMedicos(user.uid);
    console.log('User info', user);

    await this.getDoctorDetail();
  }

  getUserProfile() {
    this.profileAvailable = false;
    this.firebaseAuthService.getAuthState().subscribe(
      (user) => {
        if (user) {
          this.profileInfo = user;
        }
        this.profileAvailable = true;
      },
      (error) => {
        this.profileAvailable = true;
        this.widgetUtilService.presentToastError(error.message);
      }
    );
  }

  async getDoctorDetail() {
    try {
      const ret = await Preferences.get({ key: 'user' });
      const user = JSON.parse(ret.value);

      console.log('Usuario almacenado en el Storage', user);

      this.doctorDetail = user;

      this.doctorAvailable = false;

      this.nombreCompleto = `${this.doctorDetail.nombre} ${this.doctorDetail.apellido}`;

      this.doctorDetailList = [];
      for (const key in this.doctorDetail) {
        if (this.doctorDetail.hasOwnProperty(key)) {
          this.doctorDetailList.push({
            name: key,
            value: this.doctorDetail[key],
          });
        }
      }

      this.doctorAvailable = true;
    } catch (error) {
      console.log(error);
      this.widgetUtilService.presentToastError(error.message);
      this.doctorAvailable = true;
    }
  }

  async getRepresentantes(uid: string) {
    console.log('Consulta de los representantes ejecutada con el ID: ', uid);
    this.firestoreDbService
      .getAllDataInside('interacciones', uid, 'representantes')
      .subscribe(
        (result) => {
          console.log('Resultado de la consulta de los representantes', result);
          this.representantesList = result;
        },
        (error) => {
          console.error('Error al obtener representantes:', error);
        }
      );
  }

  async getMedicos(uid: string) {
    this.firestoreDbService
      .getAllDataInside('interacciones', uid, 'medicos')
      .subscribe(
        (result) => {
          this.medicosList = result;
        },
        (error) => {
          console.error('Error al obtener médicos:', error);
        }
      );
  }

  async checkRole() {
    const ret = await Preferences.get({ key: 'user' });
    const u = JSON.parse(ret.value);

    if (u.role === 'representante') {
      this.role = 'representante';
      this.equipo = u.passcode;
    } else if (u.role === 'productividad') {
      this.role = 'productividad';
    }
  }

  async signOut() {
    await this.authService.signOut();
  }

  async clearStorage() {
    await Preferences.clear();
  }

  abrirRepresentante(id: string) {
    this.router.navigate(['mi-representante', id]);
  }

  abrirMedico(id: string) {
    this.router.navigate(['mi-medico', id]);
  }

  async changeTeam(uid: string) {
    console.log(uid);
    this.alertaCambio(uid);
  }

  async alertaCambio(uid: string) {
    const alert = await this.alertController.create({
      header: 'Cambio de Fuerza de Ventas',
      subHeader: 'Ingresa el passcode de la nueva FV proporcionado por tu gerente',
      inputs: [
        {
          name: 'passcode',
          type: 'text',
          placeholder: 'Código de verificación',
        },
      ],
      buttons: [
        {
          text: 'Cambiarme de FV',
          role: 'cambiar',
          cssClass: 'secondary',
          handler: (data) => {
            console.log('Passcode ingresado:', data.passcode);
            this.confirmacionCambio(data.passcode, uid);
          },
        },
      ],
    });

    await alert.present();
    await alert.onDidDismiss();
  }

  async confirmacionCambio(passcode: string, uid: string) {
    const alert = await this.alertController.create({
      header: '¿Estás seguro?',
      subHeader: 'Estás a punto de cambiar de Fuerza de Ventas',
      buttons: [
        {
          text: 'Confirmar',
          role: 'cambiar',
          cssClass: 'primary',
          handler: () => {
            this.validateChange(passcode, uid);
          },
        },
        {
          text: 'Cancelar',
          role: 'cancelar',
          cssClass: 'secondary',
          handler: () => {},
        },
      ],
    });

    await alert.present();
    await alert.onDidDismiss();
  }

  validateChange(passcode: string, uid: string) {
    switch (passcode) {
      case 'Argonautas2g?B3M':
        this.updateTeam(passcode, 'argonautas', 'dogma', uid);
        break;
      // Agrega los demás casos según tus necesidades
      default:
        console.log('Error: Passcode inválido');
        this.widgetUtilService.presentToastError(`El código que ingresaste es inválido: ${passcode}`);
        break;
    }
  }

  async updateTeam(passcode: string, equipo: string, udn: string, uid: string) {
    const ret = await Preferences.get({ key: 'medico' });
    const user = JSON.parse(ret.value);

    // Referencia a Firestore
    const firestore = this.firestore;

    // Document references
    const representanteRef = doc(firestore, `representantes/${uid}`);

    // Batch write
    const batch = writeBatch(firestore);

    batch.update(representanteRef, {
      udn,
      passcode,
      equipo,
      udnAnterior: user.udn,
      equipoAnterior: user.equipo,
      fechaCambioLinea: new Date().valueOf(),
    });

    await batch.commit();

    await this.updateDataInStorage(uid);
  }

  async updateDataInStorage(uid: string) {
    const representanteRef = doc(this.firestore, `representantes/${uid}`);

    const loading = await this.loadingController.create({
      message: `Estamos actualizando tu información`,
      backdropDismiss: true,
      translucent: true,
    });
    await loading.present();

    try {
      const dataRepresentanteSnapshot = await getDoc(representanteRef);
      if (dataRepresentanteSnapshot.exists()) {
        console.log('Data Usuario ', dataRepresentanteSnapshot.data());
        const userData = dataRepresentanteSnapshot.data();

        await Preferences.set({
          key: 'medico',
          value: JSON.stringify({
            uid,
            ...userData,
          }),
        });

        this.widgetUtilService.presentToast(
          `Tus datos se actualizaron correctamente a la nueva Fuerza de Ventas`
        );
      } else {
        console.log('No se encontraron datos para el usuario con UID:', uid);
      }

      await loading.dismiss();
      await this.getDoctorDetail();
    } catch (error) {
      console.error('Algo salió mal, vuelve a iniciar sesión', error);
      await loading.dismiss();
    }
  }

  async updateEmail() {
    const auth = getAuth();
    const user = auth.currentUser;

    if (user) {
      console.log('Usuario actual: ', user);
      const alert = await this.alertController.create({
        header: 'Cambio de email',
        subHeader: `Ingresa el correo al que quieres cambiar tu cuenta. Tu correo actual es: ${user.email}`,
        inputs: [
          {
            name: 'email',
            type: 'text',
            placeholder: 'Nuevo correo electrónico',
          },
        ],
        buttons: [
          {
            text: 'Cambiar mi correo',
            role: 'cambiar',
            cssClass: 'secondary',
            handler: (data) => {
              console.log('Nuevo correo: ', data.email);
              this.confirmChangeWithPassword(user.email || '', data.email);
            },
          },
        ],
      });
      await alert.present();
      await alert.onDidDismiss();
    } else {
      this.widgetUtilService.presentToastError('No se encontró al usuario autenticado.');
    }
  }

  async confirmChangeWithPassword(oldEmail: string, newEmail: string) {
    const alert = await this.alertController.create({
      header: '¿Estás seguro?',
      subHeader: `Tu correo será cambiado por: ${newEmail}. Por favor ingresa tu contraseña`,
      inputs: [
        {
          name: 'password',
          type: 'password',
          placeholder: 'Ingresa tu contraseña',
        },
      ],
      buttons: [
        {
          text: 'Confirmar',
          role: 'cambiar',
          cssClass: 'primary',
          handler: async (data) => {
            const auth = getAuth();
            const user = auth.currentUser;

            if (user) {
              const credential = EmailAuthProvider.credential(oldEmail, data.password);

              try {
                await reauthenticateWithCredential(user, credential);

                await updateEmail(user, newEmail);
                await sendEmailVerification(user);

                this.widgetUtilService.presentToast(
                  `Tu correo ha sido cambiado satisfactoriamente a ${newEmail}. Hemos enviado un correo de validación, por favor confírmalo para continuar teniendo acceso a la aplicación.`
                );
              } catch (error) {
                this.widgetUtilService.presentToastError(`Ocurrió un error: ${error.message}`);
              }
            } else {
              this.widgetUtilService.presentToastError('No se encontró al usuario autenticado.');
            }
          },
        },
        {
          text: 'Cancelar',
          role: 'cancelar',
          cssClass: 'secondary',
          handler: () => {},
        },
      ],
    });
    await alert.present();
    await alert.onDidDismiss();
  }

  async eliminarMiCuenta() {
    const alert = await this.alertController.create({
      header: '¿Estás seguro?',
      subHeader: `Tu cuenta será eliminada permanentemente, por favor ingresa tu contraseña`,
      inputs: [
        {
          name: 'password',
          type: 'password',
          placeholder: 'Ingresa tu contraseña',
        },
      ],
      buttons: [
        {
          text: 'Confirmar',
          role: 'eliminar',
          cssClass: 'primary',
          handler: async (data) => {
            await this.confirmarEliminacion(this.role, data.password);
          },
        },
        {
          text: 'Cancelar',
          role: 'cancelar',
          cssClass: 'secondary',
          handler: () => {
            console.log('No se eliminó la cuenta');
          },
        },
      ],
    });

    await alert.present();
    await alert.onDidDismiss();
  }

  async confirmarEliminacion(userRole: string, password: string) {
    const alert = await this.alertController.create({
      header: 'Acción Definitiva',
      subHeader: `Tu cuenta será eliminada permanentemente de Sanfer Conecta.`,
      buttons: [
        {
          text: 'Confirmar',
          role: 'eliminacion',
          cssClass: 'primary',
          handler: async () => {
            await this.eliminarUsuario(this.uuid, userRole, password);
          },
        },
        {
          text: 'Cancelar',
          role: 'cancelar',
          cssClass: 'secondary',
          handler: () => {
            console.log('No se eliminó la cuenta');
          },
        },
      ],
    });

    await alert.present();
    await alert.onDidDismiss();
  }

  async eliminarUsuario(uid: string, role: string, password: string) {
    await this.widgetUtilService.presentLoading();
    const auth = getAuth();
    const user = auth.currentUser;

    if (user && user.email) {
      const credential = EmailAuthProvider.credential(user.email, password);

      try {
        // Reautenticar al usuario
        await reauthenticateWithCredential(user, credential);

        // Llamar a la Cloud Function para eliminar datos del backend
        const eliminarUsuarioCompleto = httpsCallable(this.functions, 'eliminarUsuarioCompleto');
        await eliminarUsuarioCompleto({ uid, role });

        // Eliminar la cuenta del usuario
        await deleteUser(user);

        await this.widgetUtilService.dismissLoader();
        await this.authService.signOut();
      } catch (error) {
        console.error('Error al eliminar el usuario:', error);
        await this.widgetUtilService.dismissLoader();
        this.widgetUtilService.presentToastError(`Ocurrió un error: ${error.message}`);
      }
    } else {
      await this.widgetUtilService.dismissLoader();
      this.widgetUtilService.presentToastError('No se encontró al usuario autenticado.');
    }
  }
}
