<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Recomendado</ion-title>
<!--    <ion-buttons slot="start">-->
<!--      <ion-back-button text="Regresar" defaultHref="/profile"></ion-back-button>-->
<!--    </ion-buttons>-->
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">

  <ion-grid *ngIf="videoPublications.length > 0" [fixed]="true">
    <ion-row>
      Videos:
    </ion-row>
    <ion-row>
      <ion-col
          size="12"
          size-xs="4"
          size-sm="4"
          size-md="4"
          class=""
          size-lg="2"
          size-xl="2"
          *ngFor="let video of videoPublications"
        >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(video)">
          <ion-img class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="video.url" src="{{strapiUrl}}{{video.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{video.title}}</p>
          <p class="ion-text-center text-overlay">{{video.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="galleryPublications.length > 0">
      Galerias:
    </ion-row>
    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class=""
        size-lg="2"
        size-xl="2"
        *ngFor="let post of galleryPublications"
      >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(post)">
          <ion-img *ngIf="post.image" class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{post.title}}</p>
          <p class="ion-text-center text-overlay">{{post.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="textPublications.length > 0">
      Textos:
    </ion-row>
    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class=""
        size-lg="2"
        size-xl="2"
        *ngFor="let post of textPublications"
      >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(post)">
          <ion-img *ngIf="post.image" class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{post.title}}</p>
          <p class="ion-text-center text-overlay">{{post.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="pdfPublications.length > 0">
      PDF's:
    </ion-row>
    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class=""
        size-lg="2"
        size-xl="2"
        *ngFor="let post of pdfPublications"
      >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(post)">
          <ion-img *ngIf="post.image" class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{post.title}}</p>
          <p class="ion-text-center text-overlay">{{post.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row  *ngIf="promoPublications.length > 0">
      Promocionales:
    </ion-row>
    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class=""
        size-lg="2"
        size-xl="2"
        *ngFor="let post of promoPublications"
      >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(post)">
          <ion-img *ngIf="post.image" class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{post.title}}</p>
          <p class="ion-text-center text-overlay">{{post.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="linkPublications.length > 0">
      Links:
    </ion-row>
    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class=""
        size-lg="2"
        size-xl="2"
        *ngFor="let post of linkPublications"
      >
        <ion-card  button="true"  class="post-card image-container" (click)="openContent(post)">
          <ion-img *ngIf="post.image" class="post-thumbnail" style="margin-bottom: 0px" [routerLink]="post.url" src="{{strapiUrl}}{{post.image.url}}"></ion-img>
          <p class="ion-no-margin icon-overlay">{{post.title}}</p>
          <p class="ion-text-center text-overlay">{{post.type}} <ion-icon name="arrow-forward"></ion-icon>
          </p>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-modal
    #publicationModal
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="1"
    [breakpoints]="[0,  1]"

    [backdropDismiss]="true"
  >
    <ng-template>

      <ion-fab class="ion-padding" vertical="bottom" horizontal="center" *ngIf="closeBottomBtn"  @fadeAnimation>
        <ion-fab-button [color]="buttonColor"  (click)="closePublicationModal(postContent)">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <ion-fab vertical="top" horizontal="center" *ngIf="closeTopBtn" @fadeAnimation>
        <ion-fab-button [color]="buttonColor"  (click)="closePublicationModal(postContent)">
          <ion-icon name="close"></ion-icon>
        </ion-fab-button>
      </ion-fab>

      <!-------------Video--------------------->
      <ion-content color="dark" *ngIf="postContent.type === 'video'">
        <div class="ion-text-center vimeo-modal-style" >
          <div id="reproductorModal"></div>
        </div>
      </ion-content>

      <!-------------Galería--------------------->
      <ion-content *ngIf="postContent.type === 'gallery'">

        <div class="ion-text-center banner-modal-style" >
          <swiper-container pagination="true" *ngIf="postContent.gallery"   slides-per-view="1" class="ion-no-padding ion-no-margin swiperStyleSanfer" >
            <swiper-slide *ngFor="let img of postContent.gallery">
              <ion-img style="padding-bottom: 30px" class="centrar_imagen" src="{{strapiUrl}}{{img.url}}"></ion-img>
            </swiper-slide>
          </swiper-container>
          <h1>{{postContent.title}}</h1>
          <p  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></p>
          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)"> {{postContent.textButton}}
            </ion-button>
          </div>
          <h3 *ngIf="postContent.sourceInfo" (click)="openBrowserModalPublication(postContent.sourceLink)" class="small_text"><ion-icon name="link" color="primary"></ion-icon> Fuente: {{postContent.sourceInfo}}</h3>
        </div>
        <div style="height: 300px"></div>



      </ion-content>
      <!-------------Text--------------------->

      <ion-content [color]="modalcolor" *ngIf="postContent.type === 'text'">
        <div class="ion-text-center banner-modal-style" >
          <ion-img *ngIf="postContent.imageSwitch" class="centrar_imagen" src="{{strapiUrl}}{{postContent.image.url}}"></ion-img>
          <h1>{{postContent.title}}</h1>
          <div *ngIf="textContent" class="ion-padding no_padding_top"  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></div>
          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)"> {{postContent.textButton}}
            </ion-button>
          </div>
          <h3 *ngIf="postContent.sourceInfo" (click)="openBrowserModalPublication(postContent.sourceLink)" class="small_text"><ion-icon name="link" color="primary"></ion-icon> Fuente: {{postContent.sourceInfo}}</h3>
        </div>
        <div style="height: 300px"></div>


      </ion-content>

      <!-------------Promo--------------------->
      <ion-content [color]="modalcolor" *ngIf="postContent.type === 'promo'">

        <div class="ion-text-center banner-modal-style" >
          <ion-img *ngIf="postContent.image"  src="{{strapiUrl}}{{postContent.image.url}}"></ion-img>

          <h1>{{postContent.promocion}}</h1>
          <h3 class="small_text"><ion-icon name="calendar" color="primary"></ion-icon> Vigencia: {{postContent.validity}}</h3>
          <p  style=" font-size: 14px; line-height: 22px;" [innerHTML]="textContent"></p>

          <div class="ion-padding" *ngIf="postContent.switchButton">
            <ion-button [color]="buttonColor" class="ion-text-wrap" (click)="openBrowserModalPublication(postContent.link)">{{postContent.textButton}}</ion-button>
          </div>

        </div>
        <div style="height: 300px"></div>


      </ion-content>





    </ng-template>
  </ion-modal>
</ion-content>
