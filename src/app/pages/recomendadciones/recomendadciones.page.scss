.post-card {
  position: relative;
  width: 100%;
  margin-bottom: 0px;
  margin-top: 0px;
  margin-left: 1px;
  padding-bottom: 127.78%; /* Esto mantiene la relación de aspecto de 9x16 */
  overflow: hidden;
  border-radius: 16px;
}
ion-card.promo-cards{
  //margin: 0 0 0 20px;
  margin: 0 1px 20px 1px;
  border-radius: 16px;
}
.image-container {
  border-radius: 20px;
  position: relative;
  display: inline-block;
}

.image-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 50%;
  background: linear-gradient( var(--ion-color-dark), transparent);
  opacity: 1;
  z-index: 1; /* z-index más bajo para el degradado */
}
.text-overlay {
  position: absolute;
  bottom: -10px;
  left: 15px;
  color: var(--ion-color-primary);
  font-size: 12px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 2; /* z-index más alto para el texto */
}
.icon-overlay{
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;

  font-size: 15px;
  color: white;
  //shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  z-index: 2; /* z-index más alto para el texto */
}
.post-thumbnail {
  position: absolute;
  object-fit: cover; /* Se encarga de escalar y recortar la imagen para llenar el contenendor mientras se mantiene su aspecto */
  object-position: center; /* Centra la imagen dentro del contenedor */
  width: 100%;
  height: 100%;

  //width: 100%;
  //height: 150px;
  //object-fit: cover; /* Se encarga de escalar y recortar la imagen para llenar el contenendor mientras se mantiene su aspecto */
  //object-position: center; /* Centra la imagen dentro del contenedor */
}

.vimeo-modal-style {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
