import {Component, OnInit, ViewChild} from '@angular/core';
import {Preferences} from "@capacitor/preferences";
import Swiper from "swiper";
import {marked} from "marked";
import {BrowserService} from "../../services/browser.service";
import {IonModal} from "@ionic/angular";
import Player from '@vimeo/player';
import {Browser} from "@capacitor/browser";
import {environment} from "../../../environments/environment";
import {animate, style, transition, trigger} from "@angular/animations";

@Component({
  selector: 'app-recomendadciones',
  templateUrl: './recomendadciones.page.html',
  styleUrls: ['./recomendadciones.page.scss'],
  animations: [
    trigger('fadeAnimation', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('0.3s 0.3s ease-in', style({ opacity: 1 })) // Agregar un retraso de 0.3s
      ]),
      transition(':leave', [
        animate('0.3s ease-out', style({ opacity: 0 }))
      ])
    ])
  ]
})
export class RecomendadcionesPage implements OnInit {

  @ViewChild('publicationModal') publicationModal: IonModal;

  strapiUrl: string = environment.strapiURL;


  publications:any = [];
  videoPublications:any = [];
  galleryPublications:any = [];
  textPublications:any = [];
  pdfPublications:any = [];
  promoPublications:any = [];
  linkPublications:any = [];


  userContent: any;

  closeBottomBtn: boolean = false;
  closeTopBtn: boolean = false;

  postContent: any = {}
  textContent: any
  modalcolor: string = 'white'
  buttonColor: string = 'primary'

  videoId: any
  vimeoPlayer: Player;


  constructor(
    private browser: BrowserService,

  ) {

  }

  async ngOnInit() {
    await this.getUserContent()
  }

  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    this.userContent = await JSON.parse(userContent.value);
    console.log('Home user content: ', this.userContent)

    const banners = this.userContent.banners


    this.publications = banners.publications

    for(let video of this.publications) {
      if (video.type === 'video'){
        this.videoPublications.push(video)
      }
      if (video.type === 'gallery'){
        this.galleryPublications.push(video)
      }
      if (video.type === 'text'){
        this.textPublications.push(video)
      }
      if (video.type === 'pdf'){
        this.pdfPublications.push(video)
      }
      if (video.type === 'promo'){
        this.promoPublications.push(video)
      }
      if (video.type === 'link'){
        this.linkPublications.push(video)
      }
    }

    console.log(this.videoPublications)
    console.log(this.galleryPublications)
    console.log(this.textPublications)



  }

  async openContent(post: any) {
    this.postContent = post;

    this.modalcolor = post.modalColor;
    this.buttonColor = post.buttonColor;

    this.textContent = undefined
    if (this.postContent.textContent !== null && this.postContent.textContent !== undefined) {
      this.textContent = marked(this.postContent.textContent)
    }

    switch (post.type) {
      case 'link':
        console.log('Abrir vinculo')
        await this.openBrowser(this.postContent.link)
        break;
      case 'video':
        console.log('Abrir video')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        await this.handleVideo(post.videoURL, 'reproductorModal');
        this.cerrarModalHandle();
        break;
      case 'gallery':
        console.log('Abrir galeria Nueva')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        this.cerrarModalHandle();
        break;
      case 'pdf':
        console.log('Abrir pdf')
        await this.openBrowser(this.postContent.pdfURL)
        break;
      case 'text':
        console.log('Abrir text')
        this.closeBottomBtn = true;
        await this.publicationModal.present();

        this.cerrarModalHandle();
        break;
      case 'model':
        console.log('Abrir model', this.postContent.modelURL)
        await this.open3DModel(this.postContent.modelURL)
        break;
      case 'promo':
        console.log('Abrir promo')
        this.closeBottomBtn = true;
        await this.publicationModal.present();
        this.cerrarModalHandle();
        break;
    }

  }

  cerrarModalHandle() {
    this.publicationModal.onDidDismiss().then(() => {
      this.closeBottomBtn = false;
      this.closeTopBtn = false;
    });
  }

  // Cierra el modal del contenido
  async closePublicationModal(postContent: any) {
    this.closeBottomBtn = false
    this.closeTopBtn = false

    if (postContent.type === 'vimeo') {
      if (this.vimeoPlayer) {
        try {
          await this.vimeoPlayer.pause();
        } catch (error) {
          console.error("Error al pausar el video", error);
        }
      }
    }

    await this.publicationModal.dismiss();
  }

  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }


  async handleVideo(videoURL: string, playerId: string) {
    console.log(videoURL, playerId);

    // Extrae el ID del video de Vimeo
    const vimeo = videoURL;
    if (vimeo.includes('https://')) {
      const idVimeo = vimeo.split('/');
      this.videoId = idVimeo[3];
    } else {
      this.videoId = vimeo;
    }

    console.log(this.videoId);

    // Elimina el reproductor existente
    if (this.vimeoPlayer !== undefined) {
      this.vimeoPlayer.destroy();
    }

    // Crea un nuevo reproductor
    this.vimeoPlayer = new Player(playerId, {
      id: this.videoId,
      responsive: true,
      color: '#f32d36'
    });

    // Eventos del reproductor
    this.vimeoPlayer.on('play', async (play: any) => {
      console.log('El usuario dio play en el video', play);
    });

    this.vimeoPlayer.on('pause', async (pause: any) => {
      console.log('El usuario dio pausa al video', pause);
    });

    this.vimeoPlayer.on('ended', async (end: any) => {
      console.log('El video ha terminado', end);
    });

    // Manejo de errores
    this.vimeoPlayer.on('error', (error: any) => {
      console.error('Error en el reproductor de Vimeo', error);
    });
  }

  async open3DModel(modelUrl: any) {
    if (!modelUrl.includes('http')) {

      const url =  `https://sketchfab.com/models/${modelUrl}/embed?autostart=1&amp;preload=1&amp;ui_controls=1&amp;ui_infos=1&amp;ui_inspector=1&amp;ui_stop=1&amp;ui_watermark=1&amp;ui_watermark_link=1`
      console.log('URL DEL MODELO', url)
      await Browser.open({ url , toolbarColor: '#ff2836' });
    } else {
      await Browser.open({ url: `${modelUrl}` });

    }

  }

  async openBrowserModalPublication(url:string) {
    await this.publicationModal.dismiss();
    await this.browser.openBrowser(url)

  }

}
