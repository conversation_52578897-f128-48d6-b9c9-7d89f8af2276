<ion-header>



  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button color="primary" text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>

    <ion-title slot="" color="primary">¿Olvidaste tu contraseña?</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">



  <ion-grid fixed style="height: 100%">
    <ion-row class="ion-justify-content-center ion-align-items-center" style="height: 100%">
      <ion-col size="12"  size-md="6" size-lg="6">
        <ion-card class="card" color="primary">

          <ion-card-header class="ion-text-center" >
            <ion-icon name="key" size="large"></ion-icon>
          </ion-card-header>
          <ion-card-content >


<!--            <form [formGroup]="loginForm" autocomplete="off" class="">-->

<!--              <ion-item >-->

<!--                <ion-label position="floating"><ion-icon name="mail" color="primary"></ion-icon> Email</ion-label>-->
<!--                <ion-input type="email" formControlName="email" placeholder="Ingresa aquí tu correo" [ngModel]="correoDelMedico"></ion-input>-->

<!--              </ion-item><div class="error-message" >{{formError.email}}</div>-->
<!--              <h3 class="ion-text-center ion-padding-top">-->
<!--                Si olvidaste tu contraseña solo ingresa tu correo o valida que este correcto y se te enviará un link a tu cuenta de correo para que la restablezcas-->
<!--              </h3>-->

<!--              <ion-button expand="block" size="" class="ion-margin-top" color="secondary" [disabled]="loginForm.invalid" (click)="recover()">-->
<!--                <ion-spinner name="dots" slot="start" *ngIf="showLoginSpinner" ></ion-spinner>-->
<!--                Recuperar contraseña-->
<!--              </ion-button>-->
<!--              &lt;!&ndash;<ion-button color="danger" expand="block" size="" class="ion-margin-top" (click)="googleLogin()">-->
<!--                  <ion-icon name="logo-google" slot="end" ></ion-icon>-->
<!--                  Google-->
<!--              </ion-button>&ndash;&gt;-->
<!--            </form>-->

            <form [formGroup]="recoverForm" autocomplete="off" class="">

<!--              <ion-item>-->
<!--                <ion-label position="floating"><ion-icon name="mail" color="primary"></ion-icon> Email</ion-label>-->
<!--                <ion-input type="email" formControlName="email" placeholder="Ingresa aquí tu correo"></ion-input>-->
<!--              </ion-item>-->
<!--              <div class="error-message">{{ recoverForm.get('email').errors }}</div>-->


              <!--// Email===============================-->
              <ion-item lines="none">
                <ion-icon
                  slot="start"
                  name="mail-outline"
                  color="primary">

                </ion-icon>
                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Ingresa tu correo electronico"
                  [errorText]=" formError.email"
                  label-placement="floating"
                  type="email"
                  formControlName="email"
                  label="Email">
                </ion-input>
              </ion-item>
              <h3 class="ion-text-center ion-padding-top">
                Si olvidaste tu contraseña solo ingresa tu correo o valida que este correcto y se te enviará un link a tu cuenta de correo para que la restablezcas
              </h3>

              <ion-button expand="block" size="" class="ion-margin-top" color="secondary" [disabled]="recoverForm.invalid" (click)="recover()">
                <ion-spinner name="dots" slot="start" *ngIf="showLoginSpinner"></ion-spinner>
                Recuperar contraseña
              </ion-button>
              <!-- El botón de Google está comentado, pero sigue estando disponible para uso futuro -->
              <!--
              <ion-button color="danger" expand="block" size="" class="ion-margin-top" (click)="googleLogin()">
                <ion-icon name="logo-google" slot="end"></ion-icon>
                Google
              </ion-button>
              -->
            </form>

            <!--<ion-button expand="block" size="small" class="ion-margin-top" color="light" routerLink="/signup"  > <ion-icon name="create" slot="end" ></ion-icon> Registrarte </ion-button>-->
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>


</ion-content>
