import { Component, OnInit } from '@angular/core';
import {HelperService} from "../../services/helper.service";
import {ActivatedRoute, Router} from "@angular/router";
import {WidgetUtilService} from "../../services/widget-util.service";
import {AlertController} from "@ionic/angular";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {RECOVER} from "../../interfaces/formValidationMessage";
import {FirebaseAuthentication} from "@capacitor-firebase/authentication";
import {AnalyticsService} from "../../services/analytics.service";

@Component({
  selector: 'app-recover',
  templateUrl: './recover.page.html',
  styleUrls: ['./recover.page.scss'],
})
export class RecoverPage implements OnInit {

  recoverForm: FormGroup;
  email: FormControl;
  formError: any = {
    email: ''
  };

  showLoginSpinner: boolean = false;

  validationMessage: any = RECOVER;

  correoDelMedico: any;
  constructor(
    private helperService: HelperService,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private activatedRoute: ActivatedRoute,
    public alertController: AlertController,
    private analyticsService: AnalyticsService,

  ) {

  }

  async ngOnInit() {
    this.createFormControl();
    this.createForm();
    await this.analyticsService.setCurrentScreen('Recover' );


    this.activatedRoute.params.subscribe((result: any) => {

      this.recoverForm.patchValue({
        email: result.email
      });

    });
  }
  async recover() {
    try {
      this.showLoginSpinner = true;
      console.log('Recover', this.email.value)
      await FirebaseAuthentication.sendPasswordResetEmail({
        email: this.email.value,
      });
      // const result = await FirebaseAuthentication.sendPasswordResetEmail(this.email.value);
      // console.log('Result', result);
      this.showLoginSpinner = false;
      await this.alertaDeSpam(this.email.value);
      await this.widgetUtilService.presentToast
      (`¡Se te ha enviado un correo de restablecimiento!<br>Por favor ingresa a tu correo ${this.email.value} y sigue el link de cambio de contraseña`);
      // this.router.navigate(['/login']);
    } catch (error) {
      console.log('Error', error);
      this.showLoginSpinner = false;
      await this.widgetUtilService.presentToastError(error.message);
    }

  }


  createFormControl() {
    this.email = new FormControl('', [
      Validators.required,
      Validators.email
    ]);
  }

  createForm() {
    this.recoverForm = new FormGroup({
      email: this.email
    });
    this.recoverForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
  }

  onFormValueChanged(data) {

    this.formError = this.helperService.prepareValidationMessage(this.recoverForm, this.validationMessage, this.formError);

  }
  resetForm() {
    this.recoverForm.reset();
    this.formError = {
      email: ''
    };
  }



  async alertaDeSpam(email) {
    const alert = await this.alertController.create({
      // cssClass: 'my-custom-class',
      header: 'Revisa tu correo no deseado',
      subHeader: `${email}`,
      message: `Acabamos de enviar un corrreo de restablecimiento de tu contraseña a tu correo electronico ${email}, ` +
        'usualmente llega inmediatamente pero puede tomar un par de minutos en llegar, ' +
        'asegurate de buscar en tu bandeja de correo no deseado si aun no te llega',
      buttons: [
        {
          text: 'Perfecto',
          handler: async () => {
            console.log('Confirm Okay');
            await this.router.navigate(['/login']);

          }
        }
      ]
    });
    await alert.present();

    const { role } = await alert.onDidDismiss();
  }

}
