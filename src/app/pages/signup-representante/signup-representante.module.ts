import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SignupRepresentantePageRoutingModule } from './signup-representante-routing.module';

import { SignupRepresentantePage } from './signup-representante.page';
import {NgPipesModule} from "ngx-pipes";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    NgPipesModule,
    SignupRepresentantePageRoutingModule
  ],
  declarations: [SignupRepresentantePage]
})
export class SignupRepresentantePageModule {}
