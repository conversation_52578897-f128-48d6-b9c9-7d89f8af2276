<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button color="primary" text="Regresar" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-title slot="" color="primary">Registrate en Sanfer Conecta</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-grid >
    <ion-row class="ion-justify-content-center centrar_imagen">
      <ion-col size="12"  size-md="8" size-lg="8" size-sm="12" size-xs="12">
        <ion-img class="centrar_imagen"  style="width: 30%; max-width: 250px; padding-top: 20px; padding-bottom: 20px;"  src="/assets/sanfer_conecta_oficial.svg"></ion-img>
        <h1>Si eres representante médico registrate aquí</h1>

        <ion-card >

          <ion-card-content>
            <form [formGroup]="signupForm" autocomplete="off">
              <ion-item>
                <ion-label position="floating">Codigo de verificación
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input type="text" id="passcode" formControlName="passcode"></ion-input>
              </ion-item><div class="error-message ion-text-center" >{{formError.passcode}}</div>

              <!--// Nombre===============================-->
              <ion-item lines="none">
                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Ingresa tu nombre"
                  [errorText]=" formError.nombre"
                  id="nombre"
                  label-placement="floating"
                  type="text"
                  formControlName="nombre"
                  label="Nombre">
                </ion-input>
              </ion-item>

              <!--// Apellido  Paterno===============================-->
              <ion-item lines="none">

                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Ingresa tu Apellido"
                  [errorText]=" formError.apellido"
                  id="apellido"
                  label-placement="floating"
                  type="text"
                  formControlName="apellido"
                  label="Apellido Paterno">
                </ion-input>
              </ion-item>

              <!--// Telefono===============================-->
              <ion-item lines="none">
                <ion-icon
                  slot="start"
                  name="call-outline"
                  color="primary">
                </ion-icon>
                <ion-text color="danger">*</ion-text>
                <ion-input
                  [counter]="true"
                  maxlength="10"
                  helperText="Ingresa tu telefono a 10 digitos"
                  [errorText]=" formError.telefono"
                  label-placement="floating"
                  type="tel"
                  id="telefono"
                  formControlName="telefono"
                  label="Telefono">
                </ion-input>

              </ion-item>

              <!--// Email===============================-->
              <ion-item lines="none">
                <ion-icon
                  slot="start"
                  name="mail-outline"
                  color="primary">

                </ion-icon>
                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Ingresa tu correo electronico"
                  [errorText]=" formError.email"
                  label-placement="floating"
                  type="email"
                  id="email"
                  formControlName="email"
                  label="Email">
                </ion-input>
              </ion-item>

              <!--// Password===============================-->
              <ion-item>
                <ion-icon
                  slot="start"
                  name="key-outline"
                  color="primary">

                </ion-icon>
                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Ingresa un password seguro"
                  [errorText]=" formError.password"
                  [counter]="true"
                  maxlength="16"
                  label-placement="floating"
                  type="password"
                  id="password"
                  formControlName="password"
                  label="Password"
                >
                </ion-input>
              </ion-item>
              <!--// Numero de empleado===============================-->
              <ion-item lines="none">
                <ion-text color="danger">*</ion-text>
                <ion-input
                  helperText="Numero de Empleado"
                  [errorText]=" formError.nEmpleado"
                  id="nEmpleado"
                  label-placement="floating"
                  type="text"
                  formControlName="nEmpleado"
                  label="Numero de Empleado">
                </ion-input>
              </ion-item>
              <ion-button expand="block" size="" class="ion-margin-top" [disabled]="signupForm.invalid" (click)="signup()"> <ion-spinner name="dots" slot="start" *ngIf="showSignupSpinner" ></ion-spinner><ion-icon name="create" slot="end" ></ion-icon> Registrarse </ion-button>
            </form>
            <ion-button expand="block" size="small" class="ion-margin-top" color="light" routerLink="/login" > <ion-icon name="log-in" slot="end" ></ion-icon> Iniciar sesión </ion-button>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>

</ion-content>
