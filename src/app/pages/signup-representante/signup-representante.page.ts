import { Component, OnInit } from '@angular/core';
import {AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators} from '@angular/forms';
import { SIGNUPREPRE} from '../../constants/formValidationMessage';
import {HelperService} from '../../services/helper.service';
import {Router} from '@angular/router';
import {WidgetUtilService} from '../../services/widget-util.service';
import { Preferences } from '@capacitor/preferences';
import {FirebaseAuthentication} from "@capacitor-firebase/authentication";
import {EmailAuthProvider, getAuth, signInWithCredential} from "@angular/fire/auth";
import {FirebaseFirestore} from "@capacitor-firebase/firestore";
import {FirestoreService} from "../../services/firestore.service";
import {environment} from "../../../environments/environment";




@Component({
  selector: 'app-signup-representante',
  templateUrl: './signup-representante.page.html',
  styleUrls: ['./signup-representante.page.scss'],
})


export class SignupRepresentantePage implements OnInit {
  usuarioCreado: string = '';

  profileInfo: any = {};
  profileAvailable = false;

  fecha = new Date();
  signupForm: any = FormGroup;
  nombre: any = FormControl;
  apellido: any = FormControl;
  email: any = FormControl;
  telefono: any = FormControl;
  password: any = FormControl;
  passcode: any = FormControl;
  nEmpleado: any = FormControl;




  formError: any = {
    nombre: '',
    apellido: '',
    email: '',
    telefono: '',
    password: '',
    passcode: '',
    nEmpleado: ''
  };


  validationMessage: any = SIGNUPREPRE;
  showSignupSpinner = false;

  passcodeClean: string = ''

  constructor(private helperService: HelperService,
              private router: Router,
              private widgetUtilService: WidgetUtilService,
              private firestoreService: FirestoreService
  ) {
  }

  ngOnInit() {
    this.createFormControl();
    this.createForm();
  }

  resetForm() {
    this.signupForm.reset();
    this.formError = {
      nombre: '',
      apellido: '',
      email: '',
      telefono: '',
      password: '',
      passcode: '',
      nEmpleado: ''
    };
  }




  async signup() {
    try {
      this.showSignupSpinner = true;


      const result = await FirebaseAuthentication.createUserWithEmailAndPassword({email: this.email.value, password: this.password.value
      });
      console.log('Resultado del registro: ', result)

      const credential = EmailAuthProvider.credential(this.email.value, this.password.value);
      console.log('Obtain Credential 1 Firebase ', credential)
      const auth = getAuth();
      console.log('Resultado del Auth despues del registro: ', auth)

      const loggeo = await signInWithCredential(auth, credential);


      console.log('Resultado del Loggeo despues del registro: ', loggeo)

      await FirebaseAuthentication.sendEmailVerification();
      console.log('Se envio el correo de verificacion');
      const uid = result.user.uid;

      this.passcodeClean = this.passcode.value.replace(/\s/g, '');

      const medicosRef = `representantes/${uid}`
      const userData = {
        nombre: this.nombre.value,
        apellido: this.apellido.value,
        email: this.email.value,
        telefono: this.telefono.value,
        isAdmin: false,
        fecha: this.fecha.toDateString(),
        fechaMs: this.fecha.valueOf(),
        hora: this.fecha.toLocaleTimeString(),
        Registro: 'Registro Web Representante Medico',
        passcode: this.passcodeClean,
        role: 'representante',
        nEmpleado: this.nEmpleado.value
      }
      console.log('Data para guardar en firestore: ', userData)
      const firestoreResponse = await FirebaseFirestore.setDocument({reference: medicosRef, data: userData})

      console.log('Data retornada por firestore: ', firestoreResponse)

      const snapshot = await this.firestoreService.getDocument(`representantes/${result.user.uid}`);
      const user = snapshot.data as any;
      console.log('User Snapshot: ', user)


      await Preferences.set({
        key: 'user',
        value: JSON.stringify({

          uid: result.user.uid,
          nombre: user.nombre,
          apellido: user.apellido,
          isAdmin: user.isAdmin,
          correo: user.email,
          telefono: user.telefono,
          passcode: user.passcode,
          nEmpleado: user.nEmpleado,
          role: user.role,
        })
      });



      await this.router.navigate(['/welcome'], { replaceUrl: true });
      this.showSignupSpinner = false;
      this.resetForm();
    } catch (error) {
      console.log('Error', error);
      this.showSignupSpinner = false;
      await this.widgetUtilService.presentToastError(`Se encontro error ${error}`);
    }
  }

  createFormControl() {

    const passcodes = environment.passcodes

    this.nombre = new FormControl('', [
      Validators.required
    ]);
    this.apellido = new FormControl('', [
      Validators.required
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.email
    ]);
    this.telefono = new FormControl('', [
      Validators.required,
      Validators.minLength(10)


    ]);
    this.password = new FormControl('', [
      Validators.required,
      Validators.minLength(8)
    ]);

    this.passcode = new FormControl('', [
      Validators.required,
      this.passcodeValidator(passcodes) // Aquí usamos el validador personalizado
    ]);
    this.nEmpleado = new FormControl('', [
        Validators.required

    ]);

  }

  createForm() {
    this.signupForm = new FormGroup({
      nombre: this.nombre,
      apellido: this.apellido,
      email: this.email,
      telefono: this.telefono,
      password: this.password,
      passcode: this.passcode,
      nEmpleado: this.nEmpleado
    });
    this.signupForm.valueChanges.subscribe(data => this.onFormValueChanged(data));
  }

  onFormValueChanged(data) {
    this.formError = this.helperService.prepareValidationMessage(this.signupForm, this.validationMessage, this.formError);
  }

  passcodeValidator(condiciones: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const passcode = control.value || ''; // Asegura que passcode sea una cadena
      const isValid = condiciones.some(el => passcode.includes(el));
      return isValid ? null : {invalidPasscode: true};
    };
  }
}

