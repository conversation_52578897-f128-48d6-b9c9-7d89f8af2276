import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SignupPageRoutingModule } from './signup-routing.module';

import { SignupPage } from './signup.page';
import {NgPipesModule} from "ngx-pipes";
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    SignupPageRoutingModule,
    NgPipesModule
  ],
  declarations: [SignupPage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]

})
export class SignupPageModule {}
