import {Component, OnInit, ViewChild} from '@angular/core';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {HelperService} from "../../services/helper.service";
import {Router} from "@angular/router";


import { SIGNUP } from '../../interfaces/formValidationMessage';
import {FirebaseAuthentication} from "@capacitor-firebase/authentication";
import {FirebaseFirestore} from "@capacitor-firebase/firestore";
import {WidgetUtilService} from "../../services/widget-util.service";
import {IonModal, LoadingController, ModalController} from "@ionic/angular";
import {StrapiService} from "../../services/strapi.service";
import {Preferences} from "@capacitor/preferences";
import {environment} from "../../../environments/environment";
import { User } from '../../models/user.model';
import {FirestoreService} from "../../services/firestore.service";
import { Medico } from '../../models/medico.model';
// import {CedulaPage} from "../cedula/cedula.page";
import {Capacitor, CapacitorHttp, HttpResponse} from "@capacitor/core";
import {EmailAuthProvider, getAuth, signInWithCredential} from "@angular/fire/auth";
import {AnalyticsService} from "../../services/analytics.service";
import {SwiperContainer} from "swiper/swiper-element";
import {BrowserService} from "../../services/browser.service";

import { SegmentService } from '../../services/segment.service';
import { AnalyticsBrowser } from '@segment/analytics-next';




@Component({
  selector: 'app-signup',
  templateUrl: './signup.page.html',
  styleUrls: ['./signup.page.scss'],
})
export class SignupPage implements OnInit {

  @ViewChild(IonModal) modal: IonModal;

  showSignupSpinner: boolean = false;
  uid: string;
  titulosPermitidos: any = [];
  fecha = new Date();


  // Sep data for Cedulas
  sepData:any = [];
  cedulaData: any = {};


  textoBuscar: string = '';



  version = environment.version;


  signupForm: any = FormGroup;
  nombre: any = FormControl;
  paterno: any = FormControl;
  materno: any = FormControl;
  cedula: any = FormControl;
  titulo: any = FormControl;
  email: any = FormControl;
  telefono: any = FormControl;
  password: any =FormControl;
  formError: any = {
    nombre: '',
    paterno: '',
    materno: '',
    cedula: '',
    titulo: '',
    email: '',
    telefono: '',
    password: '',
  };


  showSpinner = false;

  clicked: boolean = false;

  validationMessage: any = SIGNUP;

  segmentAnalytics: AnalyticsBrowser;


  constructor(
    private helperService: HelperService,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private modalCtrl: ModalController,
    private strapi: StrapiService,
    private loadingCtrl: LoadingController,
    private analyticsService: AnalyticsService,
    private browser: BrowserService,

    private firestoreService: FirestoreService,
    private segmentService: SegmentService
) {
  this.segmentAnalytics = this.segmentService.getAnalytics();

}

  async ngOnInit() {
    this.createFormControl();
    this.createForm();
    this.strapi.getContenido('controles')
      .subscribe(async resp => {
        const arreglo: any = [];
        for ( const titulo of resp.titulos) {
          arreglo.push(titulo.titulo);
        }
        this.titulosPermitidos = arreglo;

      });
  }

  createFormControl() {
    this.nombre = new FormControl('', [
      Validators.required
    ]);
    this.paterno = new FormControl('', [
      Validators.required
    ]);
    this.materno = new FormControl('', [
      Validators.required
    ]);
    this.cedula = new FormControl('', [
      Validators.required
    ]);
    this.email = new FormControl('', [
      Validators.required,
      Validators.email
    ]);
    this.telefono = new FormControl('', [
      Validators.required,
      Validators.minLength(10)

    ]);
    this.password = new FormControl('', [
      Validators.required,
      Validators.minLength(8)
    ]);
    this.titulo = new FormControl('', [
      // this.tituloMedico
    ]);

  }


  createForm() {
    this.signupForm = new FormGroup({
      nombre: this.nombre,
      paterno: this.paterno,
      materno: this.materno,
      cedula: this.cedula,
      titulo: this.titulo,
      email: this.email,
      telefono: this.telefono,
      password: this.password,


    });
    this.signupForm.valueChanges.subscribe((data: string) => this.onFormValueChanged(data));
  }

  onFormValueChanged(data: any) {
    // console.log('Data: ', data);
    // console.log(this.signupForm);
    this.formError = this.helperService.prepareValidationMessage(this.signupForm, this.validationMessage, this.formError);
    // console.log(this.formError);
  }




  async signup() {
    try {
      this.clicked = true;
      this.showSignupSpinner = true;


      const result = await FirebaseAuthentication.createUserWithEmailAndPassword({email: this.email.value, password: this.password.value
      });
      console.log('Resultado del registro: ', result)

      const credential = EmailAuthProvider.credential(this.email.value, this.password.value);
      console.log('Obtain Credential 1 Firebase ', credential)
      const auth = getAuth();
      console.log('Resultado del Auth despues del registro: ', auth)

      const loggeo = await signInWithCredential(auth, credential);


      console.log('Resultado del Loggeo despues del registro: ', loggeo)

      await FirebaseAuthentication.sendEmailVerification();
      console.log('Se envio el correo de verificacion');


      // console.log('Resultado del registro via FirebaseAuthentication', result)
      // const result = await this.firebaseAuthService.registerWithEmailPassword(this.email.value, this.password.value);
      const uid = result.user.uid;

      const medicosRef = `medicos/${uid}`
      const userData = {
        nombre: this.nombre.value,
        paterno: this.paterno.value,
        materno: this.materno.value,

        cedula: this.cedula.value,
        titulo: this.titulo.value,
        institucion: this.cedulaData.Institucion,
        anioRegistro: this.cedulaData.AnioRegistro,
        tipo: this.cedulaData.Tipo,
        genero: this.cedulaData.Genero,
        email: this.email.value,
        telefono: this.telefono.value,
        role: 'medico',
        // password: this.password.value,
        isAdmin: false,
        fecha: this.fecha.valueOf(),
        hora: this.fecha.toLocaleTimeString(),
        Registro: 'Registro SanferConecta'
      }

      await FirebaseFirestore.setDocument({reference: medicosRef, data: userData})

      await this.widgetUtilService.presentToast
      (`¡Registro exitoso!'<br>'Email de verificación enviado satisfactoriamente <br> Bienvenido a Sanfer Conecta`);


      const snapshot = await this.firestoreService.getDocument(`medicos/${result.user.uid}`);
      const user = snapshot.data as Medico;



      await Preferences.set({
        key: 'user',
        value: JSON.stringify({
          uid: result.user.uid,
          cedula: user.cedula,
          nombre: user.nombre,
          titulo: user.titulo,
          paterno: user.paterno,
          materno: user.materno,

          correo: user.email,
          telefono: user.telefono,
          role: user.role,
          institucion: user.institucion,
          tipo: user.tipo,
          anioRegistro: user.anioRegistro,
          genero: user.genero,
          fechaFinal: user.fechaFinal,
        })
      });


      // this.segment.track('Login Successful', {
      //   userId: 'user1234-justTesting',
      //   sanferId: uid,
      //   role: userData.role
      // }).then(() => console.log("Event sended", 'uid: ', uid, 'role: ',  userData.role));

      await this.analyticsService.logEvent('signup', { method: 'email' });

      const platform = Capacitor.getPlatform();
      console.log('Platform', platform);
      await this.segmentAnalytics.identify(uid, {
        name: userData.nombre + userData.paterno + userData.paterno,
        email: userData.email,
        channel: platform,
        traits: {
          titulo: userData.titulo,
          role: userData.role,
          cedula: userData.cedula
        }
        }
      )
      // Tracking de Segment
      await this.segmentAnalytics.track('Sign Up', {
        channel: platform,
      });




      await this.router.navigate(['/gracias'], { replaceUrl: true });

      this.showSignupSpinner = false;
      this.resetForm();
      this.clicked = false;

    } catch (error: any) {
      console.log('Error', error);
      this.showSignupSpinner = false;
      this.clicked = false;
      this.widgetUtilService.presentToastError(error);
    }

  }



  // async openCedula() {
  //
  //   console.log('verificacion de cedula')
  //
  //   const modal = await this.modalCtrl.create({
  //     component: CedulaPage,
  //     cssClass: 'cart-modal',
  //     componentProps: {
  //       doctorName: this.nombre.value.normalize('NFD').replace(/[\u0300-\u036f]/g, ''),
  //       doctorLastName: this.apellido.value.normalize('NFD').replace(/[\u0300-\u036f]/g, ''),
  //       doctorCedula: this.cedula.value.toString()
  //     }
  //   });
  //   modal.onWillDismiss().then(() => {
  //     // this.fab.nativeElement.classList.remove('animated', 'bounceOutLeft');
  //   });
  //
  //   await modal.present();
  //   const {data} = await modal.onDidDismiss();
  //   console.log(this.carrito);
  //   this.carrito = data;
  //
  //
  //   console.log('Titulos permitidos llamados 2', this.titulosPermitidos);
  //
  //   const condiciones = this.titulosPermitidos;
  //
  //   const verificacion = condiciones.some((el: string) => this.carrito.Titulo.includes(el));
  //
  //   if (verificacion  === true) {
  //     const nombreRecabado = (<HTMLInputElement> document.getElementById('nombre'))
  //       .value = `${this.carrito.Nombre}`;
  //     const apellidoRecabado = (<HTMLInputElement> document.getElementById('apellido'))
  //       .value = `${this.carrito.Paterno} ${this.carrito.Materno}`;
  //     const cedulaRecabado = (<HTMLInputElement> document.getElementById('cedula'))
  //       .value = `${this.carrito.Cedula}`;
  //     const tituloRecabado = (<HTMLInputElement> document.getElementById('titulo'))
  //       .value = `${this.carrito.Titulo}`;
  //
  //     this.widgetUtilService.presentToast('¡Cedula verificada!<br> Puedes registrarte');
  //
  //
  //   } else {
  //     const nombreRecabado = (<HTMLInputElement> document.getElementById('nombre'))
  //       .value = `${this.carrito.Nombre}`;
  //     const apellidoRecabado = (<HTMLInputElement> document.getElementById('apellido'))
  //       .value = `${this.carrito.Paterno} ${this.carrito.Materno}`;
  //     const tituloRecabado = (<HTMLInputElement> document.getElementById('titulo'))
  //       .value = `${this.carrito.Titulo}`;
  //
  //
  //     this.widgetUtilService.presentToastError('Disculpe esta aplicación es solo para los profesionales de la Salud, si se equivoco de cedula profesional intente nuevamente');
  //
  //   }
  //
  //
  //
  //
  // }





  async lookForCedula(nombre: string, paterno: string, materno: string){
    console.log('Nueva Ruta');
    this.modal.present
    const loading = await this.loadingCtrl.create();
    await loading.present();

    // Intenta contactar con la SEP
    try {
      const options = {
        url: 'https://sanferconecta.live/sep',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          maxResult: "1000",
          nombre,
          paterno,
          materno,
          idCedula: ""}
      };
      const response: HttpResponse = await CapacitorHttp.post(options);
      // console.log('Retorno RAW: ', response);
      await loading.dismiss();


      this.sepData = response.data.items;
      // console.log('Headers ', response.headers);
      // console.log('Retorno en forma de arreglo: ', this.sepData);
      // await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa! <br><h3>Selecciona tu cedula</h3><br>I');
    } catch (e) {

      // Si hay un error intenta conectar nuevamente ocn la sep

      console.log('Error', e);

    }

}


// Back Up Before change the api

  // async lookForCedula(nombre: string, apellido: string, cedula: string){
  //   this.modal.present
  //   const loading = await this.loadingCtrl.create();
  //   await loading.present();
  //   const apellidoDividido =  apellido.split(' ');
  //   console.log('Apelido Dividido: ', apellidoDividido);
  //   const rutaSep = `https://api.allorigins.win/raw?&url=https://www.cedulaprofesional.sep.gob.mx/cedula/buscaCedulaJson.action?json=%7B%22maxResult%22%3A%2250%22%2C%22nombre%22%3A%22${nombre
  //     .replace(/\s/g, '+')}%22%2C%22paterno%22%3A%22${apellidoDividido[0]}%22%2C%22materno%22%3A%22${apellidoDividido[1]}%22%2C%22idCedula%22%3A%22%22%7D`;
  //   //  Por si mas adelante se requiere indica donde se colocaba la cedula
  //   // const rutaSep = `https://api.allorigins.win/raw?&url=https://www.cedulaprofesional.sep.gob.mx/cedula/buscaCedulaJson.action?json=%7B%22maxResult%22%3A%2250%22%2C%22nombre%22%3A%22${this.doctorName
  //   //     .replace(/\s/g, '+')}%22%2C%22paterno%22%3A%22${apellidoDividido[0]}%22%2C%22materno%22%3A%22${apellidoDividido[1]}%22%2C%22idCedula%22%3A%22${this.doctorCedula}%22%7D`;
  //   console.log('URL de la SEP', rutaSep);
  //   console.log('https://sanferconecta.live/sep/new');
  //   // Intenta contactar con la SEP
  //   try {
  //     const options = {
  //       url: 'https://sanferconecta.live/sep/new',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       },
  //       data: {
  //         url: rutaSep,
  //         // nombre,
  //         // apellidoP: apellidoDividido[0],
  //         // apellidoM: apellidoDividido[1],
  //         // cedula
  //
  //       }
  //     };
  //     const response: HttpResponse = await CapacitorHttp.post(options);
  //     console.log('Retorno RAW: ', response);
  //     await loading.dismiss();
  //
  //
  //     this.sepData = response.data.items;
  //     console.log('Headers ', response.headers);
  //     console.log('Retorno en forma de arreglo: ', this.sepData);
  //     await this.widgetUtilService.presentToast('¡Conexion con la SEP exitosa! <br><h3>Selecciona tu cedula</h3><br>I');
  //   } catch (e) {
  //
  //     // Si hay un error intenta conectar nuevamente ocn la sep
  //
  //     console.log('Error', e);
  //
  //   }
  //
  // }


  async obtenerDatos(cedula: any) {
    // Asegúrate de que `cedula` tenga un tipo adecuado en lugar de `any`.
    this.cedulaData = {
      Nombre: cedula.nombre,
      Paterno: cedula.paterno,
      Materno: cedula.materno,
      Cedula: cedula.idCedula,
      Titulo: cedula.titulo,
      Institucion: cedula.desins,
      AnioRegistro: cedula.anioreg,
      Tipo: cedula.tipo,
      Genero: cedula.sexo
    };
    await this.modalCtrl.dismiss();

    const verificacion = this.titulosPermitidos.some(el => this.cedulaData.Titulo.includes(el));

    this.signupForm.patchValue({
      nombre: this.cedulaData.Nombre,
      apellido: `${this.cedulaData.Paterno} ${this.cedulaData.Materno}`,
      cedula: this.cedulaData.Cedula,
      titulo: this.cedulaData.Titulo
    });

    if (verificacion) {
      await this.widgetUtilService.presentToast('¡Cedula verificada! Puedes registrarte');
    } else {
      await this.widgetUtilService.presentToastError('Disculpe esta aplicación es solo para los profesionales de la Salud, si se equivoco de cedula profesional intente nuevamente');
    }
  }


  // async obtenerDatos(cedula: any) {
  //
  //   this.cedulaData = {
  //     Nombre: cedula.nombre,
  //     Paterno: cedula.paterno,
  //     Materno: cedula.materno,
  //     Cedula: cedula.idCedula,
  //     Titulo: cedula.titulo,
  //     Institucion: cedula.desins,
  //     AnioRegistro: cedula.anioreg,
  //     Tipo: cedula.tipo,
  //     Genero: cedula.sexo
  //   };
  //   await this.modalCtrl.dismiss();
  //
  //   // const {data} = await modal.onDidDismiss();
  //   // console.log(this.carrito);
  //   // this.carrito = data;
  //
  //
  //   console.log('Titulos permitidos: ', this.titulosPermitidos);
  //
  //   // const condiciones = this.titulosPermitidos;
  //
  //   const verificacion = this.titulosPermitidos.some((el: string) => this.cedulaData.Titulo.includes(el));
  //
  //   if (verificacion === true) {
  //     const nombreRecabado = (<HTMLInputElement>document.getElementById('nombre'))
  //       .value = `${this.cedulaData.Nombre}`;
  //     const apellidoRecabado = (<HTMLInputElement>document.getElementById('apellido'))
  //       .value = `${this.cedulaData.Paterno} ${this.cedulaData.Materno}`;
  //     const cedulaRecabado = (<HTMLInputElement>document.getElementById('cedula'))
  //       .value = `${this.cedulaData.Cedula}`;
  //     const tituloRecabado = (<HTMLInputElement>document.getElementById('titulo'))
  //       .value = `${this.cedulaData.Titulo}`;
  //
  //     this.widgetUtilService.presentToast('¡Cedula verificada!<br> Puedes registrarte');
  //
  //
  //   } else {
  //     const nombreRecabado = (<HTMLInputElement>document.getElementById('nombre'))
  //       .value = `${this.cedulaData.Nombre}`;
  //     const apellidoRecabado = (<HTMLInputElement>document.getElementById('apellido'))
  //       .value = `${this.cedulaData.Paterno} ${this.cedulaData.Materno}`;
  //     const tituloRecabado = (<HTMLInputElement>document.getElementById('titulo'))
  //       .value = `${this.cedulaData.Titulo}`;
  //
  //
  //     await this.widgetUtilService.presentToastError('Disculpe esta aplicación es solo para los profesionales de la Salud, si se equivoco de cedula profesional intente nuevamente');
  //   }
  // }

  closeModal() {
    this.modalCtrl.dismiss().then(response => {
      console.log('Close modal', response)
    });
  }



  buscar( event: any ) {
    this.textoBuscar = event.detail.value;
    console.log(this.textoBuscar)

  }

  async openBrowser(url:string) {
    await this.analyticsService.logEvent('open_browser', {url: url})
    await this.browser.openBrowser(url)
  }

  resetForm() {
    this.signupForm.reset();
    this.formError = {
      nombre: '',
      cedula: '',
      titulo: '',
      paterno: '',
      materno: '',
      email: '',
      password: '',
      telefono: ''
    };
  }

}
