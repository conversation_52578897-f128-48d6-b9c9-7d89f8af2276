import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { SpringerArticlePage } from './springer-article.page';
import {NgArrayPipesModule} from "ngx-pipes";
import {PipesModule} from "../../pipes/pipes.module";

const routes: Routes = [
  {
    path: '',
    component: SpringerArticlePage
  }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes),
        NgArrayPipesModule,
        PipesModule
    ],
  declarations: [SpringerArticlePage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SpringerArticlePageModule {}
