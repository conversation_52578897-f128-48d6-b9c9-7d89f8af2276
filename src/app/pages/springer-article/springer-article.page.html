<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Articulo de Springer</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/springer"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>


  <ion-grid [fixed]="true" >

    <ion-list >
      <ion-item *ngFor="let articulo of articulos">
        <ion-label class="ion-text-wrap container">
          <h1>
            {{articulo.title}}
          </h1>
          <h5 style="" *ngIf="articulo.publicationName">
            <span class="top-border">{{articulo.publicationName}}</span>
          </h5>

          <p *ngIf="articulo.abstract">
            {{articulo.abstract }}
          </p>

          <p class="padding-top small"><ion-icon color="primary" name="calendar"></ion-icon> Fecha de publicación: {{articulo.publicationDate}}</p>
          <p class=" small">
            <ion-icon color="primary" name="book"></ion-icon> Autores: <span *ngFor="let autor of articulo.creators">{{autor.creator}} | </span>
          </p>
          <p class=" small">
            <ion-icon color="primary" name="book"></ion-icon> Temas: <span *ngFor="let subject of articulo.subjects">{{subject}} | </span>
          </p>
          <p class=" small">
            <ion-icon color="primary" name="book"></ion-icon> Publisher: {{articulo.publisher}}
          </p>

          <div class="ion-padding-top ion-padding-bottom">
            <ion-button expand="block" (click)="openPDF(articulo.url[1].value)"><ion-icon slot="start" name="download" color="primary" ></ion-icon> Descargar PDF <ion-icon name="paper" size="small"></ion-icon></ion-button>

          </div>

          <ion-list>
            <ion-item>
              <ion-label>
                Links del articulo:

              </ion-label>

            </ion-item>
            <ion-item *ngFor="let url of articulo.url" (click)="openBrowser(url.value)" button="true" detail="true">
              <ion-label class="ion-text-wrap">
                {{url.value}}
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <p class="ion-text-center">{{articulo.copyright}}</p>

              </ion-label>

            </ion-item>
          </ion-list>

        </ion-label>
      </ion-item>
    </ion-list>
  </ion-grid>





</ion-content>
