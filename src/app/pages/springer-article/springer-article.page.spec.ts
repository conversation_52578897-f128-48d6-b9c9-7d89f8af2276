import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SpringerArticlePage } from './springer-article.page';

describe('SpringerArticlePage', () => {
  let component: SpringerArticlePage;
  let fixture: ComponentFixture<SpringerArticlePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SpringerArticlePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SpringerArticlePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
