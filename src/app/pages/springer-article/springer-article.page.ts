import { Component, OnInit } from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {SpringerService} from '../../services/springer.service';
import {Records} from '../../interfaces/interfaces';
import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';
// import { Plugins } from '@capacitor/core';
import {map} from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
// const { Browser, Http, Storage } = Plugins;
import {Observable} from 'rxjs';
// import function to register Swiper custom elements
import { register } from 'swiper/element/bundle';
// register Swiper custom elements
register();
import {WidgetUtilService} from '../../services/widget-util.service';
import {Platform} from '@ionic/angular';
import {Firestore, doc,setDoc, docData, collection, getDoc, writeBatch} from '@angular/fire/firestore';



// Actualizacion de Capacitor a 3.0
// import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import {CapacitorHttp, HttpResponse} from '@capacitor/core';

import { Preferences } from '@capacitor/preferences';
import { Browser } from '@capacitor/browser';
import {BrowserService} from "../../services/browser.service";
import {InteractionService} from "../../services/interaction.service";
import {SpringerResponse} from "../../models/springer.model";
// import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import { Device } from '@capacitor/device';
// import { Geolocation } from '@capacitor/geolocation';



@Component({
  selector: 'app-springer-article',
  templateUrl: './springer-article.page.html',
  styleUrls: ['./springer-article.page.scss'],
})
export class SpringerArticlePage implements OnInit {

  doi = '';
  logDocument: any;
  strapiUrl = environment.strapiURL;
  uid: any;
  articulos: any = [];
  slides: any = [];
  activado = false;
  mensajeDemora = 'Estamos intentando descargar el documento';
  slideOpts = {
        initialSlide: 1,
        speed: 400,
        pager: true,
        autoplay: true

    };
  constructor(private activatedRoute: ActivatedRoute,
              private  springerService: SpringerService,
              private strapi: StrapiService,
              private http: HttpClient,
              private widgetUtilService: WidgetUtilService,
              private platform: Platform,
              // private db: AngularFirestore,
              private firestore: Firestore,
              private browser: BrowserService,
              private interaction: InteractionService




  ) {

    this.activatedRoute.params.subscribe((result: any) => {
       this.doi = result.doi;
       // this.logDocument = result.id2;
       console.log('resultado de doi', this.doi);

    });
  }

  async ngOnInit() {

    await this.getUserInStorage();
    await this.getArticleContent();



  }

  async getUserInStorage() {
    const ret: any = await Preferences.get({ key: 'user' });
    const user = JSON.parse(ret.value);
    this.uid = user.uid;
  }

  async setInteraction() {
    const articulo: SpringerResponse = this.articulos[0];
    const data= {
      titulo: articulo.title,
      autor: articulo.creators[0],
      aditionalData: articulo.publicationName,
      doi: articulo.doi,
      imagen: '/assets/inicio/Icono_springer.svg',
      type: 'springer-article'
    }
    console.log('Data:  ', data)
    console.log('uid :  ', this.uid)

    await this.interaction.registrarInteraccion(this.uid, `/springer/${encodeURIComponent(this.doi)}`, data);
  }

  async getArticleContent() {
    const doi = await this.springerService.searchDOI(this.doi)
    const doiData: any = doi.data

    this.articulos = doiData.records

    await this.setInteraction();
    console.log('Doi data:  ', this.articulos[0])



  }
  demora() {
      this.widgetUtilService.dismissLoader();
      this.mensajeDemora = 'Esta demorando más de lo esperado esta descarga, seguramente el documento es muy pesado, si no quieres continuar esperando da click afuera de este mensaje para salir';
      this.widgetUtilService.presentLoadingMessage(this.mensajeDemora);

  }

  extractDoiFromLink(link: string): string {
    const doiArray = link.split(':');
    const doi = doiArray[2]; // Asumiendo que el DOI siempre está en esta posición
    return doi;
  }

  async openPDF(link: string) {
    const mensajeCarga = this.platform.is('android') ?
      'Por favor espera, el archivo se guardará en tu carpeta de descargas' :
      this.mensajeDemora;
    this.widgetUtilService.presentLoadingMessage(mensajeCarga);

    try {

      const doi = this.extractDoiFromLink(link); // Asumiendo que tienes una función extractDoiFromLink
      const response: HttpResponse = await this.postPDF(link, doi);

      if (response.status === 200) {
        await this.handleSuccessfulDownload(link);
      } else {
        throw new Error('Error de conexión en el servidor');
      }
    } catch (error) {
      console.error('Error:', error);
      await this.widgetUtilService.dismissLoader();
      await this.widgetUtilService.presentToastError('Ocurrió un error, intenta nuevamente');
    }
  }

// Separar lógica en funciones auxiliares para claridad y reusabilidad
  async postPDF(link: string, doi: string): Promise<HttpResponse> {
    const options = {
      url: 'https://sanferconecta.live/springer/pdf',
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        url: link,
        doi
      }
    };

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);
      return response;
    } catch (error) {
      console.error('Error al realizar la solicitud POST:', error);
      throw error; // Lanza el error para que pueda ser capturado por el llamador
    }
  }

  async handleSuccessfulDownload(link: string) {
    try {
      // Extraer el DOI del enlace
      const doiSplitted = link.split(':')[2].split('/');
      const doi = doiSplitted[1];

      // Abrir el PDF en el navegador
      await Browser.open({ url: `https://sanferconecta.live/springer/pdf/${doi}` });

      // Actualizar Firestore con los detalles de la descarga
      const historialRef = doc(this.firestore, `medicos/${this.uid}/historial/${this.logDocument}`);
      await setDoc(historialRef, {
        doi,
        downloaded: true,
        url: `https://sanferconecta.live/springer/pdf/${doi}`
      }, { merge: true });

      // Otras acciones después de la descarga exitosa, si las hay
      // ...

      await this.widgetUtilService.dismissLoader();
      if (this.platform.is('android')) {
        await this.widgetUtilService.presentToastQuit('Se ha descargado el archivo PDF, por favor revisa tu carpeta de descargas');
      }
      // Manejo de otras plataformas, si es necesario
      // ...
    } catch (error) {
      console.error('Error al manejar la descarga exitosa:', error);
      // Manejar el error de manera adecuada
      await this.widgetUtilService.dismissLoader();
      await this.widgetUtilService.presentToastError('Ocurrió un error durante el proceso de descarga');
    }
  }
  // async openPDF(link: string) {
  //     if (this.platform.is('android') ) {
  //         this.widgetUtilService.presentLoadingMessage('Porfavor espera, el archivo se guardará en tu carpeta de descargas');
  //         setTimeout(() => {
  //             this.demora();
  //         }, 10000);
  //
  //     } else {
  //         // const demora = 'Esta demorando mucho la descarga, da click aquí';
  //         this.widgetUtilService.presentLoadingMessage(this.mensajeDemora);
  //         setTimeout(() => {
  //             this.demora();
  //         }, 10000);
  //
  //
  //     }
  //     const doiArray = link.split( ':' );
  //     const doi = doiArray[2];
  //
  //   const options = {
  //     url: 'https://sanferconecta.live/springer/pdf',
  //     headers: {
  //       'Content-Type': 'application/json'
  //     },
  //     data: {
  //       url: link,
  //       doi
  //     }
  //
  //   };
  //
  //   const response: HttpResponse = await CapacitorHttp.post(options);
  //
  //     if (response.status === 200) {
  //         try {
  //             const doiSplitted = link.split( ':' )[2].split( '/' );
  //             await Browser.open({ url: `https://sanferconecta.live/springer/pdf/${doiSplitted[1]}` });
  //
  //             // Update Firestore
  //             const historialRef = doc(this.firestore, `medicos/${this.uid}/historial/${this.logDocument}`);
  //             await setDoc(historialRef, {
  //                 doi: doiSplitted[1],
  //                 downloaded: true,
  //                 url: `https://sanferconecta.live/springer/pdf/${doiSplitted[1]}`
  //             }, { merge: true });
  //
  //             await this.widgetUtilService.dismissLoader();
  //             if (this.platform.is('android')) {
  //                 await this.widgetUtilService.presentToastQuit('Se ha descargado el archivo PDF, por favor revisa tu carpeta de descargas');
  //             }
  //             // Handle other platforms...
  //         } catch (error) {
  //             console.error('Error del servidor:', error);
  //             await this.widgetUtilService.dismissLoader();
  //             // Handle the error appropriately
  //         }
  //     } else {
  //         await this.widgetUtilService.dismissLoader();
  //         await this.widgetUtilService.presentToastError('Error de conexión en el servidor, intenta nuevamente');
  //     }
  //
  //
  // }


  async openBrowser(url:string) {
    await this.browser.openBrowser(url)
  }


}
