import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { SpringerPage } from './springer.page';
import {NgPipesModule} from 'ngx-pipes';
import {PipesModule} from "../../pipes/pipes.module";

const routes: Routes = [
  {
    path: '',
    component: SpringerPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    NgPipesModule,
    PipesModule
  ],
  declarations: [SpringerPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SpringerPageModule {}
