<ion-header >
  <ion-toolbar color="primary">
    <ion-title >Springer</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/inicio"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar *ngIf="!searchFiltering">
    <ion-searchbar style="padding-top: 10px" #searchSpringer placeholder="Escribe el termino y presiona Enter" [debounce]="500" (ionClear)="clearSearch()" (ionChange)="search( $event )"></ion-searchbar>
<!--    <ion-searchbar [(ngModel)]="searchTerm" (search)="searchChanged($event)" placeholder="Escribe el termino a buscar y presiona Enter"></ion-searchbar>-->
    <ion-buttons style="padding-top: 10px" slot="end">
      <ion-button color="primary" (click)="openSearchModal()" *ngIf="filterOptions"><ion-icon name="filter-circle-outline"></ion-icon>Filtrar</ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-button color="light" class="ion-no-margin" size="small" expand="full" *ngIf="searchFiltering" (click)="limpiarBusqueda();"><ion-icon name="close"></ion-icon> Borrar resultados</ion-button>


</ion-header>

<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="springerRefresh($event)">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>






  <ion-grid [fixed]="true" class="ion-no-padding">
    <ion-row>
      <ion-col>
        <ion-card color="primary" *ngIf="searchInProgress">
          <ion-card-content>
            <ng-container   *ngIf="(records | orderBy: orderBy | multiFilter: textoBuscar : ['title', 'publicationName', 'creators[0].creator']) as filteredResults">
              <!-- Mostrando el número de resultados -->
              <div style="font-size: 12px; text-align: center">Hemos encontrado {{results.total}} resultados coincidentes con tu búsqueda y se muestran {{ filteredResults.length }}, desplazate o filtra tu busqueda para obetener más</div>
            </ng-container>

          </ion-card-content>

        </ion-card>

        <div *ngIf="!searchInProgress">
          <ion-card *ngIf="springers" mode="md" style="box-shadow: none;">
            <ion-card-header>
              <ion-card-title class="ion-text-center" style="font-size: 14px" >
                Te recomendamos los siguientes articulos médicos basados en tu especialidad medica
              </ion-card-title>
            </ion-card-header>
            <ion-card-content class="ion-no-padding">
              <swiper-container pagination="true" slides-per-view="2.5" space-between="10" slides-offset-before="10" slides-offset-after="10" class="sipringerSlides">
                <swiper-slide *ngFor="let springer of springers">
                  <ion-card style="margin-bottom: 40px" (click)="openBrowser(springer.url)"  class="newsCard"  mode="md">
                    <ion-img [routerLink]="springer.url" src="{{strapiUrl}}{{springer.image.url}}"></ion-img>
                    <ion-card-header class="newsCardHeader">
                      <ion-card-title color="primary" *ngIf="springer.title" class="title-container">
                        <span class="title-text">{{springer.title }}</span>
                        <ion-icon name="arrow-forward" class="title-icon"></ion-icon>
                      </ion-card-title>
                    </ion-card-header>
                  </ion-card>
                </swiper-slide>
              </swiper-container>
            </ion-card-content>
          </ion-card>
        </div>
      </ion-col>
    </ion-row>

    <ion-row>

      <ion-col class="ion-no-padding" >

        <!-- Envoltura para asignar los resultados filtrados a una variable -->

        <ion-list *ngFor="let articulo of records | orderBy: orderBy | multiFilter: textoBuscar : ['title', 'publicationName', 'abstract', 'creators[0].creator']; let i = index">
          <ion-item button="true" detail="true" (click)="openArticle(articulo.doi)">
            <ion-label class="ion-text-wrap ">
              <div class="container">
                <h3>
                  {{articulo.title}}
                </h3>
                <h5 style="" *ngIf="articulo.publicationName">
                  <span class="top-border">{{ i + 1 }}. {{articulo.publicationName}}</span>
                </h5>

                <p *ngIf="articulo.abstract">
                  {{articulo.abstract | slice:0:120}}
                </p>

                <p class="padding-top small"><ion-icon color="primary" name="calendar"></ion-icon> Fecha de publicación: {{articulo.publicationDate}}</p>
                <p class=" small">
                  <ion-icon color="primary" name="book"></ion-icon> Autores: <span *ngFor="let autor of articulo.creators">{{autor.creator}} | </span>
                </p>
                <p class=" small">
                  <ion-icon color="primary" name="book"></ion-icon> Temas: <span *ngFor="let subject of articulo.subjects">{{subject}} | </span>
                </p>
              </div>

            </ion-label>
          </ion-item>
        </ion-list>
<!--        <ion-card >-->
<!--          <ion-card-header class="no-paddingBottom" (click)="openArticle(articulo.doi)">-->
<!--            <ion-card-subtitle>-->
<!--              <span class="text-primary">{{ i + 1 }}. </span>-->
<!--              <span *ngIf="articulo.contentType">{{articulo.contentType}}</span>-->
<!--            </ion-card-subtitle>-->
<!--            <ion-card-title *ngIf="articulo.title">{{articulo.title}}</ion-card-title>-->
<!--            <ion-card-subtitle class="subtitulo" *ngIf="articulo.publicationName">{{articulo.publicationName}}</ion-card-subtitle><br>-->
<!--          </ion-card-header>-->
<!--          <ion-card-content (click)="openArticle(articulo.doi)" class="ion-text-justify no-paddingTop" *ngIf="articulo.abstract">{{articulo.abstract | slice:0:360}}-->
<!--          </ion-card-content>-->
<!--          <ion-card-subtitle class="ion-text-center" ><ion-icon name="calendar"></ion-icon> Fecha de publicación: {{articulo.publicationDate}} <br> <ion-icon name="book"></ion-icon> Editorial: {{articulo.publisher}}<br></ion-card-subtitle>-->
<!--        </ion-card>-->
      </ion-col>
      <ion-infinite-scroll *ngIf="searchInProgress" (ionInfinite)="onIonInfinite($event)">
        <ion-infinite-scroll-content loadingText="Porfavor espera mientras cargamos mas articulos de springer..." ></ion-infinite-scroll-content>
      </ion-infinite-scroll>
    </ion-row>
  </ion-grid>




  <ion-modal
    #searchModal
    [isOpen]="false"
    [initialBreakpoint]="0.25"
    [breakpoints]="[0, 0.25, 0.5]"
    [backdropDismiss]="true"
    [backdropBreakpoint]="0.25"
  >
    <ng-template>
      <ion-content class="ion-padding ion-text-center" color="primary">
        <!--        <ion-searchbar placeholder="Search" (click)="searchModal.setCurrentBreakpoint(0.5)"></ion-searchbar>-->
        <ion-searchbar #searchBar color="light" placeholder="Refina tu busuqueda" (click)="searchModal.setCurrentBreakpoint(0.25)" (ionInput)="buscar( $event )"></ion-searchbar>
        <ng-container  *ngIf="(records | orderBy: orderBy | multiFilter: textoBuscar : ['title', 'publicationName', 'creators[0].creator']) as filteredResults">
          <!-- Mostrando el número de resultados -->
          <div style="padding-bottom: 12px">Se muestran {{ filteredResults.length }} resultados</div>
        </ng-container>

        <ion-button color="secondary" size="small" (click)="moreArticles()">Mostrar mas resultados</ion-button>
        <div class="ion-text-center">
          <ion-chip class="chipWhite">
            <ion-label>Ordenar por:</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('title')" [ngStyle]="{'color':orderBy === 'title' ? 'white' : '', 'background-color':orderBy === 'title' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'title' ? 'red' : ''}"></ion-icon>
            <ion-label>Titulo</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('-publicationDate')" [ngStyle]="{'color':orderBy === '-publicationDate' ? 'white' : '', 'background-color':orderBy === '-publicationDate' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === '-publicationDate' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas recientes</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('publicationDate')" [ngStyle]="{'color':orderBy === 'publicationDate' ? 'white' : '', 'background-color':orderBy === 'publicationDate' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'publicationDate' ? 'red' : ''}"></ion-icon>
            <ion-label>Mas antiguas</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('publicationName')" [ngStyle]="{'color':orderBy === 'publicationName' ? 'white' : '', 'background-color':orderBy === 'publicationName' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'publicationName' ? 'red' : ''}"></ion-icon>
            <ion-label>Subtitulo</ion-label>
          </ion-chip>
          <ion-chip  (click)="selecionado('creators[0].creator')" [ngStyle]="{'color':orderBy === 'creators[0].creator' ? 'white' : '', 'background-color':orderBy === 'creators[0].creator' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color':orderBy === 'creators[0].creator' ? 'red' : ''}"></ion-icon>
            <ion-label>Autor</ion-label>
          </ion-chip>
        </div>


      </ion-content>
    </ng-template>
  </ion-modal>


</ion-content>
<!--<ion-footer >
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
    </ion-buttons>
    <ion-title slot="" >Powered by ®Springer</ion-title>
  </ion-toolbar>

</ion-footer>-->
