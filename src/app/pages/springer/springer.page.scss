

.container {
  h3{
    font-weight: bold;
  }

  h5{
    padding-top: 5px;

  }
  span.top-border {
    color: var(--ion-color-primary);
    border-top: 2px solid red;;
  }
  p.padding-top{
    padding-top: 5px;

  }
  p.small{
    font-size: 10px;

  }
  span.date {
    //color: var(--ion-color-primary);
    border-top: 2px solid red;;
  }


}
// Estilos específicos para ion-card
ion-card.newsCard {
  height: auto; // o una altura fija dependiendo del diseño
  margin: 0;

  // Redondear las esquinas
  border-radius: 16px; // Radio de borde que se asemeje al diseño

  // Sombra para la tarjeta
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12); // Ajustar según la imagen

  // Estilo para la imagen de la tarjeta
  ion-img {
    width: 100%; // Asegura que la imagen cubra todo el ancho de la tarjeta
    height: 150px; // Altura fija para la imagen, ajustar según necesidad
    object-fit: cover; // Asegura que la imagen cubra todo el espacio sin deformarse
    border-top-left-radius: 16px; // Radio de borde superior izquierdo
    border-top-right-radius: 16px; // Radio de borde superior derecho
  }



  ion-card-title {
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 8px; // Espacio debajo del título
  }



  .newsCardHeader {
    padding-top: 0;
    padding-bottom: 0;
  }

  .newsCardDate {
    margin-top:8px;
    font-size: 8px;
    //padding-bottom: 5px;

  }
  .newsCardSubtitle{
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 10px;

  }



  .title-container {
    padding: 10px 0 0px 0;
    display: flex; // Use Flexbox for layout
    align-items: center; // Align items vertically in the center

    .title-text {
      flex-grow: 1; // Allows the text to expand
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; // Limit to two lines
      overflow: hidden; // Hide overflow
      text-overflow: ellipsis; // Add ellipsis to overflow
      margin-right: 10px; // Add some margin before the icon
    }

    .title-icon {
      // Style as needed
    }
  }
  // Estilos para el contenido de la tarjeta
  .card-content {
    //padding: 16px; // Espaciado interior para el contenido

    // Estilos para el título
    h2 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px; // Espacio debajo del título
    }

    // Estilos para la descripción o el cuerpo del texto
    p {
      font-size: 14px;
      color: #666; // Color de texto gris, ajustar según diseño
    }

    // Estilos para el pie de tarjeta, donde podría ir la información del autor
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px; // Espacio encima del pie de tarjeta

      // Estilos para el autor o fuente de la noticia
      .author {
        font-size: 12px;
        font-weight: bold;
        color: #333; // Color más oscuro para el texto del autor
      }

      // Estilos para los detalles como tiempo de lectura y votos
      .details {
        font-size: 12px;
        color: #999; // Color más claro para los detalles
      }
    }
  }
}





.no-paddingBottom{ padding-bottom: 0px !important}
.no-paddingTop{
  padding-top: 0px !important;
}


ion-card{
  margin: 5px;
  padding: 0;
}

ion-slides {
  padding-bottom: 30px;
}
ion-slides .springers {
  height: 100%;
}
h1 {
  margin-top: 5px;
  font-size: 18px;
}

ion-card-header h3 {
  margin-top: 5px;
  font-weight: bold;
  font-size: 15px;
}

//ion-card-content {
//  display: -webkit-box;
//  -webkit-box-orient: vertical;
//  -webkit-line-clamp: 2; // Limit to four lines
//  overflow: hidden; // Hide overflow
//  text-overflow: ellipsis; // Add ellipsis at the end
//}






.sipringerSlides {
  --swiper-pagination-color: var(--ion-color-primary);

  //--swiper-pagination-bottom: 20px;


}

.card-header{
  padding: 10px;
}

.title-container {
  display: flex; // Use Flexbox for layout
  align-items: center; // Align items vertically in the center

  .title-text {
    font-size: 10px;
    flex-grow: 1; // Allows the text to expand
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3; // Limit to two lines
    overflow: hidden; // Hide overflow
    text-overflow: ellipsis; // Add ellipsis to overflow
    //margin-right: 10px; // Add some margin before the icon
  }

  .title-icon {
    font-size: 20px;
    // Style as needed
  }

}
.card-content {
  padding: 0 10px 10px 10px;
  font-size: 10px;
}


.autor-curso {
  font-size: 12px;
}

@media (max-width: 600px) {
  ion-card-header h3 {
    font-weight: bold;
    font-size: 12px;
  }


  .autor-curso {
    font-size: 10px;
  }

}

@media (min-width: 600px) and (max-width: 990px)  {
  ion-card-header h3 {
    font-weight: bold;
    font-size: 14px;
  }

  //.description-class {
  //  display: none;
  //}

}


@media (min-width: 991px) and (max-width: 1600px)  {
  ion-card-header h3 {
    font-weight: bold;
    font-size: 12px;
  }
  .autor-curso {
    font-size: 10px;
  }

  //.description-class {
  //  display: none;
  //}

}


ion-toggle {
  height: 20px;
}

