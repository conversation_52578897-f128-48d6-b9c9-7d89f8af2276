import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SpringerPage } from './springer.page';

describe('SpringerPage', () => {
  let component: SpringerPage;
  let fixture: ComponentFixture<SpringerPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SpringerPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SpringerPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
