import {Component, OnInit, ViewChild} from '@angular/core';
import { SpringerService} from '../../services/springer.service';
import {Router} from '@angular/router';
import {InfiniteScrollCustomEvent, IonInput, IonModal, IonSearchbar, LoadingController} from '@ionic/angular';
import {WidgetUtilService} from '../../services/widget-util.service';
import {StrapiService} from '../../services/strapi.service';
import {environment} from '../../../environments/environment';
import {FirestoreDbService} from '../../services/firestore-db.service';
import {Firestore, doc, docData, collection, getDoc, writeBatch, addDoc, setDoc} from '@angular/fire/firestore';

import { Preferences } from '@capacitor/preferences';
import {ContentService} from "../../services/content.service";
import {BrowserService} from "../../services/browser.service";
import {<PERSON>rowser} from "@capacitor/browser";
import {InteractionService} from "../../services/interaction.service";
import {SpringerResponse} from "../../models/springer.model";
import {AnalyticsService} from "../../services/analytics.service";


@Component({
  selector: 'app-springer',
  templateUrl: './springer.page.html',
  styleUrls: ['./springer.page.scss'],
})
export class SpringerPage implements OnInit {
  @ViewChild('searchModal') searchModal: IonModal;
  @ViewChild('searchBar') searchBar!: IonInput;

  springerData: any;
  nextPage: number
  userContent: any;
  orderBy: string = '-publicationDate';
  searchBy: string = 'title';

  searchFiltering: boolean = false;
  searchInProgress: boolean = false;
  textoBuscar: string = '';



  searchTerm: string;
  records: any = [];
  results: any = [];

  strapiUrl = environment.strapiURL;

  cedula: string = '';
  uid: string = '';
  fecha = new Date();
  date: any;
  minute: any;

  springer: any;


  filterOptions: boolean = false



  slides: any = [];
  springers: any = [];



  activado = false;

  logDocument: any;
  user: any;
  constructor(
      private firestore: Firestore,
      private  springerService: SpringerService,
      private router: Router,
      private widgetUtilService: WidgetUtilService,
      private content: ContentService,
      private browser: BrowserService,
      private interaction: InteractionService,
      private analyticsService: AnalyticsService,

  ) {

  }


  async ngOnInit() {

    await this.getUserContent();
    await this.getUserInStorage();
    await this.analyticsService.setCurrentScreen('Springer' );

  }

  async getUserInStorage() {
    const ret: any = await Preferences.get({ key: 'user' });
    this.user = JSON.parse(ret.value);
    this.uid = this.user.uid;
  }

  selecionado(event: string) {
    this.orderBy = event;
    console.log(this.orderBy);
  }




  async userLog(searchTerm: string) {
    if (this.user.role === 'representante' || this.user.role === 'medico') {
      // Determine the collection based on user role
      const collectionPath = this.user.role === 'representante' ? 'representantes' : 'medicos';
      const userCollectionRef = collection(this.firestore, `${collectionPath}/${this.uid}/historial`);

      try {
        const docRef = await addDoc(userCollectionRef, {
          action: 'Realizo una consulta en springer',
          date: new Date().valueOf(),
          springer: true,
          busqueda: searchTerm,
          openArticle: false
        });

        console.log('Document written with ID: ', docRef.id);
        this.logDocument = docRef.id;
      } catch (error) {
        console.error('Error adding document: ', error);
        // Handle the error appropriately
      }
    }
  }




  async openArticle(doi: string) {
    // Navigate to the springer-article route with the DOI and logDocument ID
    await this.router.navigate(['/springer', doi]);


    console.log('Document written after DOI: ', this.logDocument);
    console.log('DOI Clicked: ', doi);
  }






  async springerRefresh(event: any) {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    await this.content.getUser(userInStorage);
    // await this.content.getNews(300);
    await this.getUserContent();
    event.target.complete();
  }



  async getUserContent () {
    const userContent = await Preferences.get({ key: 'userContent' });
    this.userContent = await JSON.parse(userContent.value);
    const banners = this.userContent.banners
    console.log('Banners loaded: ', banners)

    // Slides de la academia
    this.springers = banners.springers

    console.log('Springers: ', this.springers)
    // this.springersSwitch = banners.springersSwitch

    // await this.widgetUtilService.dismissLoader();
  }

  async openBrowser(url:string) {
    if (url.includes('http')) {
      console.log('Link con http');
      await this.browser.openBrowser(url)

    } else {
      const doi = '/springer/' + url
      await this.browser.openBrowser(doi)

    }
  }


  clearSearch() {
    console.log('Busqueda limpia')
  }


  async buscar(event: any) {
    if (!event.detail.value || event.detail.value.trim() === '') {
      console.log('busqueda no realizada');
      this.searchFiltering = false;
      await this.searchModal.dismiss();

      this.textoBuscar = ''; // Asegúrate de resetear o ajustar adecuadamente
    } else {
      this.searchFiltering = true;
      // Divide el texto de búsqueda en palabras clave y únelas con un espacio.
      // Esto asume que tu pipe de filtrado puede manejar múltiples palabras separadas por espacios como un "AND" lógico.
      this.textoBuscar = event.detail.value.split(' ').filter(word => word.trim() !== '').join(' ');
      console.log(event, 'Busqueda iniciada searchInProgress', this.textoBuscar);
    }
  }

  async limpiarBusqueda () {
    this.searchFiltering = false
    this.textoBuscar = ''
    await this.searchModal.dismiss();

  }

  async openSearchModal() {
    this.searchInProgress = true
    await this.searchModal.present();
    await this.searchBar.setFocus();

  }


  async search(event: any) {
    this.searchInProgress = true
    await this.widgetUtilService.presentLoadingMessage('Cargando articulos de springer...')
    this.filterOptions = true;
    this.searchTerm = event.detail.value;


    const springerData = await this.springerService.fetchSpringer(this.searchTerm);
    await this.setInteraction();

    // console.log('Respuesta del servidor', springerData);


    this.results = springerData.result[0];
    this.records = springerData.records;
    // console.log('Records: ', this.records)
    this.nextPage = this.records.length; // Suponiendo que nextPage es el índice del siguiente conjunto de resultados

    await this.widgetUtilService.dismissLoader();
  }

  async setInteraction() {
    const data = {
      type: 'springer',
      titulo: this.searchTerm,
      imagen: '/assets/inicio/Icono_springer.svg'
    }
    // console.log('data: ', data)
    await this.interaction.registrarInteraccion(this.uid, '/springer', data);
  }


  async moreArticles() {

    const springerData = await this.springerService.fetchSpringer(this.searchTerm, this.nextPage + 1);
    // console.log('Data para imprimir', springerData);
    this.records = [...this.records, ...springerData.records];
    this.nextPage += this.records.length; // Ajustar según la paginación de la API

    // console.log('Records length:', this.records.length);
  }

  async onIonInfinite(ev) {
    await this.moreArticles();
    (ev as InfiniteScrollCustomEvent).target.complete();
  }

  protected readonly event = event;
}
