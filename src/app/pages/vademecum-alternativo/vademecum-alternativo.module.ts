import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { VademecumAlternativoPageRoutingModule } from './vademecum-alternativo-routing.module';

import { VademecumAlternativoPage } from './vademecum-alternativo.page';
import {NgPipesModule} from 'ngx-pipes';
import {PipesModule} from '../../pipes/pipes.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    VademecumAlternativoPageRoutingModule,
    PipesModule,
    NgPipesModule
  ],
  declarations: [VademecumAlternativoPage]
})
export class VademecumAlternativoPageModule {}
