<ion-header>
  <ion-toolbar color="primary">
    <ion-title >Sanfer Vademecum</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-searchbar placeholder="Buscar medicamento" (ionChange)="buscar( $event )"></ion-searchbar>

</ion-header>

<ion-content>

  <ion-toolbar class="ion-text-center">
    <ion-chip>
      <ion-label>Ordenar y buscar por:</ion-label>
    </ion-chip>

    <ion-chip (click)="selecionado('Nombre')" [ngStyle]="{'color':searchBy === 'Nombre' ? 'red' : '', 'background-color':searchBy === 'Nombre' ? 'rgba(255,0,0,0.1)' : ''}">
      <ion-icon name="checkmark-circle" [ngStyle]="{'color':searchBy === 'Nombre' ? 'red' : ''}"></ion-icon>
      <ion-label>Nombre</ion-label>
    </ion-chip>
    <ion-chip (click)="selecionado('SKU')" [ngStyle]="{'color':searchBy === 'SKU' ? 'red' : '', 'background-color':searchBy === 'SKU' ? 'rgba(255,0,0,0.1)' : ''}">
      <ion-icon name="checkmark-circle" [ngStyle]="{'color':searchBy === 'SKU' ? 'red' : ''}"></ion-icon>
      <ion-label>EAN</ion-label>
    </ion-chip>
    <ion-chip (click)="selecionado('PrincipioActivo')" [ngStyle]="{'color':searchBy === 'PrincipioActivo' ? 'red' : '', 'background-color':searchBy === 'PrincipioActivo' ? 'rgba(255,0,0,0.1)' : ''}">
      <ion-icon name="checkmark-circle" [ngStyle]="{'color':searchBy === 'PrincipioActivo' ? 'red' : ''}"></ion-icon>
      <ion-label>Principio Activo</ion-label>
    </ion-chip>
    <ion-chip  (click)="selecionado('Laboratorio')" [ngStyle]="{'color':searchBy === 'Laboratorio' ? 'red' : '', 'background-color':searchBy === 'Laboratorio' ? 'rgba(255,0,0,0.1)' : ''}">
      <ion-icon name="checkmark-circle" [ngStyle]="{'color':searchBy === 'Laboratorio' ? 'red' : ''}"></ion-icon>
      <ion-label>Laboratorio</ion-label>
    </ion-chip>
  </ion-toolbar>

  <ion-grid>
    <ion-card color="primary">
      <ion-card-content>
        <h3 style="font-size: 20px;" class="ion-text-center " >
          Productos sugeridos
        </h3>
      </ion-card-content>
    </ion-card>

    <ion-row  *ngIf="medicamentos">

      <ion-col size="12" size-xs="6" size-sm="6" size-md="4" class="ion-no-padding" size-lg="2" size-xl="2"
               *ngFor="let producto of medicamentos | filterBy: [searchBy]: textoBuscar: false | orderBy: searchBy; let i = index;">

        <ion-card  (click)="openArticle(producto.id)">
          <ion-card-header>
            <ion-card-subtitle class="recomendados"  *ngIf="producto.PrincipioActivo"><ion-icon name="flask" size="" color="primary"></ion-icon> {{ (producto.PrincipioActivo.length>20)? (producto.PrincipioActivo | slice:0:20)+'...':(producto.PrincipioActivo) }}</ion-card-subtitle>
            <ion-card-title class="recomendados"  *ngIf="producto.Nombre">{{producto.Nombre}}</ion-card-title>
            <ion-img style="max-height: 220px"  *ngIf="producto.Imagen_de_producto[0].formats.small" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].formats.small.url}}"></ion-img>

          </ion-card-header>
          <ion-card-content style="font-size: 10px" *ngIf="producto.Descripcion">
            {{(producto.Descripcion.length>80)? (producto.Descripcion | slice:0:80)+'...':(producto.Descripcion) }}
            <br> <ion-icon name="ribbon" size="" color="primary"></ion-icon><span class="small_text" *ngIf="producto.Laboratorio"> {{producto.Laboratorio}}<br> <span *ngIf="producto.SKU"><ion-icon name="barcode" size="" color="primary"></ion-icon> EAN #{{producto.SKU}}</span></span>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-card color="primary"><ion-card-content>
      <h3 style="font-size: 20px;" class="ion-text-center" >Todos nuestros Productos </h3>
    </ion-card-content></ion-card>
    <ion-row>

      <ion-col size="12" size-xs="12" size-sm="6" size-md="4" class="ion-no-padding" size-lg="2" size-xl="2"
               *ngFor="let producto of vademecum | filterBy: [searchBy]: textoBuscar | orderBy: searchBy  let i = index;">

        <ion-card (click)="openArticle(producto.id)">
          <ion-card-header>
            <ion-card-subtitle *ngIf="producto.PrincipioActivo"><ion-icon name="flask" size="" color="primary"></ion-icon> {{ (producto.PrincipioActivo.length>20)? (producto.PrincipioActivo | slice:0:20)+'...':(producto.PrincipioActivo) }}</ion-card-subtitle>
            <ion-card-title *ngIf="producto.Nombre">{{producto.Nombre}}</ion-card-title>
            <ion-img style="height: 200px"  *ngIf="producto.Imagen_de_producto[0].formats.small" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].formats.small.url}}"></ion-img>

          </ion-card-header>
          <ion-card-content *ngIf="producto.Descripcion">
            {{(producto.Descripcion.length>140)? (producto.Descripcion | slice:0:140)+'...':(producto.Descripcion) }}
            <br> <ion-icon name="ribbon" size="" color="primary"></ion-icon><span class="small_text" *ngIf="producto.Laboratorio"> {{producto.Laboratorio}}<br> <span *ngIf="producto.SKU"><ion-icon name="barcode" size="" color="primary"></ion-icon> EAN #{{producto.SKU}}</span></span>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
