import { Component, OnInit } from '@angular/core';
import {Preferences} from '@capacitor/preferences';
import {StrapiService} from '../../services/strapi.service';
import {Router} from '@angular/router';
import {WidgetUtilService} from '../../services/widget-util.service';
import {FirebaseAuthService} from '../../services/firebase-auth.service';
import {SpecialtiesService} from '../../services/specialties.service';
import {LoadingController} from '@ionic/angular';
import {ImagenDeProducto, RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {environment} from '../../../environments/environment';

@Component({
  selector: 'app-vademecum-alternativo',
  templateUrl: './vademecum-alternativo.page.html',
  styleUrls: ['./vademecum-alternativo.page.scss'],
})
export class VademecumAlternativoPage implements OnInit {
    imagenesProd: ImagenDeProducto[] = [];
    vademecum: RespuestaStrapi[] = [];

    // isLoggedIn = false;
    medicamentos: any;

    categoria: any;
    textoBuscar = '';
    searchBy = 'Nombre';
    strapiUrl = environment.strapiURL;

  constructor(private strapi: StrapiService,
              private router: Router,
              private widgetUtilService: WidgetUtilService,
              // private firebaseAuthService: FirebaseAuthService,
              private especialidadesMedicas: SpecialtiesService,
              private loadingCtrl: LoadingController) { }


  async ngOnInit() {

    const loading = await this.loadingCtrl.create({
      message: 'Cargando Productos...',
    });

    await loading.present();
    const storageBannerString: any = await Preferences.get({ key: 'especialidadBanner' });
    const storageBanner = JSON.parse(storageBannerString.value);


    this.strapi.getContenido('medicamentos')
        .subscribe(async resp => {
          const vuser: any = resp
          this.vademecum = vuser;
          this.imagenesProd = vuser.Imagen_de_producto;
          await loading.dismiss();

          this.especialidadesMedicas.categoriasMedicas(storageBanner.id).subscribe( async respuesta => {
            this.categoria = respuesta;
            this.medicamentos =  this.categoria[0].medicamentos;

          });

        });
  }

  buscar( event: any) {
    // console.log(event)
    this.textoBuscar = event.detail.value;
  }
    selecionado(event: any) {
        this.searchBy = event;
        // console.log(this.searchBy)

    }


    openArticle(ProductoID: any) {
        this.router.navigate(['/vademecum-producto', ProductoID]);
    }



}
