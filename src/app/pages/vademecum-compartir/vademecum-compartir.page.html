<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let producto of productos"  >Compartir {{producto.Nombre}}</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="salir()">
        <ion-icon size="large" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-fab horizontal="end" vertical="top" slot="fixed">
  <ion-fab-button color="secondary" (click)="createPdf(nota)">
    <ion-icon name="send"></ion-icon>    </ion-fab-button>
</ion-fab>
<ion-content>

  <ion-card *ngFor="let producto of productos" >
    <ion-card-header>

      <ion-item>
        <ion-label  position="floating"><ion-icon name="document-text"></ion-icon>Agrega una nota para tu paciente:</ion-label>
        <ion-input placeholder="escribir aquí" name="nota" [(ngModel)]="nota"></ion-input>
      </ion-item>
<!--      <ion-card-title class="ion-text-center">{{producto.Nombre}} es el medicamento que te he prescrito, por favor no permitas que lo sustituyan en la farmacia</ion-card-title>-->
      <ion-img class="centrar_imagen" style="width: 300px" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>
      <ion-card-subtitle> {{producto.PresentacionCompartir}}</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content class="ion-text-center">
      <ion-card-subtitle class="ion-text-center small_text" >Muestra este código de barras en la farmacia<br> para que te entreguen ésta presentación</ion-card-subtitle>

      <img #barcode id="barcode">

    </ion-card-content>

  </ion-card>


</ion-content>
