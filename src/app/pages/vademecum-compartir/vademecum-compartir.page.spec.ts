import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VademecumCompartirPage } from './vademecum-compartir.page';

describe('VademecumCompartirPage', () => {
  let component: VademecumCompartirPage;
  let fixture: ComponentFixture<VademecumCompartirPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VademecumCompartirPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VademecumCompartirPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
