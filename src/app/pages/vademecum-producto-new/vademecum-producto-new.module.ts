import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { VademecumProductoNewPageRoutingModule } from './vademecum-producto-new-routing.module';

import { VademecumProductoNewPage } from './vademecum-producto-new.page';
import {PipesModule} from '../../pipes/pipes.module';
import {NgPipesModule} from 'ngx-pipes';
import {VademecumPromocionesPage} from '../vademecum-promociones/vademecum-promociones.page';
import {VademecumPromocionesPageModule} from '../vademecum-promociones/vademecum-promociones.module';
import {VademecumCompartirPageModule} from '../vademecum-compartir/vademecum-compartir.module';
import {VademecumCompartirPage} from '../vademecum-compartir/vademecum-compartir.page';
import {Routes} from '@angular/router';
import {VademecumProductoPage} from '../vademecum-producto/vademecum-producto.page';


const routes: Routes = [
  {
    path: '',
    component: VademecumProductoPage
  }
];
@NgModule({
  // entryComponents: [
  //   VademecumPromocionesPage,
  //   VademecumCompartirPage
  // ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    VademecumProductoNewPageRoutingModule,
    PipesModule,
    NgPipesModule,
    VademecumPromocionesPageModule,
    VademecumCompartirPageModule
  ],
  declarations: [VademecumProductoNewPage]
})
export class VademecumProductoNewPageModule {}
