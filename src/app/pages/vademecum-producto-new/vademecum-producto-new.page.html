<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let producto of productos">{{producto.Nombre}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/vademecum"></ion-back-button>
    </ion-buttons>

    <ion-icon slot="end" name="download" size="large" (click)="descargarPDF()"></ion-icon>

  </ion-toolbar>
</ion-header>

<ion-content *ngIf="isLoggedIn">

  <ion-card *ngFor="let producto of productos">

    <ion-fab>
      <ion-fab-button (click)="img3d=!img3d" [ngClass]="img3d ? 'img2d' : 'img3d'">
        <ng-container *ngIf="img3d">
          2D
        </ng-container>
        <ng-container *ngIf="!img3d">
          3D
        </ng-container>
      </ion-fab-button>
    </ion-fab>



<!--    <ion-button (click)="compartir()" expand="block" >Compartir {{producto.Nombre}} con el paciente</ion-button>-->

    <ng-container *ngIf="img3d">
      <iframe src='https://my.spline.design/abrixone2021copy-cfd4ad8afb4cd5a685bc8a2aea64883a/' frameborder='0' width='100%' height='350px'></iframe><!--    <ion-img class="centrar_imagen" style="max-width: 400px!important" *ngIf="producto.Imagen_de_producto[0]" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
    </ng-container>
    <ng-container *ngIf="!img3d">
<!--      <ion-slides pager="true" [options]="slideOpts" style="max-width: 800px" >-->
<!--        <ion-slide>-->
<!--          <ion-img style="margin-bottom: 40px" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
<!--        </ion-slide>-->
<!--        <ion-slide>-->
<!--          <ion-img style="margin-bottom: 40px" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
<!--        </ion-slide>-->
<!--      </ion-slides>-->
<!--      <ion-img class="centrar_imagen" style="max-width: 400px!important" *ngIf="producto.Imagen_de_producto[0]" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>-->
    </ng-container>

    <ion-card-header>
      <ion-card-title>{{producto.Nombre}}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-fab style="top: -60px" slot="fixed" vertical="top" horizontal="end" [edge]="true">
        <ion-fab-button (click)="compartir()">
          <ion-icon name="share"></ion-icon>
        </ion-fab-button>
<!--        <ion-fab-list side="top">-->
<!--          <ion-fab-button>-->
<!--            <ion-icon name="share"></ion-icon>-->
<!--          </ion-fab-button>-->
<!--          <ion-fab-button>-->
<!--            <ion-icon name="color-palette"></ion-icon>-->
<!--          </ion-fab-button>-->
<!--          <ion-fab-button>-->
<!--            <ion-icon name="globe"></ion-icon>-->
<!--          </ion-fab-button>-->
<!--        </ion-fab-list>-->
      </ion-fab>
      <span class="text-primary"><ion-icon name="water"></ion-icon> Denominación genérica: </span>{{producto.PrincipioActivo}}
      <br><span class="text-primary"><ion-icon name="flask"></ion-icon> Forma farmacéutica y formulación: </span><br> {{producto.Concentracion}}<br>
<!--      <br><span class="text-primary"><ion-icon name="flask"></ion-icon> Concentración: </span> {{producto.Concentracion}}<br>-->

<!--      <br><span class="text-primary"> <ion-icon name="medkit"></ion-icon> Indicaciones terapéuticas:</span>-->
<!--      <br> <div [innerHTML]="productoM"></div><br>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal"> <ion-icon style="padding-right: 10px" name="arrow-forward"></ion-icon> Farmacocinética y farmacodinamia </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal1" > <ion-icon size="large" style="padding-right: 10px" name="arrow-forward"></ion-icon> Contraindicaciones:</ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal2" > <ion-icon name="arrow-forward"></ion-icon> Precauciones Generales:</ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal3" > <ion-icon name="arrow-forward"></ion-icon> Restricciones de uso <br>durante el embarazo y la lactancia</ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal4" > <ion-icon name="arrow-forward"></ion-icon> Reacciones secundarias y adversas</ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal5" > <ion-icon name="arrow-forward"></ion-icon> Interacciones medicamentosas y de otro género</ion-button>-->

      <ion-card id="open-modal1" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Indicaciones terapéuticas:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Farmacocinética y farmacodinamia:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal2" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Contraindicaciones:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal3" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Precauciones Generales:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal4" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Restricciones de uso durante el embarazo y la lactancia:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal5" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Reacciones secundarias y adversas:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>

      <ion-card id="open-modal6" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Interacciones medicamentosas y de otro género:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal7" button="" style="margin: 5px 0; " class=" ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Alteraciones en los resultados de pruebas de laboratorio:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal8" button="" style="margin: 5px 0;" class=" ion-no-margin" color="tertiary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                          name="arrow-forward"></ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Precauciones en relación con efectos de carcinogénesis, mutagénesis, teratogénesis y sobre la fertilidad:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card  id="open-modal9" button="" style="margin: 5px 0;" class="ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon
                        style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                        name="arrow-forward">
                </ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Dosis y vía de administración:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal10" button="" style="margin: 5px 0;" class="ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon
                        style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                        name="arrow-forward">
                </ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Manifestaciones y manejo de la sobredosificación o ingesta accidental:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal11" button="" style="margin: 5px 0;" class="ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon
                        style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                        name="arrow-forward">
                </ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Presentaciones:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal12" button="" style="margin: 5px 0;" class="ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon
                        style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                        name="arrow-forward">
                </ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Recomendaciones sobre almacenamiento:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
      <ion-card id="open-modal13" button="" style="margin: 5px 0;" class="ion-no-margin" color="secondary">

        <ion-card-content class="ion-no-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="1.5" size-md="2">
                <ion-icon
                        style="
                        height: 100%;
                        display: block;
                        vertical-align: middle;"
                        name="arrow-forward">
                </ion-icon>

              </ion-col>
              <ion-col size="10.5" size-md="10">
                Leyendas de protección:

              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>


      <!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal6" > <ng-container><ion-icon name="arrow-forward"></ion-icon> Alteraciones en los resultados de pruebas de laboratorio:</ng-container> </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal7" > <ion-icon style="width: 35px!important; padding-right: 10px" name="arrow-forward"></ion-icon> Precauciones en relación con efectos de carcinogénesis, mutagénesis, teratogénesis y sobre la fertilidad: </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal8" > <ion-icon name="arrow-forward"></ion-icon> Dosis y vía de administración: </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal9" > <ion-icon name="arrow-forward"></ion-icon> Manifestaciones y manejo de la sobredosificación o ingesta accidental: </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal10" > <ion-icon name="arrow-forward"></ion-icon> Presentaciones: </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal11" > <ion-icon name="arrow-forward"></ion-icon> Recomendaciones sobre almacenamiento: </ion-button>-->
<!--      <ion-button color="secondary" class="medicament-list" expand="block" id="open-modal12" > <ion-icon name="arrow-forward"></ion-icon> Leyendas de protección: </ion-button>-->

      <!--      <ion-button  expand="block">Open</ion-button>-->
<!--      <p>{{ message }}</p>-->

<!--      <br><span class="text-primary"><ion-icon name="ribbon"></ion-icon>Laboratorio:</span> {{producto.Laboratorio}}<br>-->
      <span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>{{producto.SKU}}<br>
      <ion-card-subtitle><span class="text-primary"><ion-icon name="book"></ion-icon> Registro sanitario:</span> {{producto.Registro_sanitario}}</ion-card-subtitle>

      <ion-button size="large" expand="block" (click)="descargarPDF()" >Descargar IPP</ion-button>

      <!--      <ion-button *ngIf="producto.BP == true" (click)="openPrecio(producto.id, producto)" >Buscar mejor precio</ion-button>-->

      <ion-card-title style="padding-top: 10px">Presentaciones:</ion-card-title>
<!--      <ion-slides pager="true"  [options]="slideOpts">-->

<!--        <ion-slide  *ngFor="let producto of productos">-->
<!--          <ion-card style="margin-bottom: 50px;   box-shadow: none !important;"  class="ion-no-margin">-->
<!--            <ion-card-header>-->
<!--              <img style=" max-height: 300px; margin: auto" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"/>-->
<!--              <ion-card-title>4 Tabletas</ion-card-title>-->
<!--              <ion-card-subtitle>Caja de cartón con 4 </ion-card-subtitle>-->
<!--            </ion-card-header>-->
<!--            <ion-card-content class="ion-no-margin ion-no-padding">-->
<!--              &lt;!&ndash;          {{presentacion.Descripcion}}<br>&ndash;&gt;-->
<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>SKUDEMO</p>-->
<!--              &lt;!&ndash;            <span *ngIf="presentacion.Descripcion" class="text-primary"><ion-icon name="reader"></ion-icon> Descripción: </span><br>&ndash;&gt;-->
<!--              &lt;!&ndash;            <p class="ion-text-justify ion-no-padding">{{presentacion.Descripcion}}</p>&ndash;&gt;-->

<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon>Registro Sanitario:</span> R3egisto sanitario</p>-->
<!--              <p><span class="text-primary">Titulo Adicional</span> Presentacion adicional</p>-->

<!--              &lt;!&ndash;          <ion-button (click)="abrirSitiodeCompra(presentacion.url, '_system')">ir al sitio de la tienda <ion-icon name="exit"></ion-icon></ion-button>&ndash;&gt;-->

<!--            </ion-card-content>-->
<!--          </ion-card>-->
<!--        </ion-slide>-->
<!--        <ion-slide  *ngFor="let producto of productos">-->
<!--          <ion-card style="margin-bottom: 50px;   box-shadow: none !important;"  class="ion-no-margin">-->
<!--            <ion-card-header>-->
<!--              <img style=" max-height: 300px; margin: auto" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"/>-->
<!--              <ion-card-title>10 Tabletas</ion-card-title>-->
<!--              <ion-card-subtitle>Caja de cartón con 10 </ion-card-subtitle>-->
<!--            </ion-card-header>-->
<!--            <ion-card-content class="ion-no-margin ion-no-padding">-->
<!--              &lt;!&ndash;          {{presentacion.Descripcion}}<br>&ndash;&gt;-->
<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>SKUDEMO</p>-->
<!--              &lt;!&ndash;            <span *ngIf="presentacion.Descripcion" class="text-primary"><ion-icon name="reader"></ion-icon> Descripción: </span><br>&ndash;&gt;-->
<!--              &lt;!&ndash;            <p class="ion-text-justify ion-no-padding">{{presentacion.Descripcion}}</p>&ndash;&gt;-->

<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon>Registro Sanitario:</span> R3egisto sanitario</p>-->
<!--              <p><span class="text-primary">Titulo Adicional</span> Presentacion adicional</p>-->

<!--              &lt;!&ndash;          <ion-button (click)="abrirSitiodeCompra(presentacion.url, '_system')">ir al sitio de la tienda <ion-icon name="exit"></ion-icon></ion-button>&ndash;&gt;-->

<!--            </ion-card-content>-->
<!--          </ion-card>-->
<!--        </ion-slide>-->
<!--        <ion-slide  *ngFor="let producto of productos">-->
<!--          <ion-card style="margin-bottom: 50px;   box-shadow: none !important;"  class="ion-no-margin">-->
<!--            <ion-card-header>-->
<!--              <img style=" max-height: 300px; margin: auto" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"/>-->
<!--              <ion-card-title>30 Tabletas</ion-card-title>-->
<!--              <ion-card-subtitle>Caja de cartón con 30 </ion-card-subtitle>-->
<!--            </ion-card-header>-->
<!--            <ion-card-content class="ion-no-margin ion-no-padding">-->
<!--              &lt;!&ndash;          {{presentacion.Descripcion}}<br>&ndash;&gt;-->
<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>SKUDEMO</p>-->
<!--              &lt;!&ndash;            <span *ngIf="presentacion.Descripcion" class="text-primary"><ion-icon name="reader"></ion-icon> Descripción: </span><br>&ndash;&gt;-->
<!--              &lt;!&ndash;            <p class="ion-text-justify ion-no-padding">{{presentacion.Descripcion}}</p>&ndash;&gt;-->

<!--              <p><span class="text-primary"><ion-icon name="barcode"></ion-icon>Registro Sanitario:</span> R3egisto sanitario</p>-->
<!--              <p><span class="text-primary">Titulo Adicional</span> Presentacion adicional</p>-->

<!--              &lt;!&ndash;          <ion-button (click)="abrirSitiodeCompra(presentacion.url, '_system')">ir al sitio de la tienda <ion-icon name="exit"></ion-icon></ion-button>&ndash;&gt;-->

<!--            </ion-card-content>-->
<!--          </ion-card>-->
<!--        </ion-slide>-->


<!--      </ion-slides>-->

<!--      <ion-button color="tertiary" expand="block"  *ngIf="producto.Promociones[0]" (click)="promociones()" >Promociones</ion-button>-->

    </ion-card-content>


  </ion-card>
<!--  <ion-card class="ion-padding">-->
<!--    <ion-card-title class="ion-text-center">Presentaciones</ion-card-title>-->


<!--    <ion-slides pager="true"  [options]="slideOpts">-->

<!--      <ion-slide  *ngFor="let producto of productos">-->
<!--        <ion-card style="margin-bottom: 50px;   box-shadow: none !important;"  class="ion-no-margin">-->
<!--          <ion-card-header>-->
<!--            <img style=" max-height: 300px; margin: auto" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"/>-->
<!--            <ion-card-title>10 Tabletas</ion-card-title>-->
<!--            <ion-card-subtitle>Pruebna </ion-card-subtitle>-->
<!--          </ion-card-header>-->
<!--          <ion-card-content class="ion-no-margin ion-no-padding">-->
<!--            &lt;!&ndash;          {{presentacion.Descripcion}}<br>&ndash;&gt;-->
<!--            <p><span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>SKUDEMO</p>-->
<!--            &lt;!&ndash;            <span *ngIf="presentacion.Descripcion" class="text-primary"><ion-icon name="reader"></ion-icon> Descripción: </span><br>&ndash;&gt;-->
<!--            &lt;!&ndash;            <p class="ion-text-justify ion-no-padding">{{presentacion.Descripcion}}</p>&ndash;&gt;-->

<!--            <p><span class="text-primary"><ion-icon name="barcode"></ion-icon>Registro Sanitario:</span> R3egisto sanitario</p>-->
<!--            <p><span class="text-primary">Titulo Adicional</span> Presentacion adicional</p>-->

<!--            &lt;!&ndash;          <ion-button (click)="abrirSitiodeCompra(presentacion.url, '_system')">ir al sitio de la tienda <ion-icon name="exit"></ion-icon></ion-button>&ndash;&gt;-->

<!--          </ion-card-content>-->
<!--        </ion-card>-->
<!--      </ion-slide>-->


<!--    </ion-slides>-->
<!--  </ion-card>-->


<!--  <ion-card *ngFor="let adicional of adicionales">-->
<!--    <ion-card-header>-->
<!--      <ion-img *ngIf="adicional.Imagen" style="height: 250px!important;" src="{{strapiUrl}}{{adicional.Imagen.url}}"></ion-img>-->

<!--      <ion-card-title *ngIf="adicional.Titulo">-->
<!--        {{adicional.Titulo}}-->
<!--      </ion-card-title>-->
<!--    </ion-card-header>-->
<!--    <ion-card-content *ngIf="adicional.Informacion">-->
<!--      {{adicional.Informacion}}<br><br>-->
<!--      <ion-button *ngIf="adicional.NombreBoton" (click)="abrirSitiodeCompra(adicional.LinkBoton)" > {{adicional.NombreBoton}}</ion-button>-->
<!--    </ion-card-content>-->

<!--  </ion-card>-->


  <ion-modal trigger="open-modal"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>
        <ion-toolbar color="secondary" class="ion-text-center">
          Farmacocinética y farmacodinamia
          <ion-buttons slot="end">
            <ion-button (click)="cancel()">
              <ion-icon name="close"></ion-icon>
            </ion-button>
          </ion-buttons>
          <!--              <ion-buttons slot="start">-->
          <!--                <ion-button (click)="cancel()">Cancel</ion-button>-->
          <!--              </ion-buttons>-->
          <!--              <ion-title>Welcome</ion-title>-->
          <!--              <ion-buttons slot="end">-->
          <!--                <ion-button (click)="confirm()" [strong]="true">Confirm</ion-button>-->
          <!--              </ion-buttons>-->
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        La metadoxina es el ion par entre la piridoxina y el ácido pirrolidoncarboxílico. Las sales del ácido piroglutámico o pirrolidona carboxilatos son fácilmente hidrolizadas en el organismo tornando el ácido cíclico disponible para
        procesos metabólicos.
        La metadoxina se encuentra fisiológicamente presente en varios tejidos del organismo, incluyendo el tejido nervioso


        y se obtiene de la dieta o a partir de la ciclización del ácido glutámico.

        La metadoxina antagoniza la peroxidación lipídica en las células hepáticas, restaurando el daño hepático resultante de la ingesta prolongada del alcohol y reduce el hígado graso en la hepatitis crónica.
        Además, la metadoxina actúa específicamente sobre los sistemas neurotransmisores involucrados en la intoxicación alcohólica, incrementando la liberación del GABA y de la acetilcolina.
        La metadoxina mejora el metabolismo del alcohol, reduciendo los niveles plasmáticos de etanol durante la ingesta de alcohol y mejora las alteraciones cognoscitivas, así como los principales síntomas psicológicos (agresividad, agitación, estado de ánimo y alteraciones de la conducta) debido al abuso ocasional o prolongado del alcohol. ABRIXONE® reduce el tiempo de permanencia del alcohol en el organismo, disminuyendo así los efectos tóxicos sobre las células.
        500 mg

        La metadoxina también protege la estructura y función de la célula actuando sobre aquellos procesos bioquímicos que intervienen en el mantenimiento óptimo celular. ABRIXONE®, al disminuir el tiempo de desintoxicación en la intoxicación aguda con alcohol, previene y reduce las consecuencias hepáticas y neuropsíquicas de la ingesta
        habitual del alcohol.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal1"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Indicaciones terapeuticas
            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        <div [innerHTML]="productoM"></div>
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>


  <ion-modal trigger="open-modal2"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Contraindicaciones

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        La metadoxina está contraindicada en aquellos pacientes que presentan
        hipersensibilidad a este fármaco o a cualquiera de los componentes de la fórmula.
        La metadoxina no deberá administrarse durante el embarazo y la lactancia.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal3"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Precauciones Generales

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        Se deberá tener precaución cuando se consuman por periodos prolongados la combinación de metadoxina con levodopa y carvidopa u
        otro metabolito de la dopamina, pues puede verse alterada la concentración plasmática de los metabolitos de la dopamina.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal4"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Restricciones de uso durante el embarazo

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        A la fecha, no se tienen reportes de que la metadoxina provoque alguna alteración durante el embarazo y/o la lactancia; sin embargo, como cualquier
        otro fármaco, su uso durante el embarazo quedará a criterio del médico, valorando siempre los posibles riesgos contra los beneficios a obtener.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal5"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Reacciones secundarias Adversas

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        A la fecha, no se han descrito reacciones secundarias y adversas con el uso de ABRIXONE tabletas; sin embargo, ocasionalmente pueden presentarse eventos adversos no específicos, como los que pueden ocurrir con cualquier medicamento (como trastornos gastrointestinales, rash cutáneo).
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal6"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Interacciones medicamentosas y de otro genero

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        En los enfermos de Parkinson tratados con L-dopa, la metadoxina puede antagonizar el efecto de ese fármaco.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal7"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Alteraciones en los resultados de pruebas de laboratorio

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        A la fecha no se han reportado alteraciones en las pruebas de laboratorio en pacientes tratados con
        ® ABRIXONE tabletas.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal8"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Precauciones en relación con efectos de carcinogénesis y sobre la fertilidad

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        Al momento actual, no se han reportado efectos de carcinogénesis, mutagénesis, teratogénesis y de toxicología reproductiva con el uso de la metadoxina.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>

  <ion-modal trigger="open-modal9"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">
            Dosis y vía de administracion

            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        La vía de administración de ABRIXONE® tabletas es oral.
        En la intoxicación alcohólica aguda ABRIXONE® se administra a razón de una tableta de 500 mg cada 12 horas durante 3 días.
        En la intoxicación alcohólica crónica en donde hay hepatopatías degenerativas como hígado graso o cirrosis, la administración será de una tableta cada 12 horas durante 30 días como mínimo, pudiendo prolongarse este esquema hasta por 90 días en función del estado del paciente.
        En la interrupción brusca del alcohol, se administra una tableta de 500 mg cada 12 horas durante un año. En estos casos, el médico podrá ordenar la suspensión del tratamiento antes de este periodo y cuando considere que el paciente ya no necesita más metadoxina.
        <br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal trigger="open-modal10"
             (willDismiss)="onWillDismiss($event)"
             [initialBreakpoint]="0.85"
             [breakpoints]="[0]"
             handleBehavior="cycle"
  >
    <ng-template>
      <ion-header>

        <ion-header>
          <ion-toolbar color="secondary" class="ion-text-center">

            Manifestaciones y manejo de la sobredosificación o ingesta accidental
            <ion-buttons slot="end">
              <ion-button (click)="cancel()">
                <ion-icon name="close"></ion-icon>
              </ion-button>
            </ion-buttons>

          </ion-toolbar>
        </ion-header>
      </ion-header>
      <ion-content class="ion-padding">
        A la fecha, no se han reportado casos de sobredosificación con metadoxina; sin embargo, en caso de que ocurriera, se deberá monitorear estrechamente al paciente hasta su recuperación total.<br>
        <br><br><br><br><br><br><br><br><br><br><br><br>
      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>

