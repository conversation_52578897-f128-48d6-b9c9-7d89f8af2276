import {Component, OnInit, ViewChild} from '@angular/core';
import {environment} from '../../../environments/environment';
import {RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {StrapiService} from '../../services/strapi.service';
import {ActivatedRoute, Router} from '@angular/router';
import {IonModal, ModalController, Platform} from '@ionic/angular';
import {FirebaseAuthService} from '../../services/firebase-auth.service';
// import {File} from '@ionic-native/file/ngx';
import {WidgetUtilService} from '../../services/widget-util.service';
// import {FirebaseAnalyticsService} from '../../services/firebase-analitycs.service';
// import {Storage} from '@capacitor/storage';
import {VademecumPromocionesPage} from '../vademecum-promociones/vademecum-promociones.page';
import {VademecumCompartirPage} from '../vademecum-compartir/vademecum-compartir.page';
import {Browser} from '@capacitor/browser';
// import * as JsBarcode from 'jsbarcode';
import {Directory, Filesystem} from '@capacitor/filesystem';
import {Share} from '@capacitor/share';

import {marked} from "marked";
import { OverlayEventDetail } from '@ionic/core/components';

// import pdfMake from 'pdfmake/build/pdfmake';
// import pdfFonts from 'pdfmake/build/vfs_fonts';
import {Preferences} from "@capacitor/preferences";
import * as pdfMake from 'pdfmake/build/pdfmake';




@Component({
  selector: 'app-vademecum-producto-new',
  templateUrl: './vademecum-producto-new.page.html',
  styleUrls: ['./vademecum-producto-new.page.scss'],
})
export class VademecumProductoNewPage implements OnInit {
  @ViewChild(IonModal) modal: IonModal;

  message = 'This modal example uses triggers to automatically open a modal when the button is clicked.';
  name: string;


  id = '';
  strapiUrl = environment.strapiURL;
  productos: RespuestaStrapi[] = [];
  productoM: any;
  presnetacion: RespuestaStrapi[] = [];
  presentaciones: RespuestaStrapi[] = [];
  adicionales: RespuestaStrapi[] = [];

  codigoB64: any;
  imagen: any;
  imgenProductoBase64: any;
  informacion: any;
  pdfObj = null;


  isLoggedIn = false;


  img3d = false;

  clase: any;

  coords: string;

  slideOpts = {
    slidesPerView: 1,
    slidesPerGroup: 1,
    slidesPerColumn: 1,
    centeredSlides: true,


    pager: true,
    coverflowEffect: {
      rotate: 50,
      stretch: 0,
      depth: 100,
      modifier: 1,
      slideShadows: true,
    },

    autoplay: true
  };

  constructor(
      private strapi: StrapiService,
      private activatedRoute: ActivatedRoute,
      private router: Router,
      private modalCtrl: ModalController,
      private firebaseAuthService: FirebaseAuthService,
      private plt: Platform,
      // private file: File,
      private widgetUtilService: WidgetUtilService,
      // private fbas: FirebaseAnalyticsService
  ) {

    this.getAuthState();

    this.activatedRoute.params.subscribe((result: any) => {
      // console.log('resultado de id', result);
      this.id = result.id;
    });

  }




  async ngOnInit() {


    const vret = await Preferences.get({ key: 'vademecum' });
    const vuser = JSON.parse(vret.value);
    const idN = Number(this.id);
    console.log(idN);
    const producto = vuser.find(e => e.id === idN);

    console.log(vuser);
    console.log(this.id);
    console.log(producto);
    this.productos = [producto];
    this.productoM = marked(producto.Descripcion);

    this.presentaciones = producto.Presentacion;
    this.adicionales = producto.Adicional;

    /*
        this.strapi.getContenido(`medicamentos/${this.id}`).subscribe(resp => {


          this.productoM = marked(resp.Descripcion);
          this.presentaciones = resp.Presentacion;
          this.presnetacion = resp.Presentacion.Nombre;
          this.adicionales = resp.Adicional;


          // console.log('Producto id', this.productos);


        });
      */
  }






  openPrecio(id, contenido) {

    // this.strapi.setData(id, contenido);
    this.router.navigateByUrl(`/vademecum-producto-precio/${id}`);

    // this.router.navigate(['/vademecum-producto-precio', ProductoID]);
  }

  async promociones() {
    const modal  = await this.modalCtrl.create({
      component: VademecumPromocionesPage,
      cssClass: 'bannerVertical',
      componentProps: {
        Info: this.productos
      }
    });

    await modal.present();

  }
  async compartir() {
    const modal  = await this.modalCtrl.create({
      component: VademecumCompartirPage,
      cssClass: 'bannerVertical',

      componentProps: {
        Info: this.productos
      }
    });

    await modal.present();

  }
  getAuthState() {

    this.firebaseAuthService.getAuthState().subscribe(user => {
      // console.log('User auth state', user ? user.toJSON(): null);
      if (user) {
        this.isLoggedIn = true;
      } else {
        this.isLoggedIn = false;

      }


    });

  }


  async abrirSitiodeCompra(url: string) {
    await Browser.open({ url: `${url}` });

  }



  convertToDataURLviaCanvas(url, outputFormat) {
    return new Promise( (resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = function() {
        let canvas = <HTMLCanvasElement> document.createElement('CANVAS'),
            ctx = canvas.getContext('2d'),
            dataURL;
        canvas.height = img.height;
        canvas.width = img.width;
        ctx.drawImage(img, 0, 0);
        dataURL = canvas.toDataURL(outputFormat);
        canvas = null;
        resolve(dataURL);
      };
      img.src = url;
    });
  }


  async descargarPDF() {

    const vret = await Preferences.get({ key: 'vademecum' });
    const vuser = JSON.parse(vret.value);
    const idN = Number(this.id);
    console.log(idN);
    const producto = vuser.find(e => e.id === idN);

    console.log(vuser);
    console.log(this.id);
    console.log(producto);
    const imagen = this.strapiUrl + producto.Imagen_de_producto[0].url;

    const canvas = document.createElement('canvas');
    // JsBarcode(canvas, `${producto.SKU}`, {
    //   format: 'ean13',
    //   height: 40,
    //   displayValue: true,
    //   textMargin: 0});
    this.codigoB64 = canvas.toDataURL('image/jpeg');
    this.convertToDataURLviaCanvas(imagen, 'image/jpeg')
        .then( data => {
          const ipb64 = data;
          this.imgenProductoBase64 = ipb64;
          const docDefinition = {
            content: [
              {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 202.4 65.83">...</svg>',
                width: 120,
                alignment: 'center' as 'center', // Asegúrate de que este valor sea uno de los valores esperados
                margin: [0, 30, 0, 0]
              },
              {
                image: `${this.imgenProductoBase64}`,
                width: 250,
                alignment: 'center' as 'center'
              },
              {
                text: `${producto.PrincipioActivo}`,
                style: 'activo',
                alignment: 'center' as 'center'
              },
              {
                text: `Acción terapeutica:`,
                style: 'contenido',
                alignment: 'center' as 'center'
              },
              {
                text: `${producto.Descripcion}`,
                style: 'contenido',
                alignment: 'center' as 'center'
              },
              {
                text: `${producto.Nombre}`,
                style: 'header',
                alignment: 'center' as 'center'
              },
              {
                layout: 'lightHorizontalLines',
                table: {
                  headerRows: 1,
                  widths: ['*', 'auto'],
                  body: [
                    ['', ''],
                    [{ text: 'Denominación genérica:', bold: true }, `${producto.PrincipioActivo}`],
                    [{ text: 'Acción terapeutica:', bold: true }, `${producto.Descripcion}`],
                    [{ text: 'Concentración:', bold: true }, `${producto.Concentracion}`],
                    [{ text: 'Contenido:', bold: true }, `${producto.Contenido}`],
                    [{ text: 'Unidad de negocio:', bold: true }, `${producto.lineaNegocio}`],
                    [{ text: 'Registro Sanitario:', bold: true }, `${producto.Registro_sanitario}`],
                    [{ text: 'EAN:', bold: true }, `${producto.SKU}`],
                    [{ text: 'Laboratorio:', bold: true }, `${producto.Laboratorio}`],
                  ]
                }
              },
              {
                image: `${this.codigoB64}`,
                width: 120,
                alignment: 'center' as 'center'
              },
              {
                text: `Muestra este código en farmacia`,
                style: 'mostrar',
                alignment: 'center' as 'center'
              }
            ],
            styles: {
              header: {
                fontSize: 18,
                bold: true,
                color: '#f32d36',
              },
              mostrar: {
                fontSize: 8,
                bold: true,
                color: '#f32d36',
              },
              activo: {
                fontSize: 10,
                bold: true,
                color: '#f32d36',
              },
              contenido: {
                fontSize: 8,
                bold: true,
                color: '#4a4f54',
              },
              subheader: {
                fontSize: 14,
                bold: true,
                margin: [0, 15, 0, 0]
              },
              story: {
                italic: true,
                alignment: 'center' as 'center',
                width: '50%',
              },
              content: {
                background: '#ffffff'
              }
            }
          };
          // @ts-ignore
          this.pdfObj = pdfMake.createPdf(docDefinition);


          if (this.plt.is('cordova') || this.plt.is('capacitor')) {
            this.pdfObj.getBuffer((buffer) => {

              const blob = new Blob([buffer], { type: 'application/pdf' });

              if (this.plt.is('android')) {
                this.pdfObj.getBase64((pdfbase64) => {
                  Filesystem.writeFile({
                    path: `${producto.Nombre}_${producto.SKU}.pdf`,
                    data: pdfbase64,
                    directory: Directory.Documents,

                    // encoding: FilesystemEncoding.UTF8
                  }).then(writeFileResponse => {
                    console.log('writeFile success => ', writeFileResponse);
                    Share.share({
                      title: `${producto.Nombre}_${producto.SKU}.pdf`,
                      url: writeFileResponse.uri,
                    }).then(resShare => {
                      this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre}`);

                    });
                    this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre}`);
                  }, error => {
                    console.log('writeFile error => ', error);
                    this.widgetUtilService.presentToastError(error);
                  });
                });
              }
              // if (this.plt.is('ios')) {
              //
              //   // Save the PDF to the data Directory of our App
              //   this.file.writeFile(this.file.dataDirectory, `${producto.Nombre}_${producto.SKU}.pdf`,
              //       blob, { replace: true }).then(fileEntry => {
              //     // Open the PDf with the correct OS tools
              //     Share.share({
              //       title: `${producto.Nombre}_${producto.SKU}.pdf`,
              //       url: fileEntry.nativeURL,
              //     }).then(resShare => {
              //       this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre} `);
              //       // this.fbas.logEventParam('producto_pdf_descargado', 'product_name', producto.Nombre);
              //
              //     });
              //   });
              // }
            });
          } else {

            // On a browser simply use download!
            this.pdfObj.download();
          }
        });


  }
  cancel() {
    this.modal.dismiss(null, 'cancel');
  }

  confirm() {
    this.modal.dismiss(this.name, 'confirm');
  }

  onWillDismiss(event: Event) {
    const ev = event as CustomEvent<OverlayEventDetail<string>>;
    if (ev.detail.role === 'confirm') {
      this.message = `Hello, ${ev.detail.data}!`;
    }
  }

  protected readonly close = close;
}
