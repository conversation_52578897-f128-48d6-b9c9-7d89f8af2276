import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { VademecumProductoPage } from './vademecum-producto.page';
// import {PipesModule} from '../../pipes/pipes.module';
import {NgPipesModule} from 'ngx-pipes';
// import {VademecumPromocionesPage} from '../vademecum-promociones/vademecum-promociones.page';
// import {VademecumPromocionesPageModule} from '../vademecum-promociones/vademecum-promociones.module';
// import {VademecumCompartirPageModule} from '../vademecum-compartir/vademecum-compartir.module';
// import {VademecumCompartirPage} from '../vademecum-compartir/vademecum-compartir.page';


const routes: Routes = [
  {
    path: '',
    component: VademecumProductoPage
  }
];

@NgModule({
  // entryComponents: [
  //   // VademecumPromocionesPage,
  //     VademecumCompartirPage
  // ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),

    // PipesModule,
      NgPipesModule,
      // VademecumPromocionesPageModule,
      // VademecumCompartirPageModule

  ],
  declarations: [VademecumProductoPage]
})
export class VademecumProductoPageModule {}
