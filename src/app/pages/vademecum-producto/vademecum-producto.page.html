<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let producto of productos">{{producto.Nombre}}</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/vademecum"></ion-back-button>
    </ion-buttons>
    <ion-icon slot="end" name="download" size="large" (click)="descargarPDF()"></ion-icon>

  </ion-toolbar>
</ion-header>

<ion-content *ngIf="isLoggedIn">

  <ion-card *ngFor="let producto of productos">
    <ion-img class="centrar_imagen" style="max-width: 400px!important" *ngIf="producto.Imagen_de_producto[0]" src="{{strapiUrl}}{{producto.Imagen_de_producto[0].url}}"></ion-img>
    <ion-card-header>
      <ion-card-title>{{producto.Nombre}}</ion-card-title>
      <ion-card-subtitle><span class="text-primary"><ion-icon name="book"></ion-icon> Registro sanitario:</span> {{producto.Registro_sanitario}}</ion-card-subtitle>
      <ion-button (click)="compartir()" expand="block" >Compartir {{producto.Nombre}} con el paciente</ion-button>
    </ion-card-header>

    <ion-card-content>
      <span class="text-primary"><ion-icon name="water"></ion-icon> Denominación genérica: </span>{{producto.PrincipioActivo}}<br><span class="text-primary"><ion-icon name="flask"></ion-icon> Concentración: </span> {{producto.Concentracion}}<br>
      <br><span class="text-primary"> <ion-icon name="medkit"></ion-icon>Acción terapeutica:</span>
      <br> <div [innerHTML]="productoM"></div><br>
      <br><span class="text-primary"><ion-icon name="ribbon"></ion-icon>Laboratorio:</span> {{producto.Laboratorio}}<br>
      <span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>{{producto.SKU}}<br>

<!--      <ion-button expand="block"  *ngIf="producto.Promociones[0]" (click)="promociones()" >Promociones</ion-button>-->
<!--      <ion-button *ngIf="producto.BP == true" (click)="openPrecio(producto.id, producto)" >Buscar mejor precio</ion-button>-->
    </ion-card-content>


  </ion-card>
  <ion-card *ngIf="presentaciones[0]" class="ion-padding">
    <ion-card-title class="ion-text-center">Presentaciones</ion-card-title>



<!--    Remodelar presentaciones-->
<!--    <ion-slides pager="true"  [options]="slideOpts">-->

<!--      <ion-slide  *ngFor="let presentacion of presentaciones">-->
<!--        <ion-card style="margin-bottom: 50px;   box-shadow: none !important;"  class="ion-no-margin">-->
<!--          <ion-card-header>-->
<!--            <img style=" max-height: 300px; margin: auto" src="{{strapiUrl}}{{presentacion.Imagen_de_producto.url}}"/>-->
<!--            <ion-card-title>{{presentacion.Nombre}}</ion-card-title>-->
<!--            <ion-card-subtitle>{{presentacion.Presentacion}}</ion-card-subtitle>-->
<!--          </ion-card-header>-->
<!--          <ion-card-content class="ion-no-margin ion-no-padding">-->
<!--            &lt;!&ndash;          {{presentacion.Descripcion}}<br>&ndash;&gt;-->
<!--            <p><span class="text-primary"><ion-icon name="barcode"></ion-icon> EAN: </span>{{presentacion.SKU}}</p>-->
<!--&lt;!&ndash;            <span *ngIf="presentacion.Descripcion" class="text-primary"><ion-icon name="reader"></ion-icon> Descripción: </span><br>&ndash;&gt;-->
<!--&lt;!&ndash;            <p class="ion-text-justify ion-no-padding">{{presentacion.Descripcion}}</p>&ndash;&gt;-->

<!--            <p *ngIf="presentacion.Registro_sanitario"><span class="text-primary"><ion-icon name="barcode"></ion-icon>Registro Sanitario:</span> {{presentacion.Registro_sanitario}}</p>-->
<!--            <p *ngIf="presentacion.adicional"><span class="text-primary">{{presentacion.tituloAdicional}}</span> {{presentacion.adicional}}</p>-->

<!--            &lt;!&ndash;          <ion-button (click)="abrirSitiodeCompra(presentacion.url, '_system')">ir al sitio de la tienda <ion-icon name="exit"></ion-icon></ion-button>&ndash;&gt;-->

<!--          </ion-card-content>-->
<!--        </ion-card>-->
<!--      </ion-slide>-->

<!--    </ion-slides>-->
  </ion-card>


  <ion-card *ngFor="let adicional of adicionales">
    <ion-card-header>
      <ion-img *ngIf="adicional.Imagen" style="height: 250px!important;" src="{{strapiUrl}}{{adicional.Imagen.url}}"></ion-img>

      <ion-card-title *ngIf="adicional.Titulo">
        {{adicional.Titulo}}
      </ion-card-title>
    </ion-card-header>
    <ion-card-content *ngIf="adicional.Informacion">
      {{adicional.Informacion}}<br><br>
      <ion-button *ngIf="adicional.NombreBoton" (click)="abrirSitiodeCompra(adicional.LinkBoton)" > {{adicional.NombreBoton}}</ion-button>
    </ion-card-content>

  </ion-card>


</ion-content>
