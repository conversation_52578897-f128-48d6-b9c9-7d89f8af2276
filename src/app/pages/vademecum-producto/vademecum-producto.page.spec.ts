import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VademecumProductoPage } from './vademecum-producto.page';

describe('VademecumProductoPage', () => {
  let component: VademecumProductoPage;
  let fixture: ComponentFixture<VademecumProductoPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VademecumProductoPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VademecumProductoPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
