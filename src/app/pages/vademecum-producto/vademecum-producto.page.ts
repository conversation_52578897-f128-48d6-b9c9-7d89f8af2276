import { Component, OnInit } from '@angular/core';
import {environment} from '../../../environments/environment';
import {RespuestaStrapi} from '../../interfaces/strapiInterfaces';
import {StrapiService} from '../../services/strapi.service';
import {ActivatedRoute, Router} from '@angular/router';
import {ModalController, Platform} from '@ionic/angular';
// import {VademecumPromocionesPage} from '../vademecum-promociones/vademecum-promociones.page';
import {VademecumCompartirPage} from '../vademecum-compartir/vademecum-compartir.page';
import {FirebaseAuthService} from '../../services/firebase-auth.service';
// import * as marked from 'marked';
import { marked } from 'marked';

// import {FilesystemDirectory, Plugins} from '@capacitor/core';
// import * as JsBarcode from 'jsbarcode';
// const { Filesystem, Browser, Storage, Share} = Plugins;

// Actualizacion de Capacitor a 3.0
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
import { Browser } from '@capacitor/browser';
import { Share } from '@capacitor/share';


import * as pdfMake from 'pdfmake/build/pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts';
//
// function configurePdfMake() {
//   (pdfMake as any).vfs = pdfFonts.pdfMake.vfs;
// }
//

// import { File } from '@ionic-native/file/ngx';
// import {toBase64String} from '@angular/compiler/src/output/source_map';
import {WidgetUtilService} from '../../services/widget-util.service';
// import {FirebaseAnalyticsService} from '../../services/firebase-analitycs.service';
import {FirebaseAuthentication} from "@capacitor-firebase/authentication";

@Component({
  selector: 'app-vademecum-producto',
  templateUrl: './vademecum-producto.page.html',
  styleUrls: ['./vademecum-producto.page.scss'],
})
export class VademecumProductoPage implements OnInit {
  id = '';
  strapiUrl = environment.strapiURL;
  productos: RespuestaStrapi[] = [];
  productoM: any;
  presnetacion: any = [];
  presentaciones: any = [];
  adicionales: any = [];

  codigoB64: any;
  imagen: any;
  imgenProductoBase64: any;
  informacion: any;
  pdfObj: any = null;


  isLoggedIn = false;


  clase: any;

  coords: string = '';

  slideOpts = {
    slidesPerView: 1,
    slidesPerGroup: 1,
    slidesPerColumn: 1,
    centeredSlides: true,


    pager: true,
    coverflowEffect: {
      rotate: 50,
      stretch: 0,
      depth: 100,
      modifier: 1,
      slideShadows: true,
    },

    autoplay: true
  };

  constructor(
      private strapi: StrapiService,
      private activatedRoute: ActivatedRoute,
      private router: Router,
      private modalCtrl: ModalController,
      private firebaseAuthService: FirebaseAuthService,
      private plt: Platform,
      private file: File,
      private widgetUtilService: WidgetUtilService,
      // private fbas: FirebaseAnalyticsService
  ) {


    this.getAuthState();

    this.activatedRoute.params.subscribe((result: any) => {
      console.log('resultado de id', result);
      this.id = result.id;
    });

    // configurePdfMake();
  }





  async ngOnInit() {


    const vret: any = await Preferences.get({ key: 'vademecum' });
    const vuser = JSON.parse(vret.value);
    const idN = Number(this.id);
    console.log(idN);
    const producto = vuser.find((e: any) => e.id === idN);

    console.log(vuser);
    console.log(this.id);
    console.log(producto);
    this.productos = [producto];
    this.productoM = marked(producto.Descripcion);

    this.presentaciones = producto.Presentacion;
    this.adicionales = producto.Adicional;

/*
    this.strapi.getContenido(`medicamentos/${this.id}`).subscribe(resp => {


      this.productoM = marked(resp.Descripcion);
      this.presentaciones = resp.Presentacion;
      this.presnetacion = resp.Presentacion.Nombre;
      this.adicionales = resp.Adicional;


      // console.log('Producto id', this.productos);


    });
  */
  }






  // openPrecio(id, contenido) {
  //
  //   this.strapi.setData(id, contenido);
  //   this.router.navigateByUrl(`/vademecum-producto-precio/${id}`);
  //
  //   // this.router.navigate(['/vademecum-producto-precio', ProductoID]);
  // }

  // async promociones() {
  //   const modal  = await this.modalCtrl.create({
  //     component: VademecumPromocionesPage,
  //     cssClass: 'bannerVertical',
  //     componentProps: {
  //       Info: this.productos
  //     }
  //   });
  //
  //   await modal.present();
  //
  // }
  async compartir() {
    const modal  = await this.modalCtrl.create({
      component: VademecumCompartirPage,
      cssClass: 'bannerVertical',

      componentProps: {
        Info: this.productos
      }
    });

    await modal.present();

  }
  // getAuthState() {
  //
  //   this.firebaseAuthService.getAuthState().subscribe(user => {
  //     // console.log('User auth state', user ? user.toJSON(): null);
  //     if (user) {
  //       this.isLoggedIn = true;
  //     } else {
  //       this.isLoggedIn = false;
  //
  //     }
  //
  //
  //   });
  //
  // }

  async getAuthState() {
    try {
      const checkAuthState = async () => {
        const result = await FirebaseAuthentication.getCurrentUser();
        const user = result.user;

        if (user) {
          this.isLoggedIn = true;
        } else {
          this.isLoggedIn = false;

        }
      };

      // Call the checkAuthState function based on your app's logic
      // For example, on app start, after certain user actions, etc.
      checkAuthState();
    } catch (error) {
      console.error('Error fetching auth state:', error);
      // Implement your error handling logic
    }
  }


  async abrirSitiodeCompra(url: string) {
    await Browser.open({ url: `${url}` });

  }



  // convertToDataURLviaCanvas(url: string, outputFormat: string) {
  //   return new Promise( (resolve, reject) => {
  //     const img = new Image();
  //     img.crossOrigin = 'Anonymous';
  //     img.onload = function() {
  //       let canvas = <HTMLCanvasElement> document.createElement('CANVAS'),
  //           ctx = canvas.getContext('2d'),
  //           dataURL;
  //       canvas.height = img.height;
  //       canvas.width = img.width;
  //       ctx.drawImage(img, 0, 0);
  //       dataURL = canvas.toDataURL(outputFormat);
  //       canvas = null;
  //       resolve(dataURL);
  //     };
  //     img.src = url;
  //   });
  // }

  convertToDataURLviaCanvas(url: string, outputFormat: any): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = function() {
        let canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject('Could not create canvas context');
          return;
        }

        canvas.height = img.height;
        canvas.width = img.width;
        ctx.drawImage(img, 0, 0);
        const dataURL = canvas.toDataURL(outputFormat);

        resolve(dataURL);
      };

      img.onerror = function() {
        reject('Image could not be loaded');
      };

      img.src = url;
    });
  }


  async descargarPDF() {

    const vret: any = await Preferences.get({ key: 'vademecum' });
    const vuser = JSON.parse(vret.value);
    const idN = Number(this.id);
    console.log(idN);
    const producto = vuser.find((e: any) => e.id === idN);

    console.log(vuser);
    console.log(this.id);
    console.log(producto);
    const imagen = this.strapiUrl + producto.Imagen_de_producto[0].url;

    const canvas = document.createElement('canvas');
    // JsBarcode(canvas, `${producto.SKU}`, {
    //   format: 'ean13',
    //   height: 40,
    //   displayValue: true,
    //   textMargin: 0});
    this.codigoB64 = canvas.toDataURL('image/jpeg');
    this.convertToDataURLviaCanvas(imagen, 'image/jpeg')
        .then( data => {
          const ipb64 = data;
          this.imgenProductoBase64 = ipb64;
          const docDefinition: any = {
            content: [

              {
                svg: '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 202.4 65.83"><defs><style>.cls-1{fill:url(#radial-gradient);}.cls-2{fill:url(#Degradado_sin_nombre_10);}.cls-3{fill:#fff;}.cls-4{fill:#4a4f54;}.cls-5{fill:#8d2c13;}.cls-6{isolation:isolate;}.cls-7{clip-path:url(#clip-path);}.cls-8{fill:url(#radial-gradient-4);}.cls-10,.cls-9{fill:#ea0029;}.cls-9{fill-rule:evenodd;}</style><radialGradient id="radial-gradient" cx="26.89" cy="40.39" r="0.32" gradientTransform="translate(-1.52)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#63c3dd"/><stop offset="0.29" stop-color="#4695d1"/><stop offset="0.34" stop-color="#408bc7"/><stop offset="0.51" stop-color="#2e6ca8"/><stop offset="0.69" stop-color="#215692"/><stop offset="0.85" stop-color="#1a4985"/><stop offset="1" stop-color="#174480"/></radialGradient><radialGradient id="Degradado_sin_nombre_10" cx="30.3" cy="27.83" r="52.65" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fefefe"/><stop offset="0.04" stop-color="#f6f6f6"/><stop offset="0.11" stop-color="#e0dfdf"/><stop offset="0.19" stop-color="#bbbab9"/><stop offset="0.29" stop-color="#8a8885"/><stop offset="0.36" stop-color="#5c5956"/><stop offset="0.4" stop-color="#767471"/><stop offset="0.45" stop-color="#9a9896"/><stop offset="0.51" stop-color="#b9b8b7"/><stop offset="0.58" stop-color="#d2d2d1"/><stop offset="0.65" stop-color="#e6e5e5"/><stop offset="0.73" stop-color="#f4f3f3"/><stop offset="0.83" stop-color="#fcfcfb"/><stop offset="1" stop-color="#fefefe"/></radialGradient><clipPath id="clip-path"><polygon class="cls-1" points="25.16 40.77 25.51 39.98 25.5 39.98 25.5 39.99 25.5 40 25.5 40.01 25.15 40.81 25.15 40.8 25.15 40.79 25.16 40.78 25.16 40.77"/></clipPath><radialGradient id="radial-gradient-4" cx="28.72" cy="11.57" r="0.02" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff"/><stop offset="0.17" stop-color="#63c3dd"/><stop offset="0.36" stop-color="#4695d1"/><stop offset="0.4" stop-color="#408bc7"/><stop offset="0.56" stop-color="#2e6ca8"/><stop offset="0.72" stop-color="#215692"/><stop offset="0.87" stop-color="#1a4985"/><stop offset="1" stop-color="#174480"/></radialGradient></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><path class="cls-2" d="M48.16,32.42A19.18,19.18,0,1,1,29,13.25,19.18,19.18,0,0,1,48.16,32.42Z"/><path class="cls-3" d="M11.44,37.41a1.52,1.52,0,0,0-.26-.45s0,0,0,0a.22.22,0,0,0,0-.08c-.06-1-.1-2.08-.11-3.13v-.32h-.09l-.38,0c0-.22-.09-.36-.16-.36s-.16.26-.***********.18.59.1-.1.13-.24l.26,0h.06v.13c0,.87.05,1.75.1,2.61v.1l-.05,0a.6.6,0,0,0-.08.08,17.58,17.58,0,0,0,.79,2.52s0,0,0,0a1.82,1.82,0,0,0,.08-.89A3.77,3.77,0,0,0,11.44,37.41ZM16.76,19.7l.14,0a2.45,2.45,0,0,0,1.49-1.25.51.51,0,0,0,0-.36s0,0,0,0a.3.3,0,0,0-.08-.08.66.66,0,0,0-.5-.09l-.17,0-.16,0c-.43.34-.84.7-1.24,1.07a.17.17,0,0,0,0,.05.54.54,0,0,0,0,.52A.53.53,0,0,0,16.76,19.7ZM14.13,35.53l0,.11a2.28,2.28,0,0,0,.35.85,1.26,1.26,0,0,0,.87.61h.06a1,1,0,0,0,.82-.4,1.89,1.89,0,0,0,.33-1.26,2.38,2.38,0,0,0-.46-1.34,1.24,1.24,0,0,0-.93-.56,1,1,0,0,0-.81.48,1.55,1.55,0,0,0-.24.62s0,.07,0,.11H14c-.66,0-1.22-.1-1.69-.16h-.09v-.13c0-1.41,0-2.83,0-4.22,0-.12,0-.23,0-.34l-.14,0c-.31,0-.58.1-.81.16l-.09,0a.76.76,0,0,1,0-.15c0-.7.06-1.4.11-2.06a.41.41,0,0,0,0-.11l.07,0a18.3,18.3,0,0,1,3.08-.83l.13,0c0,.06,0,.11,0,.17-.09.81-.16,1.66-.2,2.5,0,.13,0,.25,0,.38l.23,0c1.69-.16,3.86-.29,6.41-.38h.13a.69.69,0,0,1,0,.13,2,2,0,0,0,.48,1,1.8,1.8,0,0,0,1.35.57,2,2,0,0,0,1.43-.62,2.16,2.16,0,0,0,.63-1.47,1.89,1.89,0,0,0-.56-1.41,1.84,1.84,0,0,0-1.33-.53h0a2,2,0,0,0-1.38.63,2.33,2.33,0,0,0-.45.7,1,1,0,0,0,0,.1h-.1c-2.37.11-4.44.28-6.11.47H15c0-.05,0-.11,0-.16.06-.86.13-1.71.23-2.53,0-.12,0-.23,0-.35l-.24.05a18.53,18.53,0,0,0-3.67,1.1l-.11.05,0,.33c-.06.81-.1,1.62-.13,2.43,0,0,0,.08,0,.12l-.06,0-.22.06c0-.22-.06-.38-.14-.39s-.26.32-.31.74,0,.76.13.78.21-.21.27-.52l.39-.08.76-.12H12v.15c-.05,1.36,0,2.74,0,4.11,0,.12,0,.23,0,.35l.15,0c.51.08,1.14.17,1.9.26Zm1.69,3.95c-.69-.07-1.16.5-1.1,1.27A1.69,1.69,0,0,0,16,42.29,1.06,1.06,0,0,0,17.2,41,1.71,1.71,0,0,0,15.82,39.48Zm1.89-6.27a1.53,1.53,0,0,0,1.42,1.58,1.49,1.49,0,0,0,1.46-1.57,1.52,1.52,0,0,0-1.47-1.58A1.49,1.49,0,0,0,17.71,33.21Zm6.91,1.34a1.64,1.64,0,0,0,1.63,1.63,1.61,1.61,0,0,0,1.62-1.62,1.64,1.64,0,0,0-1.64-1.64A1.61,1.61,0,0,0,24.62,34.55ZM35.3,23a1.37,1.37,0,0,0,1.06-.36.9.9,0,0,0,.24-.78,1.29,1.29,0,0,0-.26-.59l-.09-.1a1.29,1.29,0,0,0-.21-.19,2.14,2.14,0,0,0-1.12-.45h-.06a2.11,2.11,0,0,0-.51,0,1,1,0,0,0-.82,1A1.85,1.85,0,0,0,35.25,23ZM24.87,21.7a1.48,1.48,0,0,0-1-.31h-.07a2.25,2.25,0,0,0-.62.15,1.78,1.78,0,0,0-1.1,1.34,1.1,1.1,0,0,0,.31.92,1.42,1.42,0,0,0,1.08.4h0a1.89,1.89,0,0,0,1.77-1.57,1.15,1.15,0,0,0-.15-.64l-.06-.1Zm-2.36-3c.75-.33,1.17-.94.95-1.36l0-.06c.8-.07,1.62-.13,2.44-.18H26s0,.07,0,.12c-.1.67-.2,1.48-.3,2.41,1.14-.06,2.32-.08,3.48-.08h1a.81.81,0,0,0,0-.16c0-.65,0-1.23-.07-1.74v-.12h.15c1.11,0,2.2.08,3.26.15l.34,0c0-.06,0-.12,0-.19-.11-.49-.22-.94-.33-1.33a.56.56,0,0,0,0-.12l.17,0c.94.06,1.87.16,2.76.27h.08l0,.05a1.89,1.89,0,0,0,.5.39,3.39,3.39,0,0,0,1.12.42l.17,0a1.11,1.11,0,0,0,.67-.1.3.3,0,0,0,.12-.37,17.72,17.72,0,0,0-1.86-1h-.07a1.38,1.38,0,0,0-.65.09.29.29,0,0,0-.18.21s0,.05,0,.08h-.12c-1-.13-2.08-.22-3.15-.28l-.3,0,0,.1c.09.38.19.8.27,1.26l0,.12h-.17c-.86,0-1.76-.09-2.67-.11h-.14a.41.41,0,0,1,0-.11c0-.58-.06-1.06-.1-1.46s0-.49-.05-.69a.25.25,0,0,1,0-.08H30c1.24,0,2.48.06,3.71.14L34,15,34,15a2,2,0,0,0-.14-.25c.14,0,.22-.05.23-.1s-.31-.22-.72-.27-.76,0-.77.08.31.22.72.27h0s0,0,0,.06l.05.09h-.17c-1.18-.06-2.4-.1-3.61-.1h-.29v.07c0,.19,0,.4,0,.62v.09h-.14q-1.06,0-2.1,0H26.9s0-.07,0-.1a10.52,10.52,0,0,1,.27-1.19c.3-.05.52-.15.51-.23s-.35-.14-.76-.09-.74.16-.73.26.19.11.44.11a8.84,8.84,0,0,0-.41,1.38.44.44,0,0,1,0,.1l.31,0c.87,0,1.76,0,2.63,0h.14v.09c0,.91,0,2,0,3.22v.13h-.15c-.82,0-1.65,0-2.46,0h-.16a.66.66,0,0,1,0-.14c.06-.83.13-1.55.2-2.15,0,0,0-.07,0-.11v0l-.34,0c-1.09,0-2.17.12-3.21.22l-.17,0c0-.05,0-.09,0-.13a11.2,11.2,0,0,1,.81-2c.3-.09.51-.21.5-.29s-.37-.09-.78,0-.71.24-.69.34.17.08.38.06l-.11.19,0,.07h-.1l-1,.11s0,0,0-.06-.24-.07-.47,0-.37.24-.34.33.25.07.47,0l.16-.08,1-.11.19,0a.67.67,0,0,0-.06.12,13.25,13.25,0,0,0-.6,1.48,2.42,2.42,0,0,0-.67.16c-.73.3-1.16.91-1,1.35S21.76,19.07,22.51,18.74Zm13,11A1.62,1.62,0,0,0,37,31.38a1.49,1.49,0,0,0,1.46-1.56A1.63,1.63,0,0,0,37,28.21,1.47,1.47,0,0,0,35.46,29.76ZM39.9,45.94l-.16,0a3,3,0,0,0-1.12.54,2.35,2.35,0,0,0-.52.53l-.05.06-.08,0c-1.06.17-2.2.32-3.38.43l-.38,0a1.6,1.6,0,0,1-.05.21c-.11.5-.23.94-.34,1.33l0,.08h-.12c-.83.07-1.68.11-2.5.14H31s0-.07,0-.1c.07-.48.13-1,.2-1.64,0-.42.08-.88.12-1.39,0,0,0-.08,0-.12h.15c1.57-.06,3.12-.18,4.58-.35l.38-.05h0l.06-.29c.19-.9.36-1.91.51-3,0,0,0-.07,0-.11l.1,0a2.26,2.26,0,0,0,.86-.53,2.1,2.1,0,0,0,.64-1.26,1.45,1.45,0,0,0-.35-1.18,1.46,1.46,0,0,0-1.14-.43h-.05a2.22,2.22,0,0,0-1.34.64,2.1,2.1,0,0,0-.65,1.33A1.37,1.37,0,0,0,35.55,42a1.49,1.49,0,0,0,.61.32l.11,0s0,.07,0,.11c-.12,1-.25,1.85-.4,2.68a.52.52,0,0,1,0,.11h-.13c-1.51.17-3.1.28-4.72.33h-.42c0,.09,0,.19,0,.28,0,.52,0,1-.07,1.43a.45.45,0,0,0,0,.11H27.74a.45.45,0,0,1,0-.11c-.06-1.29-.13-2.93-.17-4.92a.76.76,0,0,1,0-.15H31.1a.49.49,0,0,1,0-.12,2.44,2.44,0,0,1,0-.27c.08-2.39.13-5.31.14-8.25v-.13l.13,0a2.18,2.18,0,0,0,1-.57,2.08,2.08,0,0,0,.6-1.49,2.06,2.06,0,0,0-.62-1.48,2.11,2.11,0,0,0-3.61,1.46,2.15,2.15,0,0,0,.63,1.51,2.12,2.12,0,0,0,.74.47l.1,0v.11c0,2.68,0,5.37-.06,7.82v.15h-1l-2.14,0h-.46a.41.41,0,0,1,0,.11c0,.09,0,.19,0,.28.1,2.39.22,4.32.36,5.77,0,.06,0,.12,0,.18v0h.39l1.77,0h1.29v.12c-.07,1-.14,1.85-.21,2.44,0,0,0,.05,0,.07H28.37a1,1,0,0,0-.36-.12c0-.31-.07-.69-.1-1.2a0,0,0,0,1,0,0,.49.49,0,0,0,0-.12h-.35c-1.09,0-2.18-.07-3.22-.15H24.2l0-.09a45.41,45.41,0,0,1-1.23-8.46s0-.08,0-.12l.13,0a1.65,1.65,0,0,0,.78-.42,1.68,1.68,0,0,0,.51-1.31,2.18,2.18,0,0,0-2-2.06h0a1.71,1.71,0,0,0-1.28.52,1.73,1.73,0,0,0-.44,1.32,2.13,2.13,0,0,0,.63,1.36A2.07,2.07,0,0,0,22,40l.1,0s0,.07,0,.11a52.22,52.22,0,0,0,.75,5.73.77.77,0,0,1,0,.15l-.17,0c-.77-.08-1.55-.18-2.3-.3h-.09l0-.07a2.91,2.91,0,0,0-.52-.61,2.66,2.66,0,0,0-1.18-.58l-.13,0a.91.91,0,0,0-.76.19.75.75,0,0,0-.14.77,1.94,1.94,0,0,0,.66.91,2.53,2.53,0,0,0,1.11.53l.16,0a1.09,1.09,0,0,0,.75-.15.56.56,0,0,0,.22-.39s0-.07,0-.1l.12,0c.73.11,1.49.2,2.27.29H23s0,.07,0,.11c.19.86.39,1.6.59,2.22l.06.16.35,0c1,.09,2.08.14,3.11.18h.14s0,.06,0,.09c.05.37.1.7.15,1-.39,0-.66.16-.65.32s.43.31.91.32.88-.13.88-.31a.18.18,0,0,0,0-.09h.65c.56,0,1.12,0,1.67,0,0,0,0,0,0,0,0-.23.1-.5.15-.82,0,0,0-.05,0-.08h.13c1,0,2-.09,2.89-.16l.34,0,.06-.14q.21-.57.39-1.26l0-.09.12,0c1-.09,1.92-.22,2.83-.36l.13,0v.1a.41.41,0,0,0,.19.3,1,1,0,0,0,.66.11l.18,0A2.76,2.76,0,0,0,40,47.52a1.79,1.79,0,0,0,.66-.8.54.54,0,0,0-.09-.63A.86.86,0,0,0,39.9,45.94Zm2.67-2.24-.13,0v-.13a.71.71,0,0,0-.16-.46.7.7,0,0,0-.65-.2l-.13,0a2.44,2.44,0,0,0-1.06.64,2.34,2.34,0,0,0-.65,1.06.75.75,0,0,0,.16.78.81.81,0,0,0,.67.16l.15,0a2.24,2.24,0,0,0,1-.6,2.27,2.27,0,0,0,.46-.62.43.43,0,0,1,0-.07l.07,0c.65-.2,1.25-.4,1.78-.61l.47-.67-.06,0A18.84,18.84,0,0,1,42.57,43.7Zm-1.23-3.49a1.63,1.63,0,0,0,1.33-1.56,1.17,1.17,0,0,0-1.17-1.36,1.63,1.63,0,0,0-1.39,1.58A1.17,1.17,0,0,0,41.34,40.21Zm-19.62,8a3.52,3.52,0,0,0-1.16-.42l-.16,0a1.16,1.16,0,0,0-.72.12.36.36,0,0,0-.17.28v.1l-.13,0-.8-.16.6.39.43.08h.07l.05,0a1.86,1.86,0,0,0,.5.37,3.18,3.18,0,0,0,1.08.35l.17,0a1.55,1.55,0,0,0,.7-.1.3.3,0,0,0,.18-.42A1.53,1.53,0,0,0,21.72,48.25Zm24.49-8.44c-.08,0-.2.08-.31.26l-.61.26-.16.06,0,0c0,.11,0,.22-.05.34-.12.74-.25,1.45-.4,2.13a.13.13,0,0,1,0,.06c.16-.24.31-.48.46-.72.07-.4.15-.81.21-1.24,0,0,0-.07,0-.1l.07,0,.29-.14c0,.06,0,.1.06.12s.27-.15.39-.43a1,1,0,0,0,.12-.47l0,0h0A.19.19,0,0,0,46.21,39.81Zm-31,3.43a3.21,3.21,0,0,0-.55-1.11,1.43,1.43,0,0,0-.69-.57.32.32,0,0,0-.41.22,1.48,1.48,0,0,0,0,.71l0,.17a3.15,3.15,0,0,0,.47,1,1.92,1.92,0,0,0,.43.46l0,0s0,0,0,.06l.12.43c.15.18.3.37.46.55q-.13-.37-.24-.78a.5.5,0,0,1,0-.13h.1a.33.33,0,0,0,.26-.19,1.14,1.14,0,0,0,.05-.73A1.24,1.24,0,0,0,15.21,43.24Zm-2.87-18.5c.15.06.4-.21.56-.6L13,24A22.57,22.57,0,0,1,16.44,23l.12,0s0,.09,0,.13a1,1,0,0,0,.21.56.89.89,0,0,0,.8.27h.1a2.22,2.22,0,0,0,1.16-.64,2.42,2.42,0,0,0,.65-1.17,1,1,0,0,0-.25-.92,1,1,0,0,0-.8-.22l-.13,0a2.21,2.21,0,0,0-1.1.61,2.45,2.45,0,0,0-.46.66.59.59,0,0,0,0,.08l-.08,0A22.67,22.67,0,0,0,13,23.44a.15.15,0,0,0-.08-.11c-.14-.06-.39.21-.55.59S12.2,24.68,12.34,24.74Zm30.37-4.48c.13-.13-.14-.6-.59-1.05s-.92-.72-1-.59.14.6.59,1.05S42.58,20.38,42.71,20.26Zm5,10.56c0-.43-.21-.78-.35-.76s-.15.14-.18.33l-.39-.11-.1,0c0,.11,0,.22,0,.33,0,1.1,0,2.21,0,3.31v.15h-.08c-1.3.13-3.8.28-7.57.37H39a.74.74,0,0,1,0-.14,2.05,2.05,0,0,0-.45-.91,1.77,1.77,0,0,0-1.36-.64,1.92,1.92,0,0,0-1.43.66A2.43,2.43,0,0,0,35.05,35a2.19,2.19,0,0,0,.57,1.56,1.83,1.83,0,0,0,1.36.57h0a2,2,0,0,0,1.38-.7,2.55,2.55,0,0,0,.49-.9s0-.09,0-.13h.12c2.18-.1,4-.23,5.39-.37h.12c0,.05,0,.11,0,.17,0,.88-.09,1.75-.16,2.58,0,0,0,.07,0,.1a.18.18,0,0,1-.07,0,2,2,0,0,0-.51.55,3.2,3.2,0,0,0-.5,1.35A1.56,1.56,0,0,0,43.48,41a.54.54,0,0,0,.58.25l.1,0a1.58,1.58,0,0,0,.78-.71,3.29,3.29,0,0,0,.42-1.27,2,2,0,0,0-.14-1.09.67.67,0,0,0-.34-.31l-.08,0a.49.49,0,0,1,0-.12c.07-.82.12-1.67.15-2.52A.86.86,0,0,1,45,35h.1c.74-.08,1.31-.17,1.75-.25l.09,0h0c0-.12,0-.23,0-.35,0-1.07,0-2.15,0-3.21V31H47l.21.05c.07.35.2.6.32.59S47.76,31.26,47.72,30.82ZM47,36.16c-.21-.06-.51.4-.67,1s-.14,1.17.07,1.22.51-.4.67-1S47.18,36.21,47,36.16Zm-10-17a1.09,1.09,0,0,0-.29.72,1,1,0,0,0,0,.39,1.19,1.19,0,0,0,.07.23,3,3,0,0,0,.81,1.27c.9.86,2.09,1.06,2.65.48a1.22,1.22,0,0,0,.29-.88c.77.17,1.5.36,2.18.56l.1,0a.5.5,0,0,1,0,.12,39,39,0,0,1,.73,4.43c0,.14,0,.28.05.42l.18,0,.08,0c.49.1.91.21,1.26.31l.1,0a.66.66,0,0,0,0,.14c.07.81.13,1.63.17,2.44,0,.06,0,.12,0,.18l-.12,0c-1.09-.17-2.56-.33-4.43-.47h-.14a1,1,0,0,0,0-.17c-.05-1-.13-2-.23-2.94,0-.15,0-.3,0-.45h-.08l-.3,0c-2.32-.32-5.15-.55-8.19-.66h-.14a.65.65,0,0,1,0-.13,2.09,2.09,0,0,0-.63-1,2.4,2.4,0,0,0-1.57-.62h0a2.26,2.26,0,0,0-1.53.57,1.94,1.94,0,0,0-.66,1.44A2.08,2.08,0,0,0,28,27.07a2.37,2.37,0,0,0,1.65.66,2.24,2.24,0,0,0,1.58-.62,2.06,2.06,0,0,0,.48-.73.76.76,0,0,0,0-.11h.13c2.9.08,5.6.27,7.85.52l.14,0c0,.05,0,.1,0,.15.08,1,.15,2,.2,3,0,.16,0,.32,0,.49h.37c2.25.11,4,.24,5.2.38h.15c0-.12,0-.24,0-.37,0-1-.09-2-.17-2.91,0-.06,0-.12,0-.19l.13,0,.85.31c.15.38.36.64.5.6s.19-.51,0-1-.4-.9-.57-.85-.17.26-.14.56c-.3-.13-.64-.26-1-.4s-.8-.25-1.23-.36l-.11,0s0-.09,0-.13a41.41,41.41,0,0,0-.75-4.29.14.14,0,0,0,0-.06l-.06-.28-.28-.1c-.74-.24-1.58-.46-2.48-.67a3.24,3.24,0,0,0-.76-1.11C38.75,18.78,37.56,18.56,37,19.16ZM33,50.22c-.53.11-.93.34-.89.52s.5.22,1,.11.92-.35.89-.53S33.52,50.1,33,50.22Zm3.88-1.46c-.47.28-.77.63-.68.78s.54.06,1-.22.77-.63.68-.78S37.33,48.49,36.87,48.76Z"/><path class="cls-4" d="M36.68,10.66l1,.28,1,.31,1,.35.95.38c.85.36,1.68.75,2.48,1.18s1.59.91,2.34,1.41,1.48,1,2.18,1.61A28.43,28.43,0,0,1,51.4,20,25.57,25.57,0,0,1,53,22.1c.5.74,1,1.5,1.4,2.29s.82,1.6,1.17,2.42a25,25,0,0,1,.92,2.55A25.68,25.68,0,0,1,57.18,32a27.38,27.38,0,0,1,.4,2.74,26.39,26.39,0,0,1,.1,2.82,26,26,0,0,1-.18,2.8A27.4,27.4,0,0,1,57,43.08c-.2.89-.45,1.76-.73,2.61s-.62,1.69-1,2.51a26.5,26.5,0,0,1-1.22,2.37c-.44.78-.93,1.52-1.44,2.25S51.58,54.24,51,54.91s-1.2,1.32-1.84,1.93-1.33,1.2-2,1.75-1.44,1.08-2.2,1.56-1.54.94-2.34,1.36-1.64.8-2.49,1.14a26,26,0,0,1-2.61.91,27,27,0,0,1-2.72.67c-.92.18-1.86.32-2.81.41s-1.92.14-2.89.14l-.25-9.17H30A22.94,22.94,0,0,0,53,32.64a22.89,22.89,0,0,0-4.6-13.8,22.87,22.87,0,0,0-11.67-8.18Z"/><polygon class="cls-5" points="37.88 25.41 37.88 25.41 37.88 25.41 37.88 25.41"/><g class="cls-6"><polygon class="cls-1" points="25.16 40.77 25.51 39.98 25.5 39.98 25.5 39.99 25.5 40 25.5 40.01 25.15 40.81 25.15 40.8 25.15 40.79 25.16 40.78 25.16 40.77"/><g class="cls-7"><g class="cls-6"><polyline class="cls-1" points="25.16 40.77 25.51 39.98 25.5 39.98 25.5 39.99 25.5 40 25.5 40.01 25.15 40.81 25.15 40.8 25.15 40.79 25.16 40.78 25.16 40.77"/></g></g></g><path class="cls-8" d="M28.71,11.57h0Z"/><path class="cls-9" d="M104.9,17.22a13.08,13.08,0,0,1-5,1.34c-.91.14-2.37.22-3.42.37a11.66,11.66,0,0,0-2.68.76,3.5,3.5,0,0,0-1.59,1.59,4.64,4.64,0,0,0-.43,2.45A3.49,3.49,0,0,0,93,26.68c.94.87,2.21,1.14,4,1.14a8.37,8.37,0,0,0,4.5-1.27,6.08,6.08,0,0,0,2.91-2.88,10.68,10.68,0,0,0,.57-4.48l-.12-2Zm.47,10.25c-1.72,1.52-3,3-4.61,3.58A11.85,11.85,0,0,1,96,32c-3,0-4.84-.4-6.45-1.92a8.1,8.1,0,0,1-2.78-5.89,7.9,7.9,0,0,1,.67-3.82,8.49,8.49,0,0,1,2.27-3A10.34,10.34,0,0,1,93,15.74,31.25,31.25,0,0,1,97.07,15c3.75-.46,5.2-.33,6.51-.9a2.61,2.61,0,0,0,1.4-2.3c0-2-.37-2.68-1.25-3.48-1.18-1.1-2.5-1.46-4.84-1.46a6.83,6.83,0,0,0-4.51,1.21,5.5,5.5,0,0,0-1.79,4.07L88,12.2a9.48,9.48,0,0,1,1.38-5.38,8.11,8.11,0,0,1,3.84-2.93,17.36,17.36,0,0,1,6.21-1.14,13.66,13.66,0,0,1,5.34,1,6.92,6.92,0,0,1,3.09,2.12,6.71,6.71,0,0,1,1.62,3,27.56,27.56,0,0,1,.24,4.46v6.46c0,4.5-.1,6.53,0,7.79.12.69.35.88,1.68.93v3.27a8.64,8.64,0,0,1-3.26,0,3.49,3.49,0,0,1-2.72-2.66,10.85,10.85,0,0,0-.11-1.6Z"/><path class="cls-9" d="M194.7,3.45a3.6,3.6,0,0,0-1.08,2.63,3.61,3.61,0,0,0,1.08,2.64,3.68,3.68,0,0,0,5.25,0A3.61,3.61,0,0,0,201,6.08,3.6,3.6,0,0,0,200,3.45a3.71,3.71,0,0,0-5.25,0Zm5.68,5.69a4.31,4.31,0,0,1-6.11,0A4.2,4.2,0,0,1,193,6.08a4.31,4.31,0,1,1,8.62,0,4.2,4.2,0,0,1-1.26,3.06Zm-2.24-4.7a2.3,2.3,0,0,0-1-.17h-.71V6h.75a2.26,2.26,0,0,0,.79-.1.73.73,0,0,0,.49-.74.74.74,0,0,0-.31-.7Zm-.9-.74a3.66,3.66,0,0,1,1.29.17,1.16,1.16,0,0,1,.74,1.2,1,1,0,0,1-.47.93,1.59,1.59,0,0,1-.68.23,1.19,1.19,0,0,1,.82.47,1.34,1.34,0,0,1,.25.73v.35c0,.11,0,.22,0,.35a.74.74,0,0,0,.05.25l0,.06h-.77l0,0a.56.56,0,0,0,0-.07l0-.16V7.81A1.18,1.18,0,0,0,198,6.72a2,2,0,0,0-.93-.16h-.66V8.44h-.84V3.7Z"/><path class="cls-9" d="M171.19,22.09c-.15,2.08-2.71,5.33-7,5.33-5.27,0-7.93-3-7.93-8.79h19.92c0-9.85-3.86-16.24-11.73-16.24-9,0-13.09,6.87-13.09,15.49,0,8.05,4.53,14,12.2,14,4.38,0,6.15-1.06,7.4-1.91a12.39,12.39,0,0,0,4.85-7.84Zm-15-7.19c0-4.31,3.33-8.31,7.56-8.31,5.58,0,7.35,4,7.61,8.31Z"/><path class="cls-10" d="M133.39,13.66c0-7.64-5.2-9.16-9.16-9.16-4.39,0-6.36,2-7.29,3.39l-.15.15v-3l-4.43,0V31.9h4.77V17.46c0-6.82,4.09-8.71,6.22-8.71,4.2,0,5.21,2.63,5.21,7.29V31.9h4.83V13.66Z"/><path class="cls-10" d="M152.38,19.05V14.88h-9.46V7.06c0-1.84.23-2.63,3.33-2.63a11.92,11.92,0,0,1,1.34,0V.09c-.81,0-.82-.09-1.57-.09-4.86,0-7.89,1.58-7.89,5.06v9.87h-3.57v4.12h3.57V31.92h4.79V19.05Z"/><path class="cls-10" d="M63.32,23.14a3.37,3.37,0,0,1,0-1.21h5a6.18,6.18,0,0,0,.61,2.9,5.41,5.41,0,0,0,1.31,1.46,4.76,4.76,0,0,0,1.07.6,8.2,8.2,0,0,0,3.5.49,7.46,7.46,0,0,0,4.84-1.32c1-.91,1.39-1.31,1.39-2.51a2.57,2.57,0,0,0-1.2-2.43c-.64-.42-2.11-.73-4.66-1.38A45.73,45.73,0,0,1,68,17.45a7.92,7.92,0,0,1-3.26-2.69A7.21,7.21,0,0,1,64,11a7.32,7.32,0,0,1,.83-3.47,7.76,7.76,0,0,1,2.31-2.68A10.36,10.36,0,0,1,70.21,3.2a14.8,14.8,0,0,1,4.17-.58,15.54,15.54,0,0,1,5.7,1A7.72,7.72,0,0,1,83.6,6.41a10.2,10.2,0,0,1,1.61,4.35l.14.77H80.28A5.31,5.31,0,0,0,78.57,8c-1-.81-2-1-3.88-1-2.15,0-3.23.16-4.15.88a3.1,3.1,0,0,0-1.33,2.54,2.65,2.65,0,0,0,.58,1.67,4.19,4.19,0,0,0,1.79,1.27c.47.18,1.13.23,3.42.87a63.24,63.24,0,0,1,7.27,2.23,6.33,6.33,0,0,1,2.93,2.45A7.55,7.55,0,0,1,86,23.15a8.13,8.13,0,0,1-1.38,4.51,8.89,8.89,0,0,1-3.92,3.26,14.27,14.27,0,0,1-5.83,1.14c-3.58,0-6.33-.76-8.22-2.28s-2.82-3.65-3.34-6.64Z"/><path class="cls-10" d="M182.7,14.37a6.85,6.85,0,0,1,7.11-6.91l1.44,0V2.37a2.25,2.25,0,0,0-.9-.14,8.82,8.82,0,0,0-7.81,5.19l-.1,0V3.22H178V31.65h4.67V14.37Z"/><path class="cls-4" d="M80.31,64.3a14.77,14.77,0,0,1-5.43.88c-6.3,0-10.33-3.83-10.33-10,0-5.71,3.91-10.38,11.17-10.38a14,14,0,0,1,4.63.76l-1,4.51A8.26,8.26,0,0,0,76,49.5,5.06,5.06,0,0,0,70.77,55a5.08,5.08,0,0,0,5.35,5.46,9.32,9.32,0,0,0,3.47-.6Z"/><path class="cls-4" d="M101.19,54.81c0,7.14-4.66,10.41-9.47,10.41-5.25,0-9.28-3.75-9.28-10S86.25,44.83,92,44.83C97.52,44.83,101.19,48.94,101.19,54.81Zm-13,.2c0,3.35,1.28,5.86,3.67,5.86,2.16,0,3.56-2.35,3.56-5.86,0-2.92-1-5.87-3.56-5.87C89.19,49.14,88.2,52.13,88.2,55Z"/><path class="cls-4" d="M104.84,51.5c0-2.44-.08-4.51-.16-6.23h5.26l.28,2.68h.12a7.14,7.14,0,0,1,6-3.12c4,0,7,2.64,7,8.38V64.78h-6.07V54c0-2.51-.87-4.23-3.07-4.23A3.25,3.25,0,0,0,111.14,52a4.18,4.18,0,0,0-.24,1.52V64.78h-6.06Z"/><path class="cls-4" d="M132.88,57c.2,2.51,2.67,3.71,5.51,3.71a17.1,17.1,0,0,0,5.38-.8l.8,4.11a19.2,19.2,0,0,1-7.06,1.2c-6.62,0-10.41-3.83-10.41-9.93,0-5,3.07-10.42,9.85-10.42,6.3,0,8.7,4.91,8.7,9.74a14.41,14.41,0,0,1-.2,2.39ZM140,52.81c0-1.47-.64-4-3.43-4-2.55,0-3.59,2.32-3.75,4Z"/><path class="cls-4" d="M163.92,64.3a14.77,14.77,0,0,1-5.43.88c-6.3,0-10.33-3.83-10.33-10,0-5.71,3.91-10.38,11.17-10.38a14,14,0,0,1,4.63.76l-1,4.51a8.3,8.3,0,0,0-3.39-.6A5.06,5.06,0,0,0,154.38,55a5.08,5.08,0,0,0,5.35,5.46,9.32,9.32,0,0,0,3.47-.6Z"/><path class="cls-4" d="M174.69,39.69v5.58H179v4.47h-4.35V56.8c0,2.36.56,3.43,2.39,3.43a9.93,9.93,0,0,0,1.8-.16l0,4.59a11.9,11.9,0,0,1-3.95.52,6.3,6.3,0,0,1-4.55-1.68c-1.12-1.15-1.68-3-1.68-5.78v-8h-2.59V45.27h2.59V41Z"/><path class="cls-4" d="M196.21,64.34a13.22,13.22,0,0,1-5.93,1.17,9.84,9.84,0,0,1-10.1-10.21C180.18,48.7,185,43,192.29,43c5.7,0,9.78,3.91,9.78,9.36,0,4.72-2.66,7.7-6.15,7.7a2.53,2.53,0,0,1-2.79-2.49h-.06A4.76,4.76,0,0,1,188.89,60c-2.11,0-3.63-1.55-3.63-4.21a7.4,7.4,0,0,1,7.58-7.54,9,9,0,0,1,3.85.81l-1,6c-.32,1.91-.09,2.78.81,2.82,1.4,0,3.14-1.75,3.14-5.48,0-4.21-2.72-7.48-7.74-7.48s-9.29,3.89-9.29,10.07c0,5.41,3.46,8.49,8.29,8.49a10.57,10.57,0,0,0,4.7-1ZM193.33,51a3.82,3.82,0,0,0-1-.13c-2.14,0-3.82,2.1-3.82,4.6,0,1.23.55,2,1.62,2s2.46-1.52,2.75-3.4Z"/><path class="cls-4" d="M190.28,65.83A10.14,10.14,0,0,1,179.85,55.3a12.28,12.28,0,0,1,12.44-12.66c5.86,0,10.11,4.07,10.11,9.69,0,4.73-2.67,8-6.48,8a2.82,2.82,0,0,1-3-2.06,4.91,4.91,0,0,1-4.05,2.06c-2.36,0-3.95-1.82-3.95-4.54a7.73,7.73,0,0,1,7.9-7.87,9.42,9.42,0,0,1,4,.85l.2.11-1,6.22c-.2,1.19-.17,1.95.08,2.26a.48.48,0,0,0,.42.18h0a2,2,0,0,0,1.33-.63,6.39,6.39,0,0,0,1.45-4.52c0-4.34-2.91-7.15-7.42-7.15s-9,3.34-9,9.74c0,5,3.06,8.16,8,8.16a10,10,0,0,0,4.54-1l.35-.18.78,2.53-.26.12A13.56,13.56,0,0,1,190.28,65.83Zm2-22.54a11.65,11.65,0,0,0-11.79,12,9.51,9.51,0,0,0,9.78,9.88,13.07,13.07,0,0,0,5.53-1l-.38-1.24a11,11,0,0,1-4.5.9c-5.23,0-8.62-3.45-8.62-8.81,0-5.92,4.14-10.39,9.62-10.39,4.9,0,8.07,3.06,8.07,7.8,0,2.94-1,4.38-1.65,5a2.47,2.47,0,0,1-1.83.82,1.15,1.15,0,0,1-.91-.42c-.39-.48-.45-1.33-.21-2.78l.94-5.75a9.36,9.36,0,0,0-3.5-.67,7.08,7.08,0,0,0-7.25,7.22c0,2.36,1.29,3.89,3.3,3.89a4.45,4.45,0,0,0,3.9-2.34l.09-.15h.48l.1.29a2.22,2.22,0,0,0,2.46,2.2c3.43,0,5.83-3,5.83-7.38C201.75,47.09,197.77,43.29,192.29,43.29Zm-2.17,14.48c-1.22,0-1.94-.87-1.94-2.33,0-2.72,1.86-4.93,4.14-4.93a4.43,4.43,0,0,1,1.08.14l.29.07-.5,3.37C192.88,56.12,191.5,57.77,190.12,57.77Zm2.2-6.61c-1.93,0-3.49,1.92-3.49,4.28,0,.76.22,1.68,1.29,1.68s2.17-1.46,2.43-3.13l.41-2.77A3,3,0,0,0,192.32,51.16Z"/><path class="cls-10" d="M21,54.3,20,54l-1-.31-1-.35L17.11,53c-.85-.36-1.68-.75-2.49-1.18s-1.58-.91-2.34-1.41-1.48-1-2.18-1.61-1.36-1.17-2-1.8-1.24-1.29-1.82-2-1.11-1.4-1.62-2.14a25.38,25.38,0,0,1-1.39-2.29c-.43-.79-.82-1.6-1.18-2.42s-.65-1.68-.92-2.55A25.68,25.68,0,0,1,.5,33a27.37,27.37,0,0,1-.39-2.74A26.38,26.38,0,0,1,0,27.39c0-.94.08-1.88.19-2.8a27.4,27.4,0,0,1,.47-2.71,25.14,25.14,0,0,1,.73-2.61c.29-.85.61-1.69,1-2.51s.78-1.6,1.22-2.37A26.16,26.16,0,0,1,5,12.14c.51-.72,1.07-1.42,1.65-2.09s1.2-1.32,1.85-1.93a26,26,0,0,1,2-1.75,26.26,26.26,0,0,1,2.19-1.56c.76-.49,1.54-.94,2.35-1.36s1.63-.8,2.48-1.14A26.87,26.87,0,0,1,20.2,1.4,27.68,27.68,0,0,1,22.92.73c.92-.18,1.86-.32,2.81-.41s1.92-.14,2.9-.14l.25,9.17H27.67a22.72,22.72,0,0,0-8.93,1.81A23,23,0,0,0,21,54.3Z"/></g></g></svg>',
                width: 120,
                alignment: 'center', margin: [ 0, 30, 0, 0 ]
              },
              {image: `${this.imgenProductoBase64}`, width: 250, alignment: 'center'},
              {text: `${producto.PrincipioActivo}`,
                style: 'activo',  alignment: 'center'
              },
              {text: `Acción terapeutica:`,
                style: 'contenido',  alignment: 'center'
              },
              {text: `${producto.Descripcion}`,
                style: 'contenido',  alignment: 'center'
              },
              {text: `${producto.Nombre}`,
                style: 'header',  alignment: 'center'},
              {
                layout: 'lightHorizontalLines', // optional
                table: {
                  // headers are automatically repeated if the table spans over multiple pages
                  // you can declare how many rows should be treated as headers
                  headerRows: 1,
                  widths: [ '*', 'auto' ],

                  body: [
                    [ '', ''],
                    [ { text: 'Denominación genérica:', bold: true }, `${producto.PrincipioActivo}` ],
                    [ { text: 'Acción terapeutica:', bold: true }, `${producto.Descripcion}` ],

                    [ { text: 'Concentración:', bold: true }, `${producto.Concentracion}` ],
                    [ { text: 'Contenido:', bold: true }, `${producto.Contenido}` ],
                    [ { text: 'Unidad de negocio:', bold: true }, `${producto.lineaNegocio}` ],
                    [ { text: 'Registro Sanitario:', bold: true }, `${producto.Registro_sanitario}` ],
                    [ { text: 'EAN:', bold: true }, `${producto.SKU}` ],
                    [ { text: 'Laboratorio:', bold: true }, `${producto.Laboratorio}` ],

                  ]
                }
              },
              {image: `${this.codigoB64}`, width: 120, alignment: 'center'},
              {text: `Muestra este código en farmacia`,
                style: 'mostrar',  alignment: 'center'
              }
            ],
            styles: {
              header: {
                fontSize: 18,
                bold: true,
                color: '#f32d36',
              },
              mostrar: {
                fontSize: 8,
                bold: true,
                color: '#f32d36',
              },
              activo: {
                fontSize: 10,
                bold: true,
                color: '#f32d36',
              },
              contenido: {
                fontSize: 8,
                bold: true,
                color: '#4a4f54',
              },
              subheader: {
                fontSize: 14,
                bold: true,
                margin: [0, 15, 0, 0]
              },
              story: {
                italic: true,
                alignment: 'center',
                width: '50%',
              },
              content: {
                background: '#ffffff'
              }
            }
          };
          this.pdfObj = pdfMake.createPdf(docDefinition);

          if (this.plt.is('capacitor')) {
            this.pdfObj.getBuffer((buffer: any) => {

              const blob = new Blob([buffer], { type: 'application/pdf' });

              if (this.plt.is('android')) {
                this.pdfObj.getBase64((pdfbase64: any) => {
                  Filesystem.writeFile({
                    path: `${producto.Nombre}_${producto.SKU}.pdf`,
                    data: pdfbase64,
                    directory: Directory.Documents,

                    // encoding: FilesystemEncoding.UTF8
                  }).then(writeFileResponse => {
                    console.log('writeFile success => ', writeFileResponse);
                    Share.share({
                      title: `${producto.Nombre}_${producto.SKU}.pdf`,
                      url: writeFileResponse.uri,
                    }).then(resShare => {
                      this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre}`);

                    });
                    this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre}`);
                  }, error => {
                    console.log('writeFile error => ', error);
                    this.widgetUtilService.presentToastError(error);
                  });
                });
              }
              if (this.plt.is('ios')) {

                // Save the PDF to the data Directory of our App
                Filesystem.writeFile({
                  path: `${producto.Nombre}_${producto.SKU}.pdf`,
                  data: blob,
                  directory: Directory.Documents,

                  // encoding: FilesystemEncoding.UTF8
                }).then(fileEntry => {
                  // Open the PDf with the correct OS tools
                  Share.share({
                    title: `${producto.Nombre}_${producto.SKU}.pdf`,
                    url: fileEntry.uri,
                  }).then(resShare => {
                    this.widgetUtilService.presentToast(`Has descargado ${producto.Nombre} `);
                    // this.fbas.logEventParam('producto_pdf_descargado', 'product_name', producto.Nombre);

                  });
                });
              }
            });
          } else {

            // On a browser simply use download!
            this.pdfObj.download();
          }
        });


  }
}
