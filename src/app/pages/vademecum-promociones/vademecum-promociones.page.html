<ion-header>
  <ion-toolbar color="primary">
    <ion-title *ngFor="let promocion of promociones"  >{{promocion.Comercio}}</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="salir()">
        <ion-icon size="large" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-fab horizontal="end" vertical="top" slot="fixed">
  <ion-fab-button color="secondary" (click)="compartir()">
    <ion-icon name="send"></ion-icon>    </ion-fab-button>

</ion-fab>
<ion-content *ngFor="let promocion of promociones">
  <ion-img src="{{strapiUrl}}{{promocion.Banner.url}}"></ion-img>
<ion-button expand="full" (click)="abrirSitiodeCompra(promocion.URL)"> Ir al sitio de la promoción   <ion-icon name="exit"></ion-icon>
</ion-button>
  <ion-card>
    <ion-card-header>
      <ion-card-title class="ion-text-center">{{promocion.Promocion}}</ion-card-title>
    <ion-card-subtitle class="ion-text-center">{{promocion.Condiciones}}</ion-card-subtitle>
      <ion-card-subtitle class="small_text ion-text-center"><ion-icon name="calendar"></ion-icon> Vigencia: {{promocion.Vigencia}}</ion-card-subtitle>

    </ion-card-header>
  </ion-card>

</ion-content>
