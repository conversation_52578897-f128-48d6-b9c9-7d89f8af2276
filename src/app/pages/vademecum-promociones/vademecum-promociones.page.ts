import {Component, Input, OnInit} from '@angular/core';
import {ModalController, Platform} from '@ionic/angular';
import {environment} from '../../../environments/environment';
import {RespuestaStrapi} from '../../interfaces/strapiInterfaces';
// import {FilesystemDirectory, Plugins, registerWebPlugin} from '@capacitor/core';
// const { Filesystem, Share, Browser} = Plugins;


// Actualizacion de Capacitor a 3.0
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
// import { Storage } from '@capacitor/storage';
import { <PERSON>rowser } from '@capacitor/browser';
import { Share } from '@capacitor/share';
// import { Http } from '@capacitor-community/http';
// import { Device } from '@capacitor/device';
// import { Geolocation } from '@capacitor/geolocation';




// import * as JsBarcode from 'jsbarcode';
// import {File} from '@ionic-native/file/ngx';

import * as pdfMake from 'pdfmake/build/pdfmake';


import {WidgetUtilService} from '../../services/widget-util.service';
@Component({
  selector: 'app-vademecum-promociones',
  templateUrl: './vademecum-promociones.page.html',
  styleUrls: ['./vademecum-promociones.page.scss'],
})
export class VademecumPromocionesPage implements OnInit {


  @Input() Info: any;

  strapiUrl = environment.strapiURL;
  pdfObj = null;

  promociones: any;
  banner: any;
  promocion: any;
  condiciones: any;
  comercio: any;
  vigencia: any;
  producto: any;

  productos: RespuestaStrapi[] = [];

  informacion: any;
  imagen: any;
  codigoB64: any;

  nombre: any;
  imgenProductoBase64: any;


  constructor( private modalCtrl: ModalController,
               private plt: Platform,
               private file: File,
               private widgetUtilService: WidgetUtilService
               // private fileOpener: FileOpener
  ) { }

  ngOnInit() {

    this.productos = this.Info;
    this.promociones = [this.Info[0].Promociones[0]];
    this.banner = this.strapiUrl + this.Info[0].Promociones[0].Banner.url;
    this.promocion = this.Info[0].Promociones[0].Promocion;
    this.condiciones = this.Info[0].Promociones[0].Condiciones;
    this.comercio = this.Info[0].Promociones[0].Comercio;
    this.vigencia = this.Info[0].Promociones[0].Vigencia;
    this.producto = this.Info[0].Promociones[0].Producto;
    // console.log('Informacion recbida', this.producto + this.condiciones + this.comercio + this.vigencia + this.promocion);

  }


  convertToDataURLviaCanvas(url: string, outputFormat: string) {
    return new Promise( (resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = function() {
        let canvas = <HTMLCanvasElement> document.createElement('CANVAS'),
            ctx = canvas.getContext('2d'),
            dataURL;
        canvas.height = img.height;
        canvas.width = img.width;
        ctx.drawImage(img, 0, 0);
        dataURL = canvas.toDataURL(outputFormat);
        canvas = null;
        resolve(dataURL);
      };
      img.src = url;
    });
  }

  async compartir() {
    /*const shareRet = await Share.share({
      title: `Oferta ${this.promocion} en ${this.producto}`,
      text: `${this.promocion} en ${this.producto}, ${this.condiciones} en ${this.comercio}, vigencia ${this.vigencia}`,
      url: this.banner,
      dialogTitle: `${this.promocion} en ${this.producto}, ${this.condiciones} en ${this.comercio}, vigencia ${this.vigencia}`
    });*/
    this.convertToDataURLviaCanvas(this.banner, 'image/jpeg')
        .then( data => {
          this.imgenProductoBase64 = data;
          const docDefinition = {
            content: [

              {image: `${this.imgenProductoBase64}`, width: 600, alignment: 'center'},
            ],
            styles: {
              header: {
                fontSize: 18,
                bold: true,
                color: '#f32d36',
              },
              subheader: {
                fontSize: 14,
                bold: true,
                margin: [0, 15, 0, 0]
              },
              story: {
                italic: true,
                alignment: 'center',
                width: '50%',
              },
              content: {
                background: '#ffffff'
              }
            }
          };
          // @ts-ignore
          this.pdfObj = pdfMake.createPdf(docDefinition);

          if (this.plt.is('cordova') || this.plt.is('capacitor')) {
            this.pdfObj.getBuffer((buffer) => {
              const blob = new Blob([buffer], { type: 'application/pdf' });


              if (this.plt.is('android')) {
                this.pdfObj.getBase64((pdfbase64) => {
                  Filesystem.writeFile({
                    path: `Oferta_en_${this.producto}.pdf`,
                    data: pdfbase64,
                    directory: Directory.Documents,


                    // encoding: FilesystemEncoding.UTF8
                  }).then(writeFileResponse => {
                    console.log('writeFile success => ', writeFileResponse);
                    Share.share({
                      title: `Oferta_en_${this.producto}.pdf`,
                      url: writeFileResponse.uri,
                    }).then(resShare => {
                      this.widgetUtilService.presentToast(`Gracias por compartir ${this.nombre} con su paciente`);

                    });
                    this.widgetUtilService.presentToast(`Gracias por compartir ${this.nombre} con su paciente`);
                  }, error => {
                    console.log('writeFile error => ', error);
                    this.widgetUtilService.presentToastError(error);
                  });
                });
              }
              // if (this.plt.is('ios')) {
              //
              //   // Save the PDF to the data Directory of our App
              //   this.file.writeFile(this.file.dataDirectory, `Oferta_en_${this.producto}.pdf`,
              //       blob, { replace: true }).then(fileEntry => {
              //     // Open the PDf with the correct OS tools
              //     Share.share({
              //       title: `Oferta_en_${this.producto}.pdf`,
              //       url: fileEntry.nativeURL,
              //     }).then(resShare => {
              //       this.widgetUtilService.presentToast(`Gracias por compartir esta promoción de ${this.producto}`);
              //
              //     });
              //   });
              // }
            });
          } else {
            // On a browser simply use download!
            this.pdfObj.download();
          }
        });



  }

  async abrirSitiodeCompra(url: string) {
    await Browser.open({ url: `${url}` });

  }

  salir() {

    this.modalCtrl.dismiss();
  }


}
