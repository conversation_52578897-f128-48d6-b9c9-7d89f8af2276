import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { VademecumPage } from './vademecum.page';
import {PipesModule} from '../../pipes/pipes.module';
import {NgPipesModule} from 'ngx-pipes';
import {SharedComponentsModule} from "../../modules/shared-components/shared-components.module";


const routes: Routes = [
  {
    path: '',
    component: VademecumPage
  }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RouterModule.forChild(routes),
        PipesModule,
        NgPipesModule,
        SharedComponentsModule
    ],
  declarations: [VademecumPage],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]

})
export class VademecumPageModule {}
