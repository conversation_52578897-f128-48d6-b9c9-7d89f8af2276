<ion-header>
  <ion-toolbar color="primary">
    <ion-title>Sanfer Vademecum</ion-title>
    <ion-buttons slot="start">
      <ion-back-button text="Regresar" defaultHref="/" (click)="onBackButtonClick()"></ion-back-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="openSearchModal()">
        <ion-icon name="search" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar *ngIf="role === 'representante'">
    <ion-buttons slot="start">
      <ion-button *ngIf="hasFilters()" (click)="resetFilters()">
        <ion-icon name="close-circle-outline"></ion-icon> Limpiar Filtros
      </ion-button>
      <ion-button *ngIf="!hasFilters()" size="large" color="primary" (click)="openFilterModal()">
        Filtrar productos
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <!-- Botones para seleccionar/deseleccionar todos -->
      <ion-button *ngIf="!allFilteredSelected" size="large" fill="solid" color="primary" (click)="selectAllProducts()">
        Seleccionar Todos
      </ion-button>
      <ion-button *ngIf="allFilteredSelected" size="large" fill="solid" color="primary" (click)="deselectAllProducts()">
        Deseleccionar Todos
      </ion-button>

      <!-- Botón para exportar PDF existente -->
      <ion-button size="large" fill="solid" color="primary" (click)="exportarPDF()" [disabled]="arregloProductosPDF.length < 1">
        Exportar PDF
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" [pullFactor]="0.5" [pullMin]="100" [pullMax]="160" (ionRefresh)="vademecumRefresh($event)" *ngIf="user.role === 'medico'">
    <ion-refresher-content
      pullingIcon="chevron-down-circle-outline"
      pullingText="Tirar hacia abajo para actualizar"
      refreshingSpinner="circles"
      refreshingText="Actualizando contenido..."
    >
    </ion-refresher-content>
  </ion-refresher>

  <ion-grid [fixed]="true">
    <ion-button *ngIf="searchInProgress" (click)="limpiarBusqueda();"><ion-icon name="close"></ion-icon> Borrar resultados</ion-button>

    <div *ngIf="role === 'medico'">
      <div *ngIf="medicamentoSugerido && !searchInProgress">
        <div class="ion-padding separadores no_padding_top">
          Destacados para ti <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>
      </div>
      <ion-row *ngIf="medicamentoSugerido && !searchInProgress">
        <ion-col
          size="12"
          size-xs="4"
          size-sm="4"
          size-md="4"
          class="ion-no-padding"
          size-lg="2"
          size-xl="2"
          *ngFor="let product of medicamentoSugerido | filterBy: [searchBy]: textoBuscar: false | orderBy: searchBy; let i = index;"
        >
          <ion-card button="true" (click)="openInfoModal(product)" class="medicamentoSugerido" mode="md">
            <div class="imagen-producto">
              <img *ngIf="product.Imagen_de_producto[0].formats.small"
                   [src]="strapiUrl + product.Imagen_de_producto[0].formats.small.url" />
            </div>
            <ion-card-header>
              <ion-card-title color="primary" *ngIf="product.Nombre" class="title-container">
                {{ product.Nombre.length > 12 ? (product.Nombre | slice:0:12) + '...' : product.Nombre }}
                <ion-icon color="primary" name="arrow-forward"></ion-icon>
              </ion-card-title>
              <ion-card-subtitle color="secondary" *ngIf="product.PrincipioActivo">
                {{ product.PrincipioActivo.length > 20 ? (product.PrincipioActivo | slice:0:20) + '...' : product.PrincipioActivo }}
              </ion-card-subtitle>
            </ion-card-header>

            <ion-card-content style="font-size: 10px" *ngIf="product.Descripcion">
              <div class="descripcion" *ngIf="product.Descripcion">
                <span *ngFor="let line of getLinesFromDescription(product.Descripcion, 17)">
                  {{ line || '&nbsp;' }}
                </span>
              </div>
              <span style="font-size: 8px" *ngIf="product.SKU"><ion-icon name="barcode" size="" color="primary"></ion-icon> EAN #{{ product.SKU }}</span>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>

      <ion-card color="primary" *ngIf="!searchInProgress"><ion-card-content>
        <h3 style="font-size: 20px;" class="ion-text-center">Todos nuestros Productos</h3>
      </ion-card-content></ion-card>
    </div>

    <ion-row>
      <ion-col
        size="12"
        size-xs="4"
        size-sm="4"
        size-md="4"
        class="ion-no-padding"
        size-lg="2"
        size-xl="2"
        *ngFor="let product of filteredProducts | orderBy: searchBy; let i = index;"
      >
        <ion-card class="medicamentoSugerido" mode="md">
          <ion-item *ngIf="role === 'representante'">
            <ion-checkbox
              slot="start"
              [value]="product.id"
              [checked]="product.isSelected"
              (ionChange)="toggleProduct(product)">
            </ion-checkbox>
            <ion-label (click)="toggleProduct(product)">
              <p style="font-size: 12px; height: 25px"> {{ product.Nombre }}</p>
            </ion-label>
          </ion-item>

          <div class="imagen-producto" (click)="openProduct(product.slug)">
            <img *ngIf="product.medicamentos.Imagen_de_producto[0].formats.small" [src]="strapiUrl + product.medicamentos.Imagen_de_producto[0].formats.small.url" />
          </div>
          <ion-card-header (click)="openProduct(product.slug)">
            <ion-card-title color="primary" *ngIf="product.Nombre" class="title-container">
              {{ product.Nombre.length > 12 ? (product.Nombre | slice:0:12) + '...' : product.Nombre }}
              <ion-icon color="primary" name="arrow-forward"></ion-icon>
            </ion-card-title>
            <ion-card-subtitle color="secondary" *ngIf="product.medicamentos.PrincipioActivo">
              {{ product.medicamentos.PrincipioActivo.length > 20 ? (product.medicamentos.PrincipioActivo | slice:0:20) + '...' : product.medicamentos.PrincipioActivo }}
            </ion-card-subtitle>
          </ion-card-header>
          <ion-card-content style="font-size: 10px" *ngIf="product.medicamentos.Descripcion" (click)="openProduct(product.slug)">
            <div class="descripcion" *ngIf="product.medicamentos.Descripcion">
              <span *ngFor="let line of getLinesFromDescription(product.medicamentos.Descripcion, 17)">
                {{ line || '&nbsp;' }}
              </span>
            </div>
            <span style="font-size: 8px" *ngIf="product.medicamentos.SKU"><ion-icon name="barcode" size="" color="primary"></ion-icon> EAN #{{ product.medicamentos.SKU }}</span>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row style="height: 400px"></ion-row>
  </ion-grid>

  <!-- Modal de búsqueda -->
  <ion-modal
    #searchModal
    [initialBreakpoint]="0.25"
    [breakpoints]="[0, 0.25]"
  >
    <ng-template>
      <ion-content class="ion-padding" color="primary">
        <ion-searchbar #searchBar color="light" placeholder="Buscar medicamento" (click)="searchModal.setCurrentBreakpoint(0.25)" (ionInput)="buscar($event)"></ion-searchbar>

        <div class="ion-text-center">
          <ion-chip class="chipWhite">
            <ion-label>Ordenar y buscar por:</ion-label>
          </ion-chip>

          <ion-chip (click)="selecionado('Nombre')" [ngStyle]="{'color': searchBy === 'Nombre' ? 'white' : '', 'background-color': searchBy === 'Nombre' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color': searchBy === 'Nombre' ? 'red' : ''}"></ion-icon>
            <ion-label>Nombre</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('division')" [ngStyle]="{'color': searchBy === 'division' ? 'white' : '', 'background-color': searchBy === 'division' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color': searchBy === 'division' ? 'red' : ''}"></ion-icon>
            <ion-label>Division</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('fv')" [ngStyle]="{'color': searchBy === 'fv' ? 'white' : '', 'background-color': searchBy === 'fv' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color': searchBy === 'fv' ? 'red' : ''}"></ion-icon>
            <ion-label>Fuerza de Ventas</ion-label>
          </ion-chip>
          <ion-chip (click)="selecionado('udn')" [ngStyle]="{'color': searchBy === 'udn' ? 'white' : '', 'background-color': searchBy === 'udn' ? 'rgba(0,0,0,0.5)' : ''}">
            <ion-icon name="checkmark-circle" [ngStyle]="{'color': searchBy === 'udn' ? 'red' : ''}"></ion-icon>
            <ion-label>Unidad de Negocio</ion-label>
          </ion-chip>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Modal de filtros -->
  <ion-modal
    #filterModal
    [initialBreakpoint]="0.25"
    [breakpoints]="[0, 0.25, 0.35]"
    [backdropDismiss]="false"
    [backdropBreakpoint]="0.5"
  >
    <ng-template>
      <ion-content class="ion-padding">
        <div class="ion-text-center">
          <ion-list>
            <ion-item>
              <ion-select
                (click)="filterModal.setCurrentBreakpoint(0.35)"
                aria-label="Division"
                placeholder="Seleccionar Division"
                [(ngModel)]="selectedDivision"
                (ionChange)="handleDivisionChange($event)"
              >
                <ion-select-option value="farma">Farma</ion-select-option>
                <ion-select-option value="consumo">Consumo</ion-select-option>
                <ion-select-option value="oftalmologia">Oftalmologia</ion-select-option>
                <ion-select-option value="altaEspecialidad">Alta Especialidad</ion-select-option>
                <ion-select-option value="sinDivision">Sin Division</ion-select-option>
              </ion-select>
            </ion-item>
          </ion-list>
          <ion-list>
            <ion-item>
              <ion-select
                (click)="filterModal.setCurrentBreakpoint(0.35)"
                aria-label="UDN"
                placeholder="Seleccionar Unidad de Negocio"
                [(ngModel)]="selectedUDN"
                (ionChange)="handleUDNChange($event)"
              >
                <!-- Opciones de UDN -->
                <!-- ... -->
              </ion-select>
            </ion-item>
          </ion-list>

          <ion-list>
            <ion-item>
              <ion-select
                (click)="filterModal.setCurrentBreakpoint(0.35)"
                aria-label="FV"
                placeholder="Seleccionar Fuerza de Ventas"
                [(ngModel)]="selectedFV"
                (ionChange)="handleFVChange($event)"
              >
                <!-- Opciones de FV -->
                <!-- ... -->
              </ion-select>
            </ion-item>
          </ion-list>

          <!-- Botón para resetear filtros -->
          <ion-button *ngIf="hasFilters()" (click)="resetFilters()">Limpiar Filtros</ion-button>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Modal de información -->
  <ion-modal
    #infoModal
    [handle]="true"
    handleBehavior="none"
    [initialBreakpoint]="1"
    [breakpoints]="[0, 1]"
    [backdropDismiss]="true"
  >
    <ng-template>
      <ion-content>
        <!-- Contenido del modal de información -->
        <!-- ... -->
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
