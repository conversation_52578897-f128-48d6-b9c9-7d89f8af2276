.colorPrincipal{
 --ion-color-primary: red;
}

.alert-button-close {
  --color: var(--ion-color-primary); // Cambia el color del texto
  --background: var(--ion-color-light); // Cambia el color de fondo
  font-weight: bold; // Hacer el texto en negrita
}
ion-card-title.recomendados {
 font-size: 20px;
}
ion-card-subtitle.recomendados {
 font-size: 9px;
}


ion-card{

  margin: 5px;
  padding: 0;
  box-shadow:
    rgb(255 0 0 / 20%) 0px 3px 5px -2px,
    rgb(255 0 0 / 14%) 0px 3px 0px 0px,
    rgb(255 0 0 / 12%) 0px 1px 5px 1px;
  //box-shadow:none !important

}

.imagen-producto {
  height: 220px; /* Altura fija para todas las imágenes */
  width: 100%; /* Ancho responsivo basado en el ion-card */
  overflow: hidden; /* Oculta cualquier parte de la imagen que exceda el tamaño del contenedor */
  display: flex;
  align-items: center; /* Centra la imagen verticalmente */
  justify-content: center; /* Centra la imagen horizontalmente */
}

.imagen-producto img {
  max-height: 100%; /* Asegura que la altura de la imagen no exceda la del contenedor */
  max-width: 100%; /* Permite que la imagen se escale de manera responsiva */
  object-fit: cover; /* Asegura que la imagen cubra todo el contenedor sin distorsionarse */
}
ion-card-header{
  padding: 0 5px 0 5px;
}

ion-card-title{
  font-size: 13px;
}

ion-card-subtitle{
  margin-top: 0px;
  font-size: 8px;

}

ion-card-content {
  font-size: 10px;
  padding: 5px;

}
//.descripcion span {
//  display: block;
//  overflow: hidden;
//  text-overflow: ellipsis;
//  white-space: nowrap;
//  width: 100%;
//  min-height: 1.2em; /* Asegura que cada línea tenga una altura mínima */
//}

.title-container {
  display: flex; /* Habilita Flexbox */
  justify-content: space-between; /* Distribuye el espacio entre los elementos */
  align-items: center; /* Alinea los elementos verticalmente en el centro */
  width: 100%; /* Ocupa el ancho completo del contenedor padre */
}

.descripcion {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word; /* Asegura que las palabras se rompan adecuadamente */
  max-height: 3.6em; /* Altura para 3 líneas, ajusta según tu tamaño de fuente */
  line-height: 1.2em; /* Ajusta según tu tamaño de fuente */
}
.descripcion span {
  //display: block;
  height: 1.2em; /* Asegura que cada línea tenga una altura uniforme */
  line-height: 1.2em;
  overflow: hidden;
  //white-space: nowrap;
  text-overflow: ellipsis;
}

@media screen and (max-width: 768px) { /* Ajusta este valor según tus necesidades */
  .imagen-producto {
    height: 130px; /* Altura reducida para pantallas más pequeñas */
  }
}

.medicamentoSugerido h1 {
  font-size: 12px;
}

.medicamentoSugerido h4 {
  font-size: 8px;
}


ion-chip.chipWhite {
  --background: #fff;
  //--color: var(--ion-color-primary);
}

