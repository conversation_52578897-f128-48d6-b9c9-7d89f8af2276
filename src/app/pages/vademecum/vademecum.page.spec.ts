import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VademecumPage } from './vademecum.page';

describe('VademecumPage', () => {
  let component: VademecumPage;
  let fixture: ComponentFixture<VademecumPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VademecumPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VademecumPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
