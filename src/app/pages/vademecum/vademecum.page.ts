import { After<PERSON>iewInit, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { AlertController, IonInput, IonModal, Platform } from '@ionic/angular';
import { <PERSON><PERSON><PERSON> } from "@capacitor/browser";
import { Directory, Filesystem } from "@capacitor/filesystem";
import { Share } from "@capacitor/share";
import { marked } from "marked";

import { StrapiService } from '../../services/strapi.service';
import { WidgetUtilService } from '../../services/widget-util.service';
import { ContentService } from '../../services/content.service';
import { DataService } from '../../services/data.service';
import { BrowserService } from '../../services/browser.service';
import { PdfgeneratorService } from '../../services/pdfgenerator.service';
import { InteractionService } from '../../services/interaction.service';
import { AnalyticsService } from '../../services/analytics.service';

import { RespuestaStrapi, ImagenDeProducto } from '../../interfaces/strapiInterfaces';
import { environment } from '../../../environments/environment';
import firebase from "firebase/compat";
import Timestamp = firebase.firestore.Timestamp;

@Component({
  selector: 'app-vademecum',
  templateUrl: './vademecum.page.html',
  styleUrls: ['./vademecum.page.scss'],
})
export class VademecumPage implements OnInit {
  @ViewChild('searchModal') searchModal: IonModal;
  @ViewChild('filterModal') filterModal: IonModal;
  @ViewChild('infoModal') infoModal: IonModal;
  @ViewChild('searchBar') searchBar!: IonInput;

  fecha = new Date().toISOString();

  expanded: number | null = null;
  multiProduct: boolean = false;
  productName: string;
  ippCompleta: boolean = false;
  ippContent: any = {};
  ippImage: string;
  textContent: any;
  product: any;
  user: any = [];
  uid: any;
  searchInProgress: boolean = false;
  userContent: any;
  medicamentoSugerido: any;
  slideVademecum: any;
  bannerSmall: any;
  bannerSmallSwitch: boolean = false;
  imagenesProd: ImagenDeProducto[] = [];
  vademecum: any;
  productos: any;
  medicamentos: any;
  categoria: any;
  udnPDF: any = [];
  strapiUrl: string = environment.strapiURL;
  textoBuscar: string = '';
  division: string = '';

  // Variables para filtros
  selectedDivision: string = '';
  selectedUDN: string = '';
  selectedFV: string = '';
  filteredProducts: any[] = [];

  role: string = '';
  arregloProductosPDF: any = [];
  searchBy: any = ['Nombre', 'fv'];
  filterBy: string = '';

  constructor(
    private strapi: StrapiService,
    private router: Router,
    private widgetUtilService: WidgetUtilService,
    private content: ContentService,
    private dataService: DataService,
    private browserService: BrowserService,
    private pdfGenerator: PdfgeneratorService,
    private platform: Platform,
    private interaction: InteractionService,
    private analyticsService: AnalyticsService,
    private alertController: AlertController,
    private cdr: ChangeDetectorRef,
  ) { }

  async ngOnInit() {
    await this.getUserInStorage();
    await this.getUserContent();
    await this.analyticsService.setCurrentScreen('Vademecum');
  }

  async ionViewWillLeave() {
    // Cerrar todos los modales abiertos
    await this.closeAllModals();
  }

  async closeAllModals() {
    // Cerrar modales si están abiertos
    if (this.filterModal) {
      await this.filterModal.dismiss();
    }
    if (this.infoModal) {
      await this.infoModal.dismiss();
    }
    if (this.searchModal) {
      await this.searchModal.dismiss();
    }
  }

  /**
   * Obtiene el usuario almacenado en preferencias y establece las propiedades del usuario.
   */
  async getUserInStorage() {
    const ret = await Preferences.get({ key: 'user' });
    if (ret.value !== null) {
      this.user = JSON.parse(ret.value);
      this.uid = this.user.uid;
      this.role = this.user.role;
    } else {
      this.user = { role: 'noRegistrado' };
    }
  }

  selecionado(event: string) {
    this.searchBy = event;
  }

  /**
   * Navega a la página del producto con el slug proporcionado.
   * @param slug - Slug del producto.
   */
  async openProduct(slug: string) {
    // Cerrar modales abiertos
    await this.closeAllModals();
    // Navegar al producto
    await this.router.navigate([`/productos/${slug}`]);
  }

  /**
   * Realiza una búsqueda de productos.
   * @param event - Evento del input de búsqueda.
   */
  buscar(event: any) {
    if (event.detail.value === '' || event.detail.value === null) {
      this.searchInProgress = false;
    } else {
      this.searchInProgress = true;
      this.textoBuscar = event.detail.value;
    }
  }

  /**
   * Limpia el texto de búsqueda.
   */
  limpiarBusqueda() {
    this.searchInProgress = false;
    this.textoBuscar = '';
  }

  /**
   * Refresca el contenido del vademécum.
   * @param event - Evento de refresco.
   */
  async vademecumRefresh(event: any) {
    const storageRawData: any = await Preferences.get({ key: 'user' });
    const userInStorage = await JSON.parse(storageRawData.value);
    await this.content.getUser(userInStorage);
    await this.content.getVademecum(300);
    await this.getUserContent();
    event.target.complete();
  }

  /**
   * Divide una descripción en líneas de una longitud máxima.
   * @param description - Descripción a dividir.
   * @param maxLineLength - Longitud máxima de cada línea.
   * @returns Arreglo de líneas de texto.
   */
  getLinesFromDescription(description: string, maxLineLength: number): string[] {
    const words = description.split(' ');
    let currentLine = '';
    let lines = [];

    for (let word of words) {
      if ((currentLine + word).length > maxLineLength) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = currentLine ? `${currentLine} ${word}` : word;
      }
    }

    lines.push(currentLine);

    while (lines.length < 3) {
      lines.push(' ');
    }

    return lines;
  }

  /**
   * Abre el modal de búsqueda y enfoca la barra de búsqueda.
   */
  async openSearchModal() {
    // Cerrar modales abiertos
    await this.closeAllModals();

    // Abrir modal de búsqueda
    await this.searchModal.present();
    await this.searchBar.setFocus();
  }

  async openFilterModal() {
    if (this.role === 'representante') {
      await this.closeAllModals();
      await this.filterModal.present();
    }
  }

  /**
   * Abre un navegador con la URL proporcionada.
   * @param url - URL a abrir en el navegador.
   */
  async openBrowser(url: string) {
    await this.browserService.openBrowser(url);
  }

  /**
   * Expande o colapsa una sección.
   * @param id - ID de la sección.
   */
  toggleSection(id: number) {
    this.expanded = this.expanded === id ? null : id;
  }

  /**
   * Cierra el modal de información.
   */
  async closeModal() {
    await this.infoModal.dismiss();
  }

  toggleProduct(product: any) {
    product.isSelected = !product.isSelected;

    const index = this.arregloProductosPDF.findIndex((item: { id: any }) => item.id === product.id);

    if (product.isSelected) {
      if (index === -1) {
        this.arregloProductosPDF.push({ id: product.id });
      }
    } else {
      if (index > -1) {
        this.arregloProductosPDF.splice(index, 1);
      }
    }
  }

  async seleccionarProducto(product: any) {
    const index = this.arregloProductosPDF.findIndex(p => p.id === product.id);

    if (index === -1) {
      this.arregloProductosPDF.push(product);
      product.isSelected = true;
    } else {
      this.arregloProductosPDF.splice(index, 1);
      product.isSelected = false;
    }
  }

  /**
   * Descarga un PDF generado a partir del contenido IPP.
   * - Utiliza condiciones específicas de la plataforma para determinar el método de descarga.
   * - Registra un evento de análisis para la descarga.
   * @param ippContent Contenido del IPP con propiedades como id y Nombre.
   */
  async descargarPDF(ippContent: any) {
    const data = await this.pdfGenerator.generateIpp([{ id: ippContent.id }]);
    const { blob, base64: base64OnlyData } = data;
    const fileName = this.interaction.slugify(ippContent.Nombre);
    await this.analyticsService.logEvent('ipp_descargada', { producto: ippContent.Nombre, file_name: `${fileName}.pdf` });

    if (this.platform.is('ios') || this.platform.is('android')) {
      await this.descargarPdfIOS(base64OnlyData, `${fileName}.pdf`);
    } else {
      await this.descargarPdfStandard(blob, `${fileName}.pdf`);
    }
  }

  async exportarPDF() {
    if (this.arregloProductosPDF.length === this.productos.length) {
      this.descargarCatalogoCompleto();
    } else {
      const exportPDF = await this.alertController.create({
        header: 'Exportar PDF',
        message: '¿Cómo quieres exportar tu selección?',
        backdropDismiss: false,
        cssClass: 'solicitarCita',
        buttons: [
          {
            text: 'Cancelar',
            role: 'cancel',
            handler: () => {
              exportPDF.dismiss();
            }
          },
          {
            text: 'IPP Completa',
            role: 'ippCompleta',
            handler: () => {
              this.ippCompleta = true;
              this.descargarFolleto();
            }
          },
          {
            text: 'Folleto',
            role: 'folleto',
            handler: () => {
              this.ippCompleta = false;
              this.descargarFolleto();
            }
          }
        ]
      });
      await exportPDF.present();
    }
  }

  /**
   * Descarga el catálogo completo en formato PDF.
   */
  async descargarCatalogoCompleto() {
    if (this.arregloProductosPDF.length > 0) {
      await this.widgetUtilService.presentLoadingMessage('Espera mientras se genera el pdf');
      const data = await this.pdfGenerator.catalogoCompleto();
      const { blob, base64: base64OnlyData } = data;
      const fileName = this.interaction.slugify('Catalogo-Sanfer-2024') + '-' + this.user.nEmpleado + '-' + this.fecha;
      await this.analyticsService.logEvent('catalogo-completo-descargado', { producto: 'Folleto Sanfer Conecta', file_name: `${fileName}.pdf` });

      if (this.platform.is('ios') || this.platform.is('android')) {
        await this.descargarPdfIOS(base64OnlyData, `${fileName}.pdf`);
        await this.widgetUtilService.dismissLoader();
      } else {
        await this.descargarPdfStandard(blob, `${fileName}.pdf`);
        await this.widgetUtilService.dismissLoader();
      }

      this.arregloProductosPDF = [];
    } else {
      console.log('arreglo vacío');
    }
  }

  /**
   * Descarga un folleto en formato PDF basado en el arreglo de productos y la elección de generación IPP.
   */
  async descargarFolleto() {
    if (this.arregloProductosPDF.length > 0) {
      await this.widgetUtilService.presentLoadingMessage('Espera mientras se genera el pdf');
      const data = this.ippCompleta ? await this.pdfGenerator.generarIPPCompleta(this.arregloProductosPDF) : await this.pdfGenerator.generarFolleto(this.arregloProductosPDF);
      const { blob, base64: base64OnlyData } = data;
      const fileName = this.interaction.slugify(this.ippCompleta ? 'IPP-Completa-Sanfer-Conecta' : 'Folleto-Sanfer-Conecta') + '-' + this.user.nEmpleado + '-' + this.fecha;
      await this.analyticsService.logEvent('ipp_descargada', { producto: 'Folleto Sanfer Conecta', file_name: `${fileName}.pdf` });

      if (this.platform.is('ios') || this.platform.is('android')) {
        await this.descargarPdfIOS(base64OnlyData, `${fileName}.pdf`);
        await this.widgetUtilService.dismissLoader();
      } else {
        await this.descargarPdfStandard(blob, `${fileName}.pdf`);
        await this.widgetUtilService.dismissLoader();
      }

      this.arregloProductosPDF = [];
    } else {
      console.log('arreglo vacío');
    }
  }

  // Getter para verificar si todos los productos filtrados están seleccionados
  get allFilteredSelected(): boolean {
    return this.filteredProducts && this.filteredProducts.length > 0 && this.filteredProducts.every(p => p.isSelected);
  }

  // Método para seleccionar todos los productos filtrados
  async selectAllProducts() {
    this.filteredProducts.forEach(product => {
      if (!product.isSelected) {
        product.isSelected = true;
        const index = this.arregloProductosPDF.findIndex(item => item.id === product.id);
        if (index === -1) {
          this.arregloProductosPDF.push({ id: product.id });
        }
      }
    });
    this.cdr.detectChanges();

    // Verificar si todos los productos están seleccionados
    if (this.arregloProductosPDF.length === this.productos.length) {
      await this.showAllProductsSelectedAlert();
    }
  }

  // Método para deseleccionar todos los productos filtrados
  deselectAllProducts() {
    this.filteredProducts.forEach(product => {
      if (product.isSelected) {
        product.isSelected = false;
      }
    });
    // Elimina de arregloProductosPDF los productos que están en filteredProducts
    this.arregloProductosPDF = this.arregloProductosPDF.filter(item => !this.filteredProducts.some(p => p.id === item.id));
    this.cdr.detectChanges();
  }

  /**
   * Función estándar para descargar PDF en plataformas que no son iOS o Android.
   * - Crea un enlace temporal para la descarga del archivo y lo elimina después de usarlo.
   * @param blob El objeto Blob que contiene los datos del PDF.
   * @param fileName El nombre del archivo bajo el cual se guardará.
   */
  private async descargarPdfStandard(blob: Blob, fileName: string) {
    try {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName; // Nombre del archivo
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
    } catch (error) {
      console.error('Error al descargar el PDF:', error);
    }
  }

  async descargarPdfIOS(base64OnlyData: any, fileName: string) {
    try {
      const writeFileResponse = await Filesystem.writeFile({
        path: fileName,
        data: base64OnlyData,
        directory: Directory.Documents,
        recursive: true
      });

      const uri = writeFileResponse.uri;

      await Share.share({
        title: 'Compartir PDF',
        url: uri,
        dialogTitle: 'Compartir PDF con',
      });

      console.log('Archivo PDF guardado y compartido con éxito: ', uri);
    } catch (error) {
      console.error('Error al guardar y abrir el PDF en iOS: ', error);
    }
  }

  async setInteraction(product) {
    const data = {
      titulo: product.Nombre,
      aditionalData: product.PrincipioActivo,
      imagen: product.Imagen_de_producto[0].url,
      type: 'medicamento'
    };

    const slug = this.interaction.slugify(product.Nombre);
    await this.interaction.registrarInteraccion(this.uid, `/productos/${slug}`, data);
  }

  async openInfoModal(info: any) {
    // Cerrar modales abiertos
    await this.closeAllModals();

    // Configurar y abrir el modal de información
    this.ippContent = info;
    await this.setInteraction(info);

    this.textContent = undefined;
    if (this.ippContent.description !== null && this.ippContent.description !== undefined) {
      this.textContent = marked(this.ippContent.description);
    }
    if (info.Imagen_de_producto) {
      this.ippImage = this.strapiUrl + this.ippContent.Imagen_de_producto[0].url;
      console.log('Se agregó ippImage', this.ippImage);
    } else {
      this.ippImage = null;
      console.log('ippImage Null', this.ippImage);
    }

    await this.infoModal.present();
  }

  async newArrayOfProducts(data: any) {
    const newArray: any = [];
    console.log('Data recibida en el iterador:', data);

    for (let product of data) {
      newArray.push({
        medicamentos: product.medicamentos[0],
        Nombre: product.producto,
        id: product.id,
        slug: product.slug,
        division: product.division,
        fv: product.fv,
        udn: product.udn,
        isSelected: false // Inicializar isSelected
      });
    }

    return newArray;
  }

  // Funciones de manejo de cambios para los selectores
  handleDivisionChange(event: any) {
    this.selectedDivision = event.detail.value;
    this.applyFilters();
  }

  handleUDNChange(event: any) {
    this.selectedUDN = event.detail.value;
    this.applyFilters();
  }

  handleFVChange(event: any) {
    this.selectedFV = event.detail.value;
    this.applyFilters();
  }

  // Función para aplicar los filtros a los productos
  applyFilters() {
    this.filteredProducts = this.productos.filter(product => {
      return (
        (this.selectedDivision ? product.division === this.selectedDivision : true) &&
        (this.selectedUDN ? product.udn === this.selectedUDN : true) &&
        (this.selectedFV ? product.fv === this.selectedFV : true)
      );
    });
  }

  // Función para resetear los filtros
  async resetFilters() {
    this.selectedDivision = '';
    this.selectedUDN = '';
    this.selectedFV = '';
    this.filteredProducts = this.productos;
    await this.filterModal.dismiss();
  }

  // Función para determinar si hay algún filtro aplicado
  hasFilters() {
    return this.selectedDivision !== '' || this.selectedUDN !== '' || this.selectedFV !== '';
  }

  // Método para inicializar los datos
  async getUserContent() {
    await this.widgetUtilService.presentLoading();

    if (this.user.role !== 'noRegistrado') {
      const vademecumContent = await Preferences.get({ key: 'vademecum' });
      this.vademecum = await JSON.parse(vademecumContent.value);
      this.productos = await this.newArrayOfProducts(this.vademecum);
      this.filteredProducts = this.productos; // Inicialmente muestra todos los productos

      console.log('Productos completos: ', this.productos);

      console.log('This Vademecum: ', this.vademecum);

      if (this.vademecum) {
        const userContent = await Preferences.get({ key: 'userContent' });
        console.log('User content antes de categoría', userContent);

        this.userContent = await JSON.parse(userContent.value);
        const banners = this.userContent.banners;
        console.log('Banners antes de categoría', banners);
        this.categoria = banners.categoria;
        this.medicamentoSugerido = banners.medicamentos;

        for (let sugerido of this.medicamentoSugerido) {
          this.udnPDF.push({ id: sugerido.id });
        }

        const bannersFiltrados = banners.banners.filter((resp: any) => resp.location === 'vademecum');
        this.slideVademecum = bannersFiltrados;

        await this.widgetUtilService.dismissLoader();
      } else {
        await this.content.getVademecum(350);
        const vademecumContent = await Preferences.get({ key: 'vademecum' });
        this.vademecum = await JSON.parse(vademecumContent.value);
        await this.widgetUtilService.dismissLoader();
      }
    } else {
      this.vademecum = await this.content.getVademecum(300);
      this.productos = await this.newArrayOfProducts(this.vademecum);
      this.filteredProducts = this.productos; // Inicialmente muestra todos los productos
      await this.widgetUtilService.dismissLoader();
    }
  }

  async showAllProductsSelectedAlert() {
    const header = 'Todos los productos seleccionados';
    const message = 'Has seleccionado todos los productos. ¿Deseas exportar el catálogo completo de productos del vademécum, deseleccionar todos los productos, o cerrar esta alerta?';
    const buttons = [
      {
        text: 'Cerrar',
        role: 'cancel',
        cssClass: 'alert-button-close', // Añadimos una clase CSS para destacar el botón
        handler: () => {
          // No se realiza ninguna acción, simplemente se cierra la alerta
        }
      },
      {
        text: 'Deseleccionar Todos',
        handler: () => {
          this.deselectAllProducts();
        }
      },
      {
        text: 'Exportar PDF',
        handler: () => {
          this.exportarPDF();
        }
      }
    ];

    await this.widgetUtilService.presentAlertWithOptions(header, message, buttons);
  }

  onBackButtonClick() {
    // Cerrar modales abiertos
    this.closeAllModals();
  }
}
