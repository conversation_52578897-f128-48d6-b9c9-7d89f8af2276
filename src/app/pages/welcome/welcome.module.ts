import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WellcomePageRoutingModule } from './welcome-routing.module';

import { WellcomePage } from './welcome.page';
import {NgArrayPipesModule, NgPipesModule} from "ngx-pipes";

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        WellcomePageRoutingModule,
        NgArrayPipesModule,
        NgPipesModule
    ],
  declarations: [WellcomePage],
  schemas:[CUSTOM_ELEMENTS_SCHEMA]

})
export class WellcomePageModule {}
