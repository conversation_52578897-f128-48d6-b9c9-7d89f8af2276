import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'haceTiempo'
})
export class HaceTiempoPipe implements PipeTransform {

  transform(value: any): string {
    if (!value) return 'Fecha inválida';

    let date: Date;

    // Revisar si el valor es un objeto con las propiedades 'seconds' y 'nanoseconds'
    if ('seconds' in value && 'nanoseconds' in value) {
      date = new Date(value.seconds * 1000 + value.nanoseconds / 1000000);
    } else if (value instanceof Date) {
      date = value;
    } else {
      date = new Date(value);
    }

    const now = new Date();
    const differenceInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // Aquí sigue la misma lógica que antes para calcular el tiempo transcurrido
    if (differenceInSeconds < 30) {
      return 'Ahora mismo';
    } else if (differenceInSeconds < 60) {
        return 'Unos segundos';
    } else if (differenceInSeconds < 600) {
      return 'Unos minutos';
    } else if (differenceInSeconds < 3600) { // Menos de 1 hora
      const minutos = Math.floor(differenceInSeconds / 60);
      return `${minutos} ${minutos === 1 ? 'minuto' : 'minutos'}`;
    } else if (differenceInSeconds < 86400) { // Menos de 1 día
      const horas = Math.floor(differenceInSeconds / 3600);
      return `${horas} ${horas === 1 ? 'hora' : 'horas'}`;
    } else if (differenceInSeconds < 2592000) { // Menos de 30 días
      const dias = Math.floor(differenceInSeconds / 86400);
      return `${dias} ${dias === 1 ? 'día' : 'días'}`;
    } else if (differenceInSeconds < 31536000) { // Menos de 1 año
      const meses = Math.floor(differenceInSeconds / 2592000);
      return `${meses} ${meses === 1 ? 'mes' : 'meses'}`;
    } else {
      const anos = Math.floor(differenceInSeconds / 31536000);
      return `${anos} ${anos === 1 ? 'año' : 'años'}`;
    }
  }


}
