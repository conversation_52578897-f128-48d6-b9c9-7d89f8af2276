import { Pipe, PipeTransform } from '@angular/core';
import {marked} from "marked";
import DOMPurify from 'dompurify';

@Pipe({
  name: 'mark'
})
export class MarkPipe implements PipeTransform {

  /**
   * Transforms Markdown text to sanitized HTML.
   *
   * @param value The Markdown string to be transformed.
   * @returns A sanitized HTML string.
   */
  transform(value: string): string {
    if (!value) return '';

    // Convert Markdown to HTML
    const dirtyHtml = marked(value);

    // Sanitize the HTML
    return DOMPurify.sanitize(dirtyHtml);
  }
}
