import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'multiFilter'
})
export class MultiFilterPipe implements PipeTransform {
  transform(items: any[], searchText: string, fields: string[]): any[] {
    if (!items) return [];
    if (!searchText) return items;

    const words = searchText.toLowerCase().split(' ');

    return items.filter(item => {
      return words.every(word =>
        fields.some(field =>
          item[field] && item[field].toLowerCase().includes(word)
        )
      );
    });
  }

}
