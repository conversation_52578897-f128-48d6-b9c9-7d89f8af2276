<ion-content class="ion-padding">
  <ion-list>
    <ion-item *ngFor="let notification of notifications" (click)="openBrowser(notification.data.url)">
      <ion-avatar  aria-hidden="true" slot="start" class="ion-no-padding ion-no-margin">
        <ion-icon name="notifications-circle-outline" color="primary" size="large"></ion-icon>
      </ion-avatar>
      <ion-label class="ion-text-wrap">
        <h3>{{notification.title}}</h3>
        <h5>{{notification.subtitle}}</h5>
        <p>{{notification.body}}</p>
      </ion-label>
    </ion-item>
  </ion-list>
  <ion-button (click)="borrarNotificaciones()" expand="block" size="small">Borrar todas</ion-button>
</ion-content>

