import { Component, OnInit } from '@angular/core';
import {PushNotificationService} from "../../services/push-notification.service";
import {<PERSON><PERSON><PERSON><PERSON>roller, PopoverController} from "@ionic/angular";
import {BrowserService} from "../../services/browser.service";

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.page.html',
  styleUrls: ['./notifications.page.scss'],
})
export class NotificationsPage implements OnInit {

  notifications: any;
  constructor(
    private pns: PushNotificationService,
    private popoverController: PopoverController,
    private browser: BrowserService,

  ) { }

  async ngOnInit() {
    const token = await this.pns.getToken();
    console.log('Token', token);
    this.notifications = await this.pns.getDeliveredNotifications();
    console.log('Notificaciones', this.notifications);
  }

  async borrarNotificaciones() {
    await this.pns.removeAllDeliveredNotifications();
    await this.popoverController.dismiss()


  }
  async openBrowser(url:string) {
    await this.popoverController.dismiss()
    await this.browser.openBrowser(url)
  }

}
