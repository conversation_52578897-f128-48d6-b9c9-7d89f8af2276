import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Firestore, collection, doc, addDoc, query, orderBy, collectionData, setDoc, serverTimestamp } from '@angular/fire/firestore';
import { first } from 'rxjs/operators';
import {firstValueFrom, Observable} from "rxjs";
import {Preferences} from "@capacitor/preferences";

@Injectable({
  providedIn: 'root'
})
export class AcademyCommentService {
  constructor(private firestore: Firestore) {}



  async agregarComentario(category: string, slug: string, uuid: string, user: any, comentario: any) {

    const comentariosRef = collection(this.firestore, `academia/cursos/${category}/${slug}/comentarios`);

    // Asignar ID único y timestamp

    // comentario.id = this.firestore.createId();

    const userData = {
      nombre: user.nombre,
      apellido: user.apellido,
      isAdmin: user.isAdmin,
      correo: user.correo,
      uid: user.uid,
      role: user.role,

    }

    const data = {
      comentario,
      uuid: uuid,
      user: userData,
      timestamp: serverTimestamp()
    }
    await addDoc(comentariosRef, data);
  }

  obtenerComentarios(category: string, slug: string): Observable<any[]> {
    // Referencia a la colección 'comentarios' dentro del documento específico
    const comentariosRef = collection(this.firestore, `academia/cursos/${category}/${slug}/comentarios`);

    // Crear una consulta ordenada por 'timestamp'
    const comentariosQuery = query(comentariosRef, orderBy('timestamp'));

    // Utilizar collectionData para obtener los cambios en tiempo real
    return collectionData(comentariosQuery, { idField: 'id' }) as Observable<any[]>;
  }

  async responderAComentario(comentarioId: string, respuesta: any): Promise<void> {
    // Referencia a la colección 'respuestas' dentro del comentario específico
    const respuestasRef = collection(this.firestore, `comentarios/${comentarioId}/respuestas`);

    // Generar un nuevo ID para la respuesta
    const newRespuestaRef = doc(respuestasRef);
    const newId = newRespuestaRef.id;

    // Agregar los campos 'id' y 'timestamp' a la respuesta
    respuesta.id = newId;
    respuesta.timestamp = serverTimestamp();

    // Guardar la respuesta en Firestore
    await setDoc(newRespuestaRef, respuesta);
  }
}
