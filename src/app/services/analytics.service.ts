import { Injectable } from '@angular/core';
import { FirebaseAnalytics } from '@capacitor-firebase/analytics';
import { SegmentService } from './segment.service';
import { AnalyticsBrowser } from '@segment/analytics-next';

@Injectable({
  providedIn: 'root'
})
export class AnalyticsService {
  segmentAnalytics: AnalyticsBrowser;

  constructor( private segmentService: SegmentService
  ) {
    this.segmentAnalytics = this.segmentService.getAnalytics();
  }
  public appInstanceId = '';


  async getAppInstance() {
    FirebaseAnalytics.getAppInstanceId().then((result) => {
      this.appInstanceId = result.appInstanceId || '';
      return this.appInstanceId
    });
  }
  async setUserId(userId: string): Promise<void> {
    await FirebaseAnalytics.setUserId({ userId });

  }


  async setUserProperty(key: string, value: string): Promise<void> {
    await FirebaseAnalytics.setUserProperty({ key, value });
  }

  async setCurrentScreen(screenName: string, screenClassOverride?: string): Promise<void> {
    await FirebaseAnalytics.setCurrentScreen({ screenName, screenClassOverride });
    await this.segmentAnalytics.page(screenName)

  }

  async logEvent(name: string, params?: { [key: string]: any }): Promise<void> {
    await FirebaseAnalytics.logEvent({ name, params });
  }

  async setSessionTimeoutDuration(duration: number): Promise<void> {
    await FirebaseAnalytics.setSessionTimeoutDuration({ duration });
  }

  async setEnabled(enabled: boolean): Promise<void> {
    await FirebaseAnalytics.setEnabled({ enabled });
  }

  async isEnabled(): Promise<boolean> {
    const { enabled } = await FirebaseAnalytics.isEnabled();
    return enabled;
  }

  async resetAnalyticsData(): Promise<void> {
    await FirebaseAnalytics.resetAnalyticsData();
  }
}
