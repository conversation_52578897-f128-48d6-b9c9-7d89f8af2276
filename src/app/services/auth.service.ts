import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import {FirebaseAuthentication} from "@capacitor-firebase/authentication";
import {getAuth, signOut} from "@angular/fire/auth";
import {Preferences} from "@capacitor/preferences";
import {AnalyticsService} from "./analytics.service";

import { SegmentService } from './segment.service';
import { AnalyticsBrowser } from '@segment/analytics-next';


@Injectable({
  providedIn: 'root',
})
export class AuthService {

  segmentAnalytics: AnalyticsBrowser;

  constructor(
    private router: Router,
    private analyticsService: AnalyticsService,
    private segmentService: SegmentService
  ) {
    this.segmentAnalytics = this.segmentService.getAnalytics();

  }


  async signOut() {

    // 1. Sign out on the native layer
    await FirebaseAuthentication.signOut();
    // 2. Sign out on the web layer
    const auth = getAuth();
    await signOut(auth);
    // 3. Remove the local storage
    await Preferences.clear();
    await Preferences.remove({key: 'user'});
    // 4. Navigate to the Home page
    await this.router.navigate(['home'], { replaceUrl: true });
    // Register analitycs
    await this.analyticsService.logEvent('logout');
    await this.segmentAnalytics.track('logout')


  }



}
