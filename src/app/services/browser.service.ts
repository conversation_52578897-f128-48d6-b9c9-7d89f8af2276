import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Browser } from '@capacitor/browser';
import { WidgetUtilService } from './widget-util.service';

@Injectable({
  providedIn: 'root'
})
export class BrowserService {

  constructor(private router: Router, private widgetUtilService: WidgetUtilService) { }

  async openBrowser(url: string): Promise<void> {
    // console.log(url);
    if (!url) {
      await this.widgetUtilService.presentToastError('No hay link');
      return;
    }

    if (url.includes('http')) {
      // console.log('Link con http');
      try {
        await Browser.open({ url, toolbarColor: '#ff2836' });
      } catch (error) {
        console.error('Error al abrir el enlace externo', error);
        await this.widgetUtilService.presentToastError('Error al abrir el enlace');
      }
    } else {
      // console.log('Link sin http');
      this.router.navigateByUrl(url).catch(err => {
        console.error('Error al navegar a la ruta interna', err);
        this.widgetUtilService.presentToastError('Error en la navegación interna');
      });
    }
  }
}
