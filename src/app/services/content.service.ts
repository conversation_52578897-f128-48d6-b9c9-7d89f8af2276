import { Injectable } from '@angular/core';
import {environment} from "../../environments/environment";
import {CapacitorHttp, HttpResponse} from '@capacitor/core';
import {Preferences} from "@capacitor/preferences";
import {AnalyticsService} from "./analytics.service";

@Injectable({
  providedIn: 'root'
})
export class ContentService {

  private strapiUrl = environment.strapiURL;

  homeContent: any
  constructor(
    private analyticsService: AnalyticsService,

  ) {

  }


  async getAllContent () {
    const news = await this.getNews(1000);
    const vademecum = await this.getVademecum(350);
    const models = await this.get3dModels(350);
    const academy = await this.getAcademy(350);
    // const springer = await this.getSpringer(350);
    const slides = await this.getSlides(350);
    const banners = await this.getBanners(350);
    const specialties = await this.getSpecialties(350);




    const allContent = {
      news,
      vademecum,
      models,
      academy,
      // springers
      slides,
      banners,
      specialties
    }

    return allContent
  }


  // Obtener el contenido de strapi
  strapiGet = async (path: string, queryParams?: string) => {
    const options = {
      url: `${this.strapiUrl}/${path}${queryParams ? '?' + queryParams : ''}`
    };
    // console.log('ruta de consulta: ', options)
    const response: HttpResponse = await CapacitorHttp.get(options);
    return response
  };

  // Realizar un post request para el contenido de strapi

  strapiPost = async (path: string, queryParams?: string) => {
    const options = {
      url: `${this.strapiUrl}/${path}${queryParams ? '?' + queryParams : ''}`,
      // headers: { 'X-Fake-Header': 'Fake-Value' },
      data: { foo: 'bar' },
    };
    const response: HttpResponse = await CapacitorHttp.post(options);
  };


  async getUser(userInStorage: any) {

    switch (userInStorage.role) {
      case undefined:
        // this.oldUser(userInStorage);
        break;

      case null:
        // this.oldUser(userInStorage);
        break;
      case 'noMedico':
        console.log('El role del usuario es noMedico');
        break;
      case 'medico':
        console.log('El role del usuario es Médico');
        console.log('Medico detectado');
        break;
      case 'representante':
        console.log('El role del usuario es representante');
        break;
      case 'productividad':
        console.log('El role del usuario es productividad');
        break;
      case 'administrador':
        console.log('El role del usuario es administrador');
        break;
    }



    const titulo = userInStorage.titulo;
    // console.log('Medico con titulo: ', titulo);
    const specialities = await this.getSpecialties(500);

    const especialidad = specialities.tituloProfesional.filter((resp: any) => {
      // console.log(targetItemID.toString())
      return resp.titulo === titulo;
    });


    console.log('Epecialidad para medicos : ', especialidad);




    const especialidadBanner = especialidad[0].Banner;

    // console.log('Epecialidad del banner', especialidadBanner);
    //
    await Preferences.set({
      key: 'especialidadBanner',
      value: JSON.stringify(especialidadBanner)
    });


    const banners = await this.strapiGet('categorias', `id=${especialidadBanner.id}` )

    // console.log('Categoria obtenida: ', categoria);

    const userContent = {
      banners: banners.data[0],

    }
    await this.analyticsService.logEvent('especialidad_sanfer_medico', {especialidad_sanfer: userContent.banners.categoria});

    console.log('User content : ', userContent);

    await Preferences.set({
      key: 'userContent',
      value: JSON.stringify(userContent)
    });

    // await this.getVademecum(350);
    // await this.get3dModels(350);

    return userContent

  }


  async getSanferEmployeeData(categoryId: string) {
    const banners = await this.strapiGet('categorias', `id=${categoryId}` )
    const userContent = {
      banners: banners.data[0],
    }
    await Preferences.set({
      key: 'userContent',
      value: JSON.stringify(userContent)
    });
    return userContent

  }



  async getWelcomeSlides(limit: number) {
    const wellcomeSlides = await this.strapiGet('bienvenidas', `_limit=${limit}`)
    // console.log('Retorno de las welcome slides: ', wellcomeSlides)
    // await Preferences.set({
    //   key: 'wellcomeSlides',
    //   value: JSON.stringify(wellcomeSlides.data)
    // });
    return wellcomeSlides.data
  }



  async getNews(limit: number) {
    const categorias = await this.strapiGet('categorias-de-noticias', `_limit=${limit}`);
    const noticias = await this.strapiGet('noticias', '_limit=350');
    // console.log('Retorno de las noticias: ', categorias)
    const news = {
      noticias: noticias.data,
      categorias: categorias.data
    }
    // await Preferences.set({
    //   key: 'news',
    //   value: JSON.stringify(news)
    // });
    return news
  }
  async getNSinglenews(limit: number) {
    const categorias = await this.strapiGet('categorias-de-noticias', `_limit=${limit}`);
    const noticias = await this.strapiGet('noticias', '_limit=350');
    // console.log('Retorno de las noticias: ', categorias)
    const news = {
      noticias: noticias.data,
      categorias: categorias.data
    }
    // await Preferences.set({
    //   key: 'news',
    //   value: JSON.stringify(news)
    // });
    return news
  }

  async getVademecum (limit: number) {
    const vademecum = await this.strapiGet('vademecums', `_limit=${limit}`);
    await Preferences.set({
      key: 'vademecum',
      value: JSON.stringify(vademecum.data)
    });
    // console.log('Vademecum cargado en el storage', vademecum)
    return vademecum.data

  }

  async getMedicamentos (limit: number) {
    const medicamentos = await this.strapiGet('medicamentos', `_limit=${limit}`);
    // await Preferences.set({
    //   key: 'medicamentos',
    //   value: JSON.stringify(medicamentos.data)
    // });
    // console.log('Vademecum cargado en el storage', medicamentos)
    return medicamentos.data

  }

  async get3dModels (limit: number) {
    const modelos = await this.strapiGet('modelos-anatomicos-3-ds', `_limit=${limit}`);
    // console.log('Modelos 3d importados de strapi', modelos)
    await Preferences.set({
      key: 'modelos',
      value: JSON.stringify(modelos.data)
    });
    return modelos.data

  }

  async getAcademy (limit: number) {

    const categorias = await this.strapiGet('categorias-de-cursos', `_limit=${limit}`);
    // const cursos = await this.strapiGet('cursos', `_limit=${limit}`);
    const academy = {
      categorias: categorias.data,
      // cursos: cursos.data
    }
    await Preferences.set({
      key: 'categorias-de-cursos',
      value: JSON.stringify(categorias.data)
    });

    console.log('Datos de la academia: ', academy)
    return academy
  }

  async getSpringer (limit: number) {

  }

  async getSlides (limit: number) {

    const slides = await this.strapiGet('slides', `_limit=${limit}`);

    // await Preferences.set({
    //   key: 'slides',
    //   value: JSON.stringify(slides)
    // });
    return slides.data
  }

  async getBanners (limit: number) {

    const banners = await this.strapiGet('categorias', `_limit=${limit}`);

    // await Preferences.set({
    //   key: 'banners',
    //   value: JSON.stringify(banners)
    // });
    return banners.data
  }


  async getSpecialties (limit: number) {

    const specialties = await this.strapiGet('especialidades', `_limit=${limit}`);

    // console.log('Especialidades: ', specialties)
    // await Preferences.set({
    //   key: 'especialidades',
    //   value: JSON.stringify(specialties)
    // });
    return specialties.data
  }

  async getCategory (limit: number) {
    const categories = await this.strapiGet('categorias', `_limit=${limit}`);
    return categories.data
  }


  async reloadConent() {
    await this.getAllContent();
  }


}
