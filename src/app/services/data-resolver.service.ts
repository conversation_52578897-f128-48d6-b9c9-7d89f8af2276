import { Injectable } from '@angular/core';
import {ActivatedRouteSnapshot, Resolve} from '@angular/router';
import {StrapiService} from './strapi.service';

@Injectable({
  providedIn: 'root'
})
export class DataResolverService implements Resolve<any>{

  constructor(private strapi: StrapiService) { }

  resolve(route: ActivatedRouteSnapshot){
    const id = route.paramMap.get('id');
    return this.strapi.getData(id);
  }

}
