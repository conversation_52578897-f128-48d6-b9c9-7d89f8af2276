import { Injectable } from '@angular/core';
import {CapacitorHttp, HttpResponse} from "@capacitor/core";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class EmailService {

  SendGridAPI = environment.sendGridAPI;

  constructor() { }


  async sendEmail(htmlTemplate: any, emailTo: any, subject: any) {
    const options = {
      url: `https://api.sendgrid.com/v3/mail/send`,
      headers: {
        ['authorization']: `Bearer ${this.SendGridAPI}`,
        ['Content-Type']: 'application/json'
      },
      data: {
        personalizations: [
          {
            to: [
              {email: emailTo}
            ]
          }
        ],
        from: {
          email: '<EMAIL>',
          name: '<PERSON><PERSON> Conecta'
        },
        subject,
        content: [
          {
            type: 'text/html',
            value: htmlTemplate
          }
        ]
      }
    };
    const response: HttpResponse = await CapacitorHttp.post(options);
    return response
  }

  enviarReporteFarmacovigilancia = async (user: any, data: any, emailTo: string) => {
    const htmlTemplate = this.reporteFarmacovigilancia(user, data);
    await this.sendEmail(htmlTemplate, emailTo, 'Reporte de farmacovigilancia')
  };



  private reporteFarmacovigilancia(user, data): string {
    return `
  <!DOCTYPE html>
  <html>
  <body>
    <img src="http://cdn.mcauto-images-production.sendgrid.net/9ca8404a3798f71d/5e4c3738-68ad-4e58-9c20-f89457b4ece2/300x122.png"/>
    <h1>Reporte de Farmacovigilancia</h1>
    <h3>Datos del Médico</h3>
    <p>El médico ${user.nombre} ${user.apellido}</p>
    <p>Con cédula profesional: ${user.cedula}</p>
    <p>Su correo es: ${user.correo}</p>
    <p>Su teléfono es: ${user.telefono}</p>
    <p>Teléfono Alterno: ${data.telefonoAlterno}</p>
    <p>Correo Alterno: ${data.correoAlterno}</p>

    <h3>Su reporte es el siguiente:</h3>
    <p>Nombre del paciente: ${data.nombrePaciente}</p>
    <p>Fecha de nacimiento: ${data.fechaNacimiento}</p>
    <p>Genero: ${data.genero}</p>
    <p>Peso: ${data.peso}</p>
    <p>Talla: ${data.talla}</p>
    <p>Descripción de la sospecha de reacción adversa al medicamento (SRAM): ${data.descripcionSospecha}</p>
    <p>La toma fue por: ${data.indicarSiEs}</p>

    <h3>¿Cuánto tiempo se manifestó la SRAM?</h3>
    <p>Fecha de Inicio: ${data.fechaInicio}</p>
    <p>Fecha de Termino: ${data.fechaTermino}</p>
    <p>Se diluyó la dosis: ${data.disminuyoDosis}</p>
    <p>Se Readministró medicamento: ${data.readministroMedicamento}</p>
    <p>Se recuperó el paciente de la SRAM: ${data.recuperoSRAM}</p>
    <p>Se cambió la farmacoterapia: ${data.cambioFarmacoterapia}</p>
    <p>Suspendió el medicamento debido a la SRAM: ${data.suspendioMedicamento}</p>
    <p>Hubo mejoría al suspender el medicamento: ${data.mejoriaSuspender}</p>
    <p>Tomó algún medicamento para tratar la SRAM: ${data.medicamentoTratarSRAM}</p>
    <p>Cuando se readministró el medicamento, ¿se presentó nuevamente la SRAM?: ${data.presentoNuevamenteSRAM}</p>

    <h3>Datos del medicamento sospechoso:</h3>
    <p>Nombre del medicamento: ${data.nombreComercial}</p>
    <p>Forma Farmacéutica: ${data.formaFarmaceutica}</p>
    <p>Nombre Genérico: ${data.nombreGenerico}</p>
    <p>Número de Lote: ${data.numLote}</p>
    <p>Vía de administración: ${data.viaAdministracion}</p>
    <p>Fecha de caducidad: ${data.caducidad}</p>
    <p>¿Para qué enfermedad la tomó? ${data.paraQueEnfermedad}</p>
    <p>Dosis: ${data.dosis}</p>
    <p>Horario: ${data.horario}</p>
    <p>Tenía tratamiento con algún otro medicamento: ${data.otroMedicamento}</p>
    <p>¿Antecedentes de alergias? ${data.antecedentesAlergias}</p>
    <p>Que alergias: ${data.alergias}</p>
    <p>Datos relevantes de la historia clínica del paciente: ${data.datosRelevantes}</p>
    <p>Relación con la condición: ${data.condicionMedica}</p>
    <p>Quiere ser contactado: ${data.contacto}</p>
    <p>Acepto el aviso de privacidad: ${data.avisoPrivacidad}</p>
  </body>
  </html>
  `;
  }
}
