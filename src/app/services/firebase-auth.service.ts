import { Injectable } from '@angular/core';
import {
  Auth,
  createUserWithEmailAndPassword,
  sendEmailVerification,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  authState
} from '@angular/fire/auth';
import {Observable} from 'rxjs';

import { FirebaseAuthentication } from '@capacitor-firebase/authentication';


@Injectable({
  providedIn: 'root'
})
export class FirebaseAuthService {
  constructor(private auth: Auth) {}

  async registerWithEmailPassword(email: string, password: string) {
    try {
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
      await sendEmailVerification(userCredential.user);
      return userCredential;
    } catch (error: any) {
      throw new Error(error);
    }
  }

  async loginWithEmailPassword(email: string, password: string) {
    try {
      const userCredential: any = await FirebaseAuthentication.signInWithEmailAndPassword({email, password})
        // await signInWithEmailAndPassword(this.auth, email, password);
      return userCredential;
    } catch (error: any) {
      throw new Error(error);
    }
  }


  async logout() {
    try {
      await FirebaseAuthentication.signOut();

      // await signOut(this.auth);
    } catch (error: any) {
      throw new Error(error);
    }
  }

  async recover(email: string) {
    try {
      await sendPasswordResetEmail(this.auth, email);
    } catch (error: any) {
      throw new Error(error);
    }
  }

  getAuthState(): Observable<any> {
    return authState(this.auth);
  }

  // Other methods...
}
