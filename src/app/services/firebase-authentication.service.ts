import { Injectable } from '@angular/core';
import {Capacitor} from "@capacitor/core";
import {getApp, initializeApp} from "@angular/fire/app";
import {getAuth, indexedDBLocalPersistence, initializeAuth} from "@angular/fire/auth";

@Injectable({
  providedIn: 'root'
})
export class FirebaseAuthenticationService {

  constructor(
  ) {}
  getFirebaseAuth = async () => {
    if (Capacitor.isNativePlatform()) {
      console.log('Autenticacion Nativa IOS o Android')

      return initializeAuth(getApp(), {
        persistence: indexedDBLocalPersistence,
      });
    } else {
      console.log('Autenticacion web')
      return getAuth();
    }
  };

}
