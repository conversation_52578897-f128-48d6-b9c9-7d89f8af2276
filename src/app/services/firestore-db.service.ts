import { Injectable } from '@angular/core';
import {
    Firestore,
    collection,
    doc,
    getDocs,
    query,
    collectionData,
    docData, addDoc, where, setDoc, getDoc, deleteDoc, updateDoc
} from '@angular/fire/firestore';
import { map } from 'rxjs/operators';

export interface Star {
    userId: any;
    movieId: any;
    value: number;
    slug: any;
}

@Injectable({
    providedIn: 'root'
})
export class FirestoreDbService {

    constructor(private firestore: Firestore) { }

    // Function to get all data from a collection
    getAllData(collectionId: string) {
        const collectionRef = collection(this.firestore, collectionId);
        return collectionData(collectionRef, { idField: 'id' });
    }

    // Function to get all data from a subcollection
    getAllDataInside(collectionId: string, documentId: string, collectionChild: string) {
        const collectionRef = collection(this.firestore, `${collectionId}/${documentId}/${collectionChild}`);
        return collectionData(collectionRef, { idField: 'id' });
    }




    getAllDataInsideCollection(collectionId: any, documentId: any, collectionChild: any, documentNested: any, collectionNested: any) {
        const nestedCollectionRef = collection(this.firestore, `${collectionId}/${documentId}/${collectionChild}/${documentNested}/${collectionNested}`);
        return collectionData(nestedCollectionRef, { idField: 'id' });
    }

    async getFecha(collectionId: string) {
        try {
            const collectionRef = collection(this.firestore, collectionId);
            const querySnapshot = await getDocs(collectionRef);
            return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        } catch (error: any) {
            throw new Error(error.message);
        }
    }

    getAllDataChild(collectionId: string, documentId: string, childCollection: string) {
        const childCollectionRef = collection(this.firestore, `${collectionId}/${documentId}/${childCollection}`);
        return collectionData(childCollectionRef, { idField: 'id' });
    }

    async snapshotAll(collectionId: string) {
        try {
            const collectionRef = collection(this.firestore, collectionId);
            const querySnapshot = await getDocs(collectionRef);
            return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        } catch (error: any) {
            throw new Error(error.message);
        }
    }

    async insertData(collectionId: string, data: any) {
        try {
            const collectionRef = collection(this.firestore, collectionId);
            const docRef = await addDoc(collectionRef, data);
            return docRef;
        } catch (error: any) {
            throw new Error(error.message);
        }
    }


    async insertDataUser(collectionId: string, uid: string, data: any) {
        const docRef = doc(this.firestore, `${collectionId}/${uid}`);
        await setDoc(docRef, data);
    }

    async insertDataUserMerge(collectionId: string, uid: string, data: any) {
        const docRef = doc(this.firestore, `${collectionId}/${uid}`);
        await setDoc(docRef, data, { merge: true });
    }

    async getDataById(collectionId: string, docId: string) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}`);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
            return docSnapshot.data();
        } else {
            throw new Error('Document not found');
        }
    }

    async busqueda(collectionId: string, queryField: string, queryData: any) {
        const collectionRef = collection(this.firestore, collectionId);
        const q = query(collectionRef, where(queryField, '==', queryData));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map(doc => doc.data());
    }



    async getDataByIdChild(collectionId: string, docId: string, childCollection: string, childId: string) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}/${childCollection}/${childId}`);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
            return docSnapshot.data();
        } else {
            throw new Error('Document not found');
        }
    }

    async updateData(collectionId: string, docId: string, updatedData: any) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}`);
        await updateDoc(docRef, updatedData);
    }

    async contador(collectionId: string, docId: string, updatedData:any) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}`);
        await updateDoc(docRef, updatedData);
    }

    async contadorEquipo(collectionId: string, docId: string, childCollection: string, childId: string, updatedData: any) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}/${childCollection}/${childId}`);
        await updateDoc(docRef, updatedData);
    }

    async setDataChildChild(collectionId: string, docId: string, childCollection: string, childId: string, childChildCollection: string, childChildId: string, updatedData: any) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}/${childCollection}/${childId}/${childChildCollection}/${childChildId}`);
        await setDoc(docRef, updatedData);
    }

    async updateDataChild(collectionId: string, docId: string, childCollection: string, updatedData: any) {
        const collRef = collection(this.firestore, `${collectionId}/${docId}/${childCollection}`);
        await addDoc(collRef, updatedData);
    }

    async asignarMedicoRepresentante(collectionId: string, docId: string, childCollection: string, childId: string, data:any) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}/${childCollection}/${childId}`);
        await setDoc(docRef, data);
    }

    async agregarDataFecha(collectionId: string, docId: string, childCollection: string, childId: string, innerChildCollection: string, data: any) {
        const collRef = collection(this.firestore, `${collectionId}/${docId}/${childCollection}/${childId}/${innerChildCollection}`);
        await addDoc(collRef, data);
    }

    async deleteData(collectionId: string, docId: string) {
        const docRef = doc(this.firestore, `${collectionId}/${docId}`);
        await deleteDoc(docRef);
    }

    async getUserCourseInteraction(categoria: string, slug: string, uid: string) {
        const docRef = doc(this.firestore, `academia/cursos/${categoria}/${slug}/interaciones/${uid}`);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
            return docSnapshot.data();
        } else {
            throw new Error('Document not found');
        }
    }

    async getLikesAndReproductions(categoria: string, slug: string) {
        const docRef = doc(this.firestore, `academia/cursos/${categoria}/${slug}`);
        const docSnapshot = await getDoc(docRef);
        if (docSnapshot.exists()) {
            return docSnapshot.data();
        } else {
            // If the document doesn't exist, create a new one with default values
            await setDoc(docRef, {}, { merge: true });
            throw new Error('Likes and reproductions data not found');
        }
    }

    searchInteractions(categoria: string, slug: string, uid: string) {
        const collRef = collection(this.firestore, `academia/cursos/${categoria}/${slug}/interacciones`);
        const q = query(collRef);
        return collectionData(q, { idField: 'id' }).pipe(
          map(docArray => docArray.map(doc => doc))
      // map(docArray => docArray.map(doc => ({ id: doc.id, ...doc })))
        );
    }

    academyInteractions(categoria: string, slug: string, uid: string, data: any) {
        const docRef = doc(this.firestore, `academia/cursos/${categoria}/${slug}`);
        const subDocRef = doc(this.firestore, `academia/cursos/${categoria}/${slug}/interacciones/${uid}`);

        getDoc(docRef).then((docSnapshot) => {
            if (docSnapshot.exists()) {
                setDoc(subDocRef, data, { merge: true });
            } else {
                setDoc(docRef, {}, { merge: true });
                setDoc(subDocRef, data, { merge: true });
            }
        });
    }





}
