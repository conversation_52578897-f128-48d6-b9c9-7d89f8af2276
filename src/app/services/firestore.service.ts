import { Injectable } from '@angular/core';
import { FirebaseFirestore } from '@capacitor-firebase/firestore';

@Injectable({
  providedIn: 'root'
})
export class FirestoreService {

  constructor() { }

  addDocument = async () => {
    await FirebaseFirestore.addDocument({
      reference: 'users',
      data: {
        first: 'Alan',
        last: 'Turing',
        born: 1912
      },
    });
  };


  setDocument = async () => {
    await FirebaseFirestore.setDocument({
      reference: 'users/Aorq09lkt1ynbR7xhTUx',
      data: {
        first: 'Alan',
        last: 'Turing',
        born: 1912
      },
      merge: true,
    });
  };

  getDocument = async (reference: string) => {
    const { snapshot } = await FirebaseFirestore.getDocument({
      reference
    });
    return snapshot;
  };

  updateDocument = async () => {
    await FirebaseFirestore.updateDocument({
      reference: 'users/Aorq09lkt1ynbR7xhTUx',
      data: {
        first: 'Alan',
        last: 'Turing',
        born: 1912
      },
    });
  };

  deleteDocument = async () => {
    await FirebaseFirestore.deleteDocument({
      reference: 'users/Aorq09lkt1ynbR7xhTUx',
    });
  };

  getCollection = async () => {
    const { snapshots } = await FirebaseFirestore.getCollection({
      reference: 'users',
      compositeFilter: {
        type: 'and',
        queryConstraints: [
          {
            type: 'where',
            fieldPath: 'born',
            opStr: '==',
            value: 1912,
          },
        ],
      },
      queryConstraints: [
        {
          type: 'orderBy',
          fieldPath: 'born',
          directionStr: 'desc',
        },
        {
          type: 'limit',
          limit: 10,
        },
      ],
    });
    return snapshots;
  };

  enableNetwork = async () => {
    await FirebaseFirestore.enableNetwork();
  };

  disableNetwork = async () => {
    await FirebaseFirestore.disableNetwork();
  };

  addDocumentSnapshotListener = async () => {
    const callbackId = await FirebaseFirestore.addDocumentSnapshotListener(
      {
        reference: 'users/Aorq09lkt1ynbR7xhTUx',
      },
      (event, error) => {
        if (error) {
          console.error(error);
        } else {
          console.log(event);
        }
      }
    );
    return callbackId;
  };

  addCollectionSnapshotListener = async () => {
    const callbackId = await FirebaseFirestore.addCollectionSnapshotListener(
      {
        reference: 'users',
        compositeFilter: {
          type: 'and',
          queryConstraints: [
            {
              type: 'where',
              fieldPath: 'born',
              opStr: '==',
              value: 1912,
            },
          ],
        },
        queryConstraints: [
          {
            type: 'orderBy',
            fieldPath: 'born',
            directionStr: 'desc',
          },
          {
            type: 'limit',
            limit: 10,
          },
        ],
      },
      (event, error) => {
        if (error) {
          console.error(error);
        } else {
          console.log(event);
        }
      }
    );
    return callbackId;
  };

  removeSnapshotListener = async (callbackId: string) => {
    await FirebaseFirestore.removeSnapshotListener({
      callbackId,
    });
  };

  removeAllListeners = async () => {
    await FirebaseFirestore.removeAllListeners();
  };

}
