import { Injectable } from '@angular/core';
import {Platform} from '@ionic/angular';

import { FormGroup, FormControl } from '@angular/forms';

interface ValidationMessages {
  [key: string]: { [key: string]: string };
}

interface FormFields {
  [key: string]: string;
}
@Injectable({
  providedIn: 'root'
})
export class HelperService {

  constructor(private platform: Platform) { }


  // prepareValidationMessage(form: any, validationMessage: string, formFields: any) {
  //   for (const field in formFields) {
  //     formFields[field] = '';
  //     const control = form.controls[field];
  //     if (control && control.invalid) {
  //       const messageObj = validationMessage[field];
  //       for (const key in control.errors) {
  //         formFields[field] = formFields[field] + messageObj[key] + ' ';
  //       }
  //
  //     }
  //   }
  //   return formFields;
  // }


  prepareValidationMessage(form: FormGroup, validationMessages: ValidationMessages, formFields: FormFields): FormFields {
    for (const field in formFields) {
      formFields[field] = '';
      const control = form.get(field) as FormControl;
      if (control && control.invalid) {
        const messageObj = validationMessages[field];
        for (const key in control.errors) {
          formFields[field] += messageObj[key] + ' ';
        }
      }
    }
    return formFields;
  }
  isNativePlatform() {

    return this.platform.is("cordova") || this.platform.is("capacitor");
  }
}
