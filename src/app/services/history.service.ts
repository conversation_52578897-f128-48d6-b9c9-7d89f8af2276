import { Injectable } from '@angular/core';
import {getFirestore} from "firebase/firestore";
import {doc, writeBatch} from "@angular/fire/firestore";

@Injectable({
  providedIn: 'root'
})
export class HistoryService {

  constructor() { }

  async videoInteraction(user: any, videoData: any, action: string, id: string, clase: string, time: any) {
    // Firestore instance
    const db = getFirestore();
    // Start a new batch
    const batch = writeBatch(db);

    if (user.role === 'representante') {
      // const historialRef = doc(db, `representantes/${user.uid}/historial`);
      // const clasesRef = doc(db, `representantes/${user.uid}/historial/${historialRef.id}/clases/${id}`);
      //
      // // Set or update data in 'historial' document
      // batch.set(historialRef, {
      //   action: `${action} el curso: ${clase}`,
      //   date: new Date().valueOf(),
      //   webinar: true,
      //
      //   titulo: this.titulo,
      //   categoria: this.categoria,
      //   descripcion: this.descripcion,
      //
      //   slug: this.slug,
      //
      //   claseId: id,
      //   timePlayed: time
      // }, { merge: true });
      //
      // // Set or update data in 'clases' sub-collection
      // batch.set(clasesRef, {
      //   clase,
      //   date: new Date().valueOf(),
      //   idVimeo: id,
      //   timePlayed: time
      // }, { merge: true });

    } else if (user.role === 'medico') {
      // const historialRef = doc(db, `medicos/${this.user.uid}/historial`);
      // const clasesRef = doc(db, `medicos/${this.user.uid}/historial/${historialRef.id}/clases/${id}`);
      //
      // // Set or update data in 'historial' document
      // batch.set(historialRef, {
      //   action: `${action} el curso: ${clase}`,
      //   date: new Date().valueOf(),
      //   webinar: true,
      //
      //   titulo: this.titulo,
      //   categoria: this.categoria,
      //   descripcion: this.descripcion,
      //
      //   slug: this.slug,
      //
      //   claseId: id,
      //   timePlayed: time
      // }, { merge: true });
      //
      // // Set or update data in 'clases' sub-collection
      // batch.set(clasesRef, {
      //   clase,
      //   date: new Date().valueOf(),
      //   idVimeo: id,
      //   timePlayed: time
      // }, { merge: true });
    }

    // Commit the batch
    try {
      await batch.commit();
      console.log('Batch operation successful');
    } catch (error) {
      console.error('Error in batch operation:', error);
    }
  }

}
