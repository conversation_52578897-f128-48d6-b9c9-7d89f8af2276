import { Injectable } from '@angular/core';
import { Firestore, collection, collectionData, doc, addDoc, deleteDoc, setDoc, getDoc, getDocs, query, where, orderBy, limit, serverTimestamp, runTransaction, updateDoc, Timestamp, DocumentData } from '@angular/fire/firestore';
import { firstValueFrom, Observable } from 'rxjs';
import { Preferences } from '@capacitor/preferences';

interface Visita {
  uuid: string;
  timestamp: Timestamp; // Ahora usamos Timestamp del SDK modular
}

interface Curso {
  views: number;
  // Incluye otras propiedades relevantes del documento aquí
}

interface Interaccion {
  categoria: string;
  slug: string;
  titulo?: string;
  curso?: any;
  tipoInteraccion: string;
  timestamp: Timestamp | Date;
}

@Injectable({
  providedIn: 'root'
})
export class InteractionService {
  uniqueKey: string;

  constructor(private firestore: Firestore) { }

  async academyViews(category: string, slug: string, uuid: string): Promise<number> {
    const cursoRef = doc(this.firestore, `academia/cursos/${category}/${slug}`);

    try {
      const result = await runTransaction(this.firestore, async (transaction) => {
        const cursoDoc = await transaction.get(cursoRef);
        const cursoData = cursoDoc.data() as Curso;
        const nuevasVisitas = cursoData?.views ? cursoData.views + 1 : 1;

        transaction.update(cursoRef, { views: nuevasVisitas });

        return nuevasVisitas;
      });

      console.log('Total de visitas actualizado:', result);
      return result;
    } catch (error) {
      console.error('Error al actualizar total de visitas:', error);
      throw new Error('Error al procesar la transacción');
    }
  }
  /**
   * Registra una interacción del usuario en la academia.
   *
   * @param uuid El identificador único del usuario.
   * @param categoria La categoría del curso o actividad.
   * @param slug El identificador único del curso o actividad.
   * @param tipoInteraccion El tipo de interacción (por ejemplo, 'visitaCurso').
   */
  async registrarInteraccionAcademia(uuid: string, categoria: string, slug: string, data: any): Promise<string> {
    const interaccionesRef = collection(this.firestore, `interacciones/${uuid}/actividades`);
    const hoy = new Date();
    hoy.setHours(0, 0, 0, 0);

    this.uniqueKey = `${uuid}-${categoria}-${slug}-${data.type}`;

    const { value } = await Preferences.get({ key: 'interacciones' });
    let interaccionesGuardadas = value ? JSON.parse(value) : [];
    let idActividad = '';

    const existeInteraccionHoy = interaccionesGuardadas.some(interaccion =>
      interaccion.uniqueKey === this.uniqueKey && new Date(interaccion.fecha).toDateString() === hoy.toDateString()
    );

    if (!existeInteraccionHoy) {
      const nuevaInteraccionRef = await addDoc(interaccionesRef, {
        url: `/academia/${categoria}/${slug}`,
        data,
        uuid,
        timestamp: serverTimestamp()
      });
      idActividad = nuevaInteraccionRef.id;
      console.log('Interacción registrada con éxito. Su ID es:', idActividad);

      interaccionesGuardadas.push({
        type: data.type,
        fecha: hoy.toISOString(),
        uniqueKey: this.uniqueKey,
      });

      await Preferences.set({
        key: 'interacciones',
        value: JSON.stringify(interaccionesGuardadas),
      });
    } else {
      console.log(`Ya se registró una interacción para ${uuid} con ${data.type} el mismo día, evitando duplicado.`);
    }

    return idActividad;
  }


  // /**
  //  * Registra una interacción del usuario en la academia.
  //  *
  //  * @param uuid El identificador único del usuario.
  //  * @param categoria La categoría del curso o actividad.
  //  * @param slug El identificador único del curso o actividad.
  //  * @param tipoInteraccion El tipo de interacción (por ejemplo, 'visitaCurso').
  //  */
  // async registrarInteraccionAcademiaAnterior(uuid: string, categoria: string, slug: string, curso: any, tipoInteraccion: string): Promise<string> {
  //   const interaccionesRef = this.firestoreCompat.collection('interacciones').doc(uuid).collection('actividades');
  //   const hoy = new Date();
  //   hoy.setHours(0, 0, 0, 0); // Ajusta a medianoche de hoy para comparar
  //
  //   // Realiza la consulta
  //   const query = this.firestoreCompat.collection(interaccionesRef.ref.path, ref =>
  //     ref.where('slug', '==', slug)
  //       .where('timestamp', '>=', hoy)
  //       .orderBy('timestamp', 'desc')
  //       .limit(1)
  //   ).get();
  //
  //   // Usa firstValueFrom para manejar el Observable como una promesa
  //   const querySnapshot = await firstValueFrom(query.pipe());
  //
  //   let idActividad = '';
  //
  //   if (!querySnapshot.empty) {
  //     const interaccionDoc = querySnapshot.docs[0];
  //     idActividad = interaccionDoc.id; // Recupera el ID de la actividad existente
  //     // No hay actualización necesaria del timestamp, por lo que no hacemos nada más aquí
  //   } else {
  //     // Si no hay interacción en las últimas 24 horas, crea una nueva
  //     const nuevaInteraccionRef = await interaccionesRef.add({
  //       categoria,
  //       slug,
  //       titulo: curso.titulo,
  //       curso,
  //       tipo: tipoInteraccion,
  //       timestamp: firebase.firestore.FieldValue.serverTimestamp()
  //     });
  //     idActividad = nuevaInteraccionRef.id; // Recupera el ID de la nueva actividad
  //   }
  //
  //   console.log('Interacción registrada con éxito.');
  //   return idActividad;
  // }

  async registrarLeccion(uuid: string, idActividad: string, detallesLeccion: any): Promise<void> {
    const leccionesRef = collection(this.firestore, `interacciones/${uuid}/actividades/${idActividad}/lecciones`);

    await addDoc(leccionesRef, {
      ...detallesLeccion,
      timestamp: serverTimestamp()
    });

    console.log('Detalle de lección registrado con éxito.');
  }

  // Ejemplo de uso en tu servicio o componente
  // async manejarAccesoCursoYLeccion(uuid: string, categoria: string, slug: string, detallesLeccion: any, curso: any) {
  //   const idActividad = await this.registrarInteraccionAcademia(uuid, categoria, slug, curso, 'visitaCurso');
  //   await this.registrarLeccion(uuid, idActividad, detallesLeccion);
  // }


  async registrarReproduccionLeccion(
    uuid: string,
    categoria: string,
    slug: string,
    tituloLeccion: string,
    tipoLeccion: 'video-vimeo' | 'video-youtube' | 'texto' | 'galeria-imagenes' | 'pdf',
    duracion?: number,
    tiempoReproducido?: number
  ): Promise<void> {
    const leccionesRef = collection(this.firestore, `interacciones/${uuid}/lecciones`);

    const nuevaLeccion = {
      categoria,
      slug,
      tituloLeccion,
      tipoLeccion,
      timestamp: serverTimestamp(),
      ...(duracion && { duracion }),
      ...(tiempoReproducido && { tiempoReproducido })
    };

    await addDoc(leccionesRef, nuevaLeccion);
    console.log('Reproducción de lección registrada con éxito.');
  }


  // Método para recuperar las actividades de un usuario específico
  getActividadesUsuario(uuid: string): Observable<any[]> {
    const actividadesRef = collection(this.firestore, `interacciones/${uuid}/actividades`);
    const actividadesQuery = query(actividadesRef, orderBy('timestamp', 'desc'));
    return collectionData(actividadesQuery, { idField: 'id' }) as Observable<any[]>;
  }

// Nueva función para cargar lecciones por demanda para una actividad específica
  getLeccionesDeActividad(uuid: string, idActividad: string): Observable<any[]> {
    const leccionesRef = collection(this.firestore, `interacciones/${uuid}/actividades/${idActividad}/lecciones`);
    return collectionData(leccionesRef, { idField: 'idLeccion' }) as Observable<any[]>;
  }





  /**
   * Registra una interacción del usuario en la academia.
   *
   * @param uuid El identificador único del usuario.
   * @param categoria La categoría del curso o actividad.
   * @param slug El identificador único del curso o actividad.
   * @param tipoInteraccion El tipo de interacción (por ejemplo, 'visitaCurso').
   * @param data El objeto que quieres almacenar en firestore.
   */

  async registrarInteraccion(uuid: string, url: string, data: any): Promise<void> {
    const interaccionesRef = collection(this.firestore, `interacciones/${uuid}/actividades`);
    const hoy = new Date();
    hoy.setHours(0, 0, 0, 0);

    if (data.type === 'springer') {
      console.log('Springer Detectado', data.type);
      const searchData = this.slugify(data.titulo);
      this.uniqueKey = `${uuid}-${searchData}-${data.type}`;
    } else {
      this.uniqueKey = `${uuid}-${url}-${data.type}`;
    }

    const { value } = await Preferences.get({ key: 'interacciones' });
    let interaccionesGuardadas = value ? JSON.parse(value) : [];

    const existeInteraccionHoy = interaccionesGuardadas.some(interaccion =>
      interaccion.uniqueKey === this.uniqueKey && new Date(interaccion.fecha).toDateString() === hoy.toDateString()
    );

    if (!existeInteraccionHoy) {
      await addDoc(interaccionesRef, {
        data,
        uuid,
        url,
        timestamp: serverTimestamp()
      });

      console.log('Interacción registrada con éxito en Firestore.');

      interaccionesGuardadas.push({
        type: data.type,
        fecha: hoy.toISOString(),
        uniqueKey: this.uniqueKey,
      });

      await Preferences.set({
        key: 'interacciones',
        value: JSON.stringify(interaccionesGuardadas),
      });
    } else {
      console.log(`Ya se registró una interacción para ${uuid} con ${data.type} el mismo día, evitando duplicado.`);
    }
  }



  /**
   * Borra una actividad específica por su ID.
   *
   * @param uuid El identificador único del usuario.
   * @param actividadId El ID de la actividad a borrar.
   */
  async borrarActividad(uuid: string, actividadId: string): Promise<void> {
    const actividadDocRef = doc(this.firestore, `interacciones/${uuid}/actividades/${actividadId}`);
    try {
      await deleteDoc(actividadDocRef);
      console.log(`Actividad ${actividadId} borrada con éxito.`);
    } catch (error) {
      console.error('Error al borrar la actividad:', error);
      throw error;
    }
  }


  slugify(text: string): string {
    return text.toString().toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .trim()
      .replace(/^-+|-+$/g, '');
  }




}
