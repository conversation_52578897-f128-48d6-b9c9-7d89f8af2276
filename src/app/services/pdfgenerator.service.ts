import { Injectable } from '@angular/core';
import { CapacitorHttp, HttpResponse } from "@capacitor/core";

@Injectable({
  providedIn: 'root'
})
export class PdfgeneratorService {

  constructor() { }

  generateIpp = async (data: any) => {
    console.log('Arreglo recibido: ', data);

    const options = {
      url: 'https://sanferconecta.live/pdf-64',
      headers: {
        'Content-Type': 'application/json'
      },
      data,
      // responseType se omite aquí, ya que se espera una respuesta en texto
    };
    console.log('Opciones enviadas: ', options);

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);
      console.log('Respuesta API: ', response);

      if (response.data) {
        // Decodificar los datos de base64 a un ArrayBuffer
        const decodedData = atob(response.data);
        const arrayBuffer = new Uint8Array(decodedData.length);
        for (let i = 0; i < decodedData.length; i++) {
          arrayBuffer[i] = decodedData.charCodeAt(i);
        }
        // Convertir el ArrayBuffer a Blob
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        const toReturn = {
          blob: blob,
          base64: response.data
        }
        return toReturn;
      } else {
        throw new Error('No data received from the server');
      }
    } catch (error) {
      console.error('Error al generar el PDF: ', error);
      throw error;
    }
  };

  async catalogoCompleto()  {

    console.log('descargar pdf completo')
    const options = {
      url: 'https://sanferconecta.live/catalogo-completo',
      headers: {
        'Content-Type': 'application/json'
      }
      // responseType se omite aquí, ya que se espera una respuesta en texto
    };
    console.log('Opciones enviadas: ', options);

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);
      console.log('Respuesta API: ', response);

      if (response.data) {
        // Decodificar los datos de base64 a un ArrayBuffer
        const decodedData = atob(response.data);
        const arrayBuffer = new Uint8Array(decodedData.length);
        for (let i = 0; i < decodedData.length; i++) {
          arrayBuffer[i] = decodedData.charCodeAt(i);
        }
// Convertir el ArrayBuffer a Blob
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        const toReturn = {
          blob: blob,
          base64: response.data
        }
        return toReturn;
      } else {
        throw new Error('No data received from the server');
      }
    } catch (error) {
      console.error('Error al generar el PDF: ', error);
      throw error;
    }
  }

  generarIPPCompleta = async (data: any) => {
    console.log('Arreglo recibido: ', data);

    const options = {
      url: 'https://sanferconecta.live/ipp-completa',
      headers: {
        'Content-Type': 'application/json'
      },
      data,
      // responseType se omite aquí, ya que se espera una respuesta en texto
    };
    console.log('Opciones enviadas: ', options);

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);
      console.log('Respuesta API: ', response);

      if (response.data) {
        // Decodificar los datos de base64 a un ArrayBuffer
        const decodedData = atob(response.data);
        const arrayBuffer = new Uint8Array(decodedData.length);
        for (let i = 0; i < decodedData.length; i++) {
          arrayBuffer[i] = decodedData.charCodeAt(i);
        }
// Convertir el ArrayBuffer a Blob
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        const toReturn = {
          blob: blob,
          base64: response.data
        }
        return toReturn;
      } else {
        throw new Error('No data received from the server');
      }
    } catch (error) {
      console.error('Error al generar el PDF: ', error);
      throw error;
    }
  };

  generarFolleto = async (data: any) => {
  console.log('Arreglo recibido: ', data);

  const options = {
    url: 'https://sanferconecta.live/folleto',
    headers: {
      'Content-Type': 'application/json'
    },
    data,
    // responseType se omite aquí, ya que se espera una respuesta en texto
  };
  console.log('Opciones enviadas: ', options);

  try {
  const response: HttpResponse = await CapacitorHttp.post(options);
  console.log('Respuesta API: ', response);

  if (response.data) {
  // Decodificar los datos de base64 a un ArrayBuffer
  const decodedData = atob(response.data);
  const arrayBuffer = new Uint8Array(decodedData.length);
  for (let i = 0; i < decodedData.length; i++) {
  arrayBuffer[i] = decodedData.charCodeAt(i);
}
// Convertir el ArrayBuffer a Blob
const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
const toReturn = {
  blob: blob,
  base64: response.data
}
return toReturn;
} else {
  throw new Error('No data received from the server');
}
} catch (error) {
  console.error('Error al generar el PDF: ', error);
  throw error;
}
};
}
