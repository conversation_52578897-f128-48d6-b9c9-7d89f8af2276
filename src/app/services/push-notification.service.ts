import { Injectable } from '@angular/core';
import {FirebaseMessaging, GetTokenOptions} from "@capacitor-firebase/messaging";
import {environment} from "../../environments/environment";

@Injectable({
  providedIn: 'root'
})
export class PushNotificationService {
  public token = "";

  constructor() { }

  public async requestPermissions(): Promise<void> {
    await FirebaseMessaging.requestPermissions();
  }

  public async checkPermissions() {
    const result = await FirebaseMessaging.checkPermissions();
    return result.receive;
  };

  public  async getToken() {
    const options: GetTokenOptions = {
      vapidKey: environment.firebaseConfig.vapidKey,
    };
    const result = await FirebaseMessaging.getToken(options);
    return result.token;
  };
  public async getDeliveredNotifications () {
    const result = await FirebaseMessaging.getDeliveredNotifications();
    return result.notifications;
  };

  public  async  removeDeliveredNotifications (id: string) {
    await FirebaseMessaging.removeDeliveredNotifications({
      notifications: [{id}],
    });
  };

  public async  removeAllDeliveredNotifications () {
    await FirebaseMessaging.removeAllDeliveredNotifications();
  };


  public async subscribeToTopic (topic: string) {
    const result = await FirebaseMessaging.subscribeToTopic({ topic });
    return result;
  };

  public async unsubscribeFromTopic (topic: string) {
    const result = await FirebaseMessaging.unsubscribeFromTopic({ topic });
    return result;

  };

}
