import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {environment} from '../../environments/environment';
import {RespuestaStrapi} from '../interfaces/strapiInterfaces';

@Injectable({
  providedIn: 'root'
})
export class SpecialtiesService {

  strapiUrl = environment.strapiURL;

  constructor(private http: HttpClient) { }




  categoriaBanner(): Observable<any> {

        return this.http.get(`${this.strapiUrl}/categorias`).pipe(
            map( results => {
                // console.log('RAW', results);
                return results;
            }));
  }


  especialidadesMedicas(): Observable<any> {
    return this.http.get(`${this.strapiUrl}/especialidades`).pipe(
        map( results => {
          // console.log('RAW', results);
          return results;
        }));
  }


  especialidad(id: string): Observable<any> {

    return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/especialidades?id=${id}`).pipe(
        map( results => {
          // console.log('RAW', results);
          return results;

        }));
  }


    categoriasMedicas(id: string): Observable<any> {

        return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/categorias?id=${id}`).pipe(
            map( results => {
                // console.log('RAW', results);
                return results;

            }));
    }
}
