import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {RespuestaSpringer} from '../interfaces/interfaces';
import {map} from 'rxjs/operators';
import {environment} from "../../environments/environment";
import {CapacitorHttp, HttpResponse} from "@capacitor/core";

@Injectable({
  providedIn: 'root'
})
export class SpringerService {
  url = 'https://api.springernature.com/meta/v2/json';

  // url = 'https://api.springernature.com';
  private apiSpringer = environment.apiSpringer;


  constructor( private http: HttpClient) { }

  // Consolidamos en una función que puede manejar la búsqueda inicial y las subsiguientes
  fetchSpringer = async (title: string, start: number = 0) => {
    const options = {
      url: `${this.url}?q=${encodeURI(title)}&p=50&api_key=${this.apiSpringer}&s=${start}`
    };
    console.log('Consulta: ', options);
    const response: HttpResponse = await CapacitorHttp.get(options);
    console.log('Respuesta: ', response);

    return response.data; // Asumiendo que `data` es el objeto que contiene los resultados
  };
  getSpringer = async (title: string) => {
    const options = {
      url: `${this.url}?q=${encodeURI(title)}&p=100&api_key=${this.apiSpringer}`
    };
    console.log('ruta de consulta: ', options)
    const response: HttpResponse = await CapacitorHttp.get(options);
    console.log('Respuessta de la consulta interna : ', response)

    return response
  };
// /meta/v2/json?q=covid&p=100&api_key=ade2d4f7aae127a25f8ce91d0c712c69&s=101
  getMoreSpringer = async (nextPage: string) => {
    const options = {
      url: `${this.url}${nextPage}`
    };
    console.log('ruta de consulta: ', options)
    const response: HttpResponse = await CapacitorHttp.get(options);
    console.log('Respuessta de la consulta interna : ', response)

    return response
  };


  async searchDOI(doi: string) {
    const options = {
      url: `${this.url}?q=${encodeURI(doi)}&api_key=${this.apiSpringer}`
    };
    console.log('Consulta: ', options);
    const response: HttpResponse = await CapacitorHttp.get(options);
    console.log('Respuesta: ', response);
    return response
  }

}
