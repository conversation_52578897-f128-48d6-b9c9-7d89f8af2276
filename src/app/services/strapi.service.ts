import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { RespuestaStrapi } from '../interfaces/strapiInterfaces';
@Injectable({
  providedIn: 'root'
})


export class StrapiService {

    private data = [];

  private strapiUrl = environment.strapiURL;

  constructor( private http: HttpClient) { }

  // strapiUrl = environment.strapiURL;



  private fetchData<T>(path: string, queryParams?: string): Observable<T> {
    return this.http.get<T>(`${this.strapiUrl}/${path}${queryParams ? '?' + queryParams : ''}`)
      .pipe(
        map(results => results),
        catchError(this.handleError)
      );
  }




  getByPrice() {
    return this.http.get('../assets/ByPrice.json').pipe(
        map( results => {

          // console.log('RAW', results);
          return results;

        }));
  }


  getContenido(title: string): Observable<RespuestaStrapi> {
    return this.fetchData<RespuestaStrapi>(title, '_limit=350');
  }



  getCategoria(slug: string): Observable<any> {

        return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/categorias-de-cursos?slug=${slug}`).pipe(
            map( results => {
                // console.log('RAW', results);
                return results;

            }));
  }


  getCategoriaNoticias(slug: string): Observable<any> {

        return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/categorias-de-noticias?slug=${slug}`).pipe(
            map( results => {
                // console.log('RAW', results);
                return results;

            }));
    }





   getCurso(slug: string): Observable<any> {

        return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/webinars?slug=${slug}`).pipe(
            map( results => {
                console.log('RAW', results);
                return results;

            }));
    }


  getContentToStorage(title: string): Observable<any> {
        return this.http.get<RespuestaStrapi>(`${this.strapiUrl}/${title}?_limit=350`).pipe(
            map( results => {

                console.log('RAW', results);
                return results;

            }));
  }



  // setData(id: string, data: any): void {
  //   this.data[id] = data;
  // }

  getData(id: any) {
      return this.data[id];
  }


  private handleError(error: any): Observable<never> {
    // Handle errors here
    // ...
    throw error;
  }
}
