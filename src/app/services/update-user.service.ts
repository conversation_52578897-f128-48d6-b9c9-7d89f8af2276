import { Injectable } from '@angular/core';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, doc, setDoc, deleteDoc, updateDoc, getDoc } from 'firebase/firestore';
import { environment } from "../../environments/environment";
import { CapacitorHttp, HttpResponse } from '@capacitor/core';

import {MedicoResponseAPI, Direccione, RepresentanteResponseAPI} from "../models/apiCRM.model";
import {AlertController, ModalController} from "@ionic/angular";
import {writeBatch} from "@angular/fire/firestore";
import {AuthService} from "./auth.service";
import {UpdateUserPage} from "../pages/modals/update-user/update-user.page";

@Injectable({
  providedIn: 'root'
})
export class UpdateUserService {
  CURRENT_VERSION = environment.version; // Establece la versión actual de la estructura de datos o de la migración

  app = initializeApp(environment.firebaseConfig);
  db = getFirestore(this.app);
  constructor(
    private authService: AuthService,
    private alertController: AlertController,
    private modalCtrl: ModalController
  ) {}

  async updateUserCollections(userId: string, searchValue: any, role: string): Promise<boolean> {

    console.log('Search value', searchValue)
    let userDocRef;
    if (role === 'medico') {
      userDocRef = doc(this.db, `medicos/${userId}`);
    } else if (role === 'representante') {
      userDocRef = doc(this.db, `representantes/${userId}`);
    } else {
      console.error('Rol no soportado.');
      return false; // Devuelve false si el rol no es soportado
    }

    const userSnapshot = await getDoc(userDocRef);
    if (userSnapshot.exists() && userSnapshot.data()['version'] === this.CURRENT_VERSION) {
      console.log('El usuario ya está actualizado a la versión actual. No se requiere acción.');
      return false; // Devuelve false si no se requiere acción
    }

    try {
      await this.deleteCollections(userId, ['historial', 'busquedas'], role);
      await this.migrateDocuments(userId, { 'muestras': 'muestras', 'representantes': 'representantes', 'medicos': 'medicos' }, role);

      let success;
      if (role === 'medico') {
        success = await this.newInfoMedicos(userId, searchValue);
      } else if (role === 'representante') {
        success = true
        // success = await this.newInfoRepresentantes(userId, searchValue); // Asumiendo que searchValue es el email para representantes
      }

      if (success) {
        await updateDoc(userDocRef, { 'version': this.CURRENT_VERSION });
        return true; // Devuelve true al final si todo es exitoso
      } else {
        return false;
      }


    } catch (error) {
      console.error('Error al actualizar las colecciones del usuario:', error);
      return false; // Devuelve false si ocurre un error
    }
  }

  private async deleteCollections(userId: string, collectionsToDelete: string[], role: string) {
    let operationsCount = 0;
    const batch = writeBatch(this.db);

    for (const colName of collectionsToDelete) {
      const colRef = collection(this.db, `${role}s/${userId}/${colName}`);
      const docsSnapshot = await getDocs(colRef);

      if (!docsSnapshot.empty) {
        docsSnapshot.docs.forEach(docSnapshot => {
          batch.delete(docSnapshot.ref);
          operationsCount++;
        });
      }
    }

    if (operationsCount > 0) {
      await batch.commit();
      console.log("Documentos borrados exitosamente.");
    } else {
      console.log("No se encontraron documentos para borrar en las colecciones especificadas.");
    }
  }

  private async migrateDocuments(userId: string, collectionsToMigrate: { [key: string]: string }, role: string) {
    const batch = writeBatch(this.db);
    for (const [sourceCol, destCol] of Object.entries(collectionsToMigrate)) {
      const colRef = collection(this.db, `${role}s/${userId}/${sourceCol}`);
      const docsSnapshot = await getDocs(colRef);
      if (docsSnapshot.empty) {
        console.log(`No se encontraron documentos en la colección '${sourceCol}' para el usuario ${userId}. Omitiendo migración.`);
        continue;
      }
      docsSnapshot.docs.forEach(docSnapshot => {
        const newDocRef = doc(this.db, `interacciones/${userId}/${destCol}`, docSnapshot.id);
        batch.set(newDocRef, docSnapshot.data());
        batch.delete(docSnapshot.ref);
      });
    }
    await batch.commit();
  }

  async newInfoMedicos(userId: string, userInStorage: any) {
    const options = {
      url: 'https://sanferconecta.live/obtener-medico',
      headers: { 'Content-Type': 'application/json' },
      data: { filter: 'cedula', value: userInStorage.cedula },
    };

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);

      if (response.status === 200) {
        const data: MedicoResponseAPI = response.data[0]

        const fechaNacimiento = data.birth_year + '-' + data.birth_month + '-' + data.birth_day
        const newData  = {
          onekey_id: data.onekey_id,
          cedulaGeneral: data.cedula_gral,
          cedulaEspecialidad: data.cedula_esp,
          phone: data.phones,
          sexo: data.sexo,
          especialidadCRM: data.especialidad_1,
          fechaNacimiento,
          especialidadSanferCRM: data.especialidad_sanfer,
          emailCRM: data.email_address,
          status: data.status,
          idUsuarioCRM: data.id_usuario,
          customerIdCRM: data.customer_id,
          direcciones: data.direcciones,
          rutas: data.rutas,
          region: data.region
        }

        const userDocRef = doc(this.db, `medicos/${userId}`);
        await updateDoc(userDocRef, newData);
        return true;

        // console.log('Información del médico actualizada con éxito.');
      } else if (response.status === 404) {

        console.log(404)

        const dataFaltanteActualizada  = await this.presentarAlertaMedicos(userId, userInStorage);

        if (dataFaltanteActualizada) {
          console.log('Alerta mostrada', response.data.message)
          return true;
        } else {
          return false;

        }


      } else {
        console.error('Error en la petición HTTP:', response.status);
        return false;

      }
    } catch (error) {
      console.error('Error al realizar la petición HTTP:', error);
      return false;

    }
  }

  async newInfoRepresentantes(userId: string, email: string) {

    const options = {
      url: 'https://sanferconecta.live/obtener-representante',
      headers: { 'Content-Type': 'application/json' },
      data: { filter: 'email_address', value: email },
    };

    try {
      const response: HttpResponse = await CapacitorHttp.post(options);

      if (response.status === 200) {
        const data: RepresentanteResponseAPI = response.data[0]

        // const fechaNacimiento = data.birth_year + '-' + data.birth_month + '-' + data.birth_day
        const newData  = {
          ruta: data.territory_name,
          nEmpleado: data.unique_integration_id,
          nombreCompleto: data.full_name,
          nombre: data.name,
          apellido: data.lastname,
          username: data.username,
          correo: data.email_address,
          perfil: data.profile_name,

          nombreCompletoJefe: data.manager_full_name,
          nombreJefe: data.manager_name,
          apellidoJefe: data.manager_lastname,
          usuarioJefe: data.manager_username,
          correoJefe: data.manager_email_address,

          fuerza: data.fuerza,
          objetivoMedicos: data.visita_medicos,
          objetivoFarmacias: data.visita_farmacias,
          objetivoHospitales: data.visita_hospitales
        }

        const userDocRef = doc(this.db, `representantes/${userId}`);
        await updateDoc(userDocRef, newData);
        return true;
        // console.log('Información del médico actualizada con éxito.');
      } else if (response.status === 404) {

        console.log(404)
        await this.presentarAlertaRepresentantes(userId);

        console.log('Alerta mostrada', response.data.message)

        return false;
      } else {
        console.error('Error en la petición HTTP:', response.status);
        return false;

      }
    } catch (error) {
      console.error('Error al realizar la petición HTTP:', error);
      return false;

    }
  }

  async presentarAlertaMedicos(userId: string, userInStorage: any) {
    const modal = await this.modalCtrl.create({
      component: UpdateUserPage,
      componentProps: {
        userId,
        userInStorage
      }
    });

    await modal.present();

    const { data } = await modal.onWillDismiss();

    // Aquí simplemente retornas `data`, que será `true` si la actualización fue exitosa,
    // o `false` si se cierra el modal de otra manera o si la actualización falla.
    return data;
  }

  async presentarAlertaMedicosAlert(userId: string, userInStorage: any): Promise<boolean> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header: 'Apoyenos con unas preguntas para mejorar su experiencia en la aplicacion',
        message: `Durante su registro nos proporciono la cedula ${userInStorage.cedula} correspondiente al titulo: ${userInStorage.titulo}`,
        inputs: [
          {
            label: 'Es mi cedula general',
            type: 'radio',
            value: 'general',
          },
          {
            label: 'Es mi cedula de especialidad',
            type: 'radio',
            value: 'especialidad',
          }
        ],

        buttons: [
          {
            text: 'Cancelar',
            role: 'cancel',
            handler: () => {
              resolve(false);
            }
          },
          {
            text: 'Confirmar',
            cssClass: 'primary',
            handler: async (data) => {
              if (data === 'general') {

              } else if( data === 'especialidad') {

              }
              // const tipoCedula = { fechaNacimiento: data.fechaNacimiento };
              // // Asegúrate de tener definido `db` y `userId` correctamente en tu contexto
              // const userDocRef = doc(this.db, `medicos/${userId}`);
              // await updateDoc(userDocRef, fechaNacimiento);
              console.log(data); // Ejemplo de lo que harías
              await this.segundaPregunta(userInStorage);
              // resolve(true);
            }
          }
        ],
        backdropDismiss: false // Esto previene que la alerta se cierre al tocar fuera

      });

      await alert.present();
    });
  }

  async segundaPregunta(userInStorage: any) {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header: 'Apoyenos con unas preguntas para mejorar su experiencia en la aplicacion',
        message: `Durante su registro nos proporciono la cedula ${userInStorage.cedula} correspondiente al titulo: ${userInStorage.titulo}`,
        inputs: [
          {
            label: 'Es mi cedula general',
            type: 'radio',
            value: 'general',
          },
          {
            label: 'Es mi cedula de especialidad',
            type: 'radio',
            value: 'especialidad',
          }
        ],

        buttons: [
          {
            text: 'Cancelar',
            role: 'cancel',
            handler: () => {
              resolve(false);
            }
          },
          {
            text: 'Confirmar',
            cssClass: 'primary',
            handler: async (data) => {
              // const tipoCedula = { fechaNacimiento: data.fechaNacimiento };
              // // Asegúrate de tener definido `db` y `userId` correctamente en tu contexto
              // const userDocRef = doc(this.db, `medicos/${userId}`);
              // await updateDoc(userDocRef, fechaNacimiento);
              console.log(data.value); // Ejemplo de lo que harías
              await this.terceraPregunta(userInStorage)
              // resolve(true);
            }
          }
        ],
        backdropDismiss: false // Esto previene que la alerta se cierre al tocar fuera

      });

      await alert.present();
    });

  }

  async terceraPregunta(userInStorage: any) {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header: 'Apoyenos con unas preguntas para mejorar su experiencia en la aplicacion',
        message: `Durante su registro nos proporciono la cedula ${userInStorage.cedula} correspondiente al titulo: ${userInStorage.titulo}`,
        inputs: [
          {
            label: 'Es mi cedula general',
            type: 'radio',
            value: 'general',
          },
          {
            label: 'Es mi cedula de especialidad',
            type: 'radio',
            value: 'especialidad',
          }
        ],

        buttons: [
          {
            text: 'Cancelar',
            role: 'cancel',
            handler: () => {
              resolve(false);
            }
          },
          {
            text: 'Confirmar',
            cssClass: 'primary',
            handler: async (data) => {
              // const tipoCedula = { fechaNacimiento: data.fechaNacimiento };
              // // Asegúrate de tener definido `db` y `userId` correctamente en tu contexto
              // const userDocRef = doc(this.db, `medicos/${userId}`);
              // await updateDoc(userDocRef, fechaNacimiento);
              console.log(data.value); // Ejemplo de lo que harías
              // resolve(true);
            }
          }
        ],
        backdropDismiss: false // Esto previene que la alerta se cierre al tocar fuera

      });

      await alert.present();
    });
  }

  async presentarAlertaRepresentantes(userId: string) {

    console.log('Alerta iniciada')
    const alert = await this.alertController.create({
      header: 'No encontramos sus datos dentro del CRM',
      subHeader: `Ponte en contacto con el departamento de productividad para que te ayuden a revisar tu usuario.`,
      buttons: [
        {
          text: 'Cerrar sesion',
          role: 'cancel',
          handler: async () => {
            await this.authService.signOut();

          }
        },
        {
          text: 'Confirmar',
          role: 'confirm',
          cssClass: 'primary',
          handler: async () => {


            await this.contactoRepresentanteWhatsapp(userId);
            await this.authService.signOut();

            // const fechaNacimiento = {fechaNacimiento: data.fechaNacimiento}
            // // Asegúrate de tener definido `db` y `userId` correctamente en tu contexto
            // const userDocRef = doc(this.db, `medicos/${userId}`);
            // await updateDoc(userDocRef, fechaNacimiento);
          }
        }
      ],
      backdropDismiss: false // Esto previene que la alerta se cierre al tocar fuera

    });

    await alert.present();

  }

  async contactoRepresentanteWhatsapp(userId: string) {
    const phone = '5536555421'; // Asegúrate de que este número esté en formato internacional sin el '+'
    const baseMessage = `Hola, me pongo en contacto contigo para que me ayudes con mi usuario de representante ya que me notifico que algo malo sucedio, te comparto mi uid: ${userId}`;
    const message = encodeURIComponent(baseMessage); // Codifica el mensaje para URL
    const url = `https://wa.me/52${phone}?text=${message}`; // Construye el enlace

    window.open(url, '_blank'); // Abre el enlace en una nueva pestaña
  }



}
