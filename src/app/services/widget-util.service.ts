import { Injectable } from '@angular/core';
import {Alert<PERSON>ontroller, LoadingController, Platform, ToastController} from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class WidgetUtilService {

  loading: any = {};

  constructor(private toastController: ToastController,
              private platform: Platform,
              private loadingController: LoadingController,
              private alertController: AlertController
  ) { }

  async presentToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 4000,
      buttons: [
  ],
      color: 'success',
      position: this.platform.is('tablet') ? 'top' : 'top'
    });
    toast.present();
  }
  async presentToastTime(message: string, time: number) {
    const toast = await this.toastController.create({
      message,
      duration: time,
      buttons: [
      ],
      color: 'success',
      position: this.platform.is('tablet') ? 'top' : 'top'
    });
    toast.present();
  }


  async presentAlertWithOptions(header: string, message: string, buttonsArray: any[]) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: buttonsArray,
      backdropDismiss: false
    });
    await alert.present();
  }

  async presentToastQuit(message: string) {
    const toast = await this.toastController.create({
      message,
      buttons: [
        {
          side: 'start',
          icon: 'checkmark',
          text: 'OK',
          handler: () => {
            toast.dismiss();
            // console.log('Favorite clicked');
          }
        }
      ],
      color: 'success',
      position: this.platform.is('tablet') ? 'top' : 'top'
    });
    toast.present();
  }

  async presentToastError(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 4500,
      buttons: [

      ],
      color: 'danger',
      position: this.platform.is('tablet') ? 'top' : 'bottom'
    });
    toast.present();
  }
  async presentToastErrorTime(message: string, time: number) {
    const toast = await this.toastController.create({
      message,
      duration: time,
      buttons: [

      ],
      color: 'danger',
      position: this.platform.is('tablet') ? 'top' : 'bottom'
    });
    toast.present();
  }

  async presentLoading() {
    this.loading = await this.loadingController.create({
      message: 'Porfavor espera...', backdropDismiss: true,
      translucent: true,
    });
    return await this.loading.present();
  }

  async presentLoadingMessage(message: string) {
    this.loading = await this.loadingController.create({
      message: `${message}`, backdropDismiss: true,
      translucent: true,
      spinner: "bubbles",
      showBackdrop: true
    });
    return await this.loading.present();
  }

  async dismissLoader() {

    return  await this.loading.dismiss();

  }

  async presentAlertConfirm(header: string, message: string, buttonsArray: any) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: buttonsArray
    });

    await alert.present();
  }


}
