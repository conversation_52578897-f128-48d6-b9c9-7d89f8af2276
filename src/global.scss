/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */


/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";


//@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
//@import "@ionic/angular/css/normalize.css";
//@import "@ionic/angular/css/structure.css";
//@import "@ionic/angular/css/typography.css";
//@import '@ionic/angular/css/display.css';

///* Optional CSS utils that can be commented out */
//@import "@ionic/angular/css/padding.css";
//@import "@ionic/angular/css/float-elements.css";
//@import "@ionic/angular/css/text-alignment.css";
//@import "@ionic/angular/css/text-transformation.css";
//@import "@ionic/angular/css/flex-utils.css";

@import "animate.css/animate.min.css";

@import "swiper/swiper-bundle.css";

.transparent-modal {
  --background: rgba(44, 39, 45, 0.2);
  &::part(content) {
    backdrop-filter: blur(12px);
  }
}


.scanner-ui { display: none; }
.scanner-hide { visibility: visible; }

body.qrscanner { background-color: transparent; }
body.qrscanner .scanner-ui { display: block; }
body.qrscanner .scanner-hide { visibility: hidden; }


.qr-scanner-hide{

  visibility: hidden;
}


ion-popover {

  --width: 80%;
}

.separadores {
  color: #3d3d3d;
  font-size: 12px;
  font-weight: bold;

}


.swiperStyleSanfer {
  --swiper-pagination-color: var(--ion-color-primary);

  --swiper-pagination-left: 10px;
  --swiper-pagination-bottom: 0px;
  //--swiper-pagination-top: -10px;
  --swiper-pagination-bullet-size: 6px;


}

.swiperStyleNews {
  --swiper-pagination-color: var(--ion-color-primary);

  //--swiper-pagination-left: 10px;
  --swiper-pagination-bottom: 20px;
  //--swiper-pagination-top: -10px;
  //--swiper-pagination-bullet-size: 6px;


}


#scanner p {
  color: #fff;
  font-family: sans-serif;
  text-align: center;
  font-weight: 600;
}

.container-scanner {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.container-scanner {
  display: flex;
}
.relative-scanner {
  position: relative;
  z-index: 1;
}
.square-scanner {
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: 0.3s;
}
.square-scanner:after {
  content: '';
  top: 0;
  display: block;
  padding-bottom: 100%;
}
.square-scanner > div {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.surround-cover {
  box-shadow: 0 0 0 99999px var( --ion-color-primary);

}
.barcode-scanner--area--container {
  width: 80%;
  max-width: min(500px, 80vh);
  margin: auto;
}
.barcode-scanner--area--outer {
  display: flex;
  border-radius: 1em;
}
.barcode-scanner--area--inner {
  width: 100%;
  margin: 1rem;
  border: 2px solid var( --ion-color-primary);
  box-shadow: 0px 0px 2px 1px rgb(0 0 0 / 0.5),
  inset 0px 0px 2px 1px rgb(0 0 0 / 0.5);
  border-radius: 1rem;
}


@keyframes shake {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  20% {
    transform: translate(5px, 5px) rotate(-1deg) scale(1.05);
  }
  40% {
    transform: translate(5px, 5px) rotate(-2deg) scale(1.07);
  }
  60% {
    transform: translate(2px, 2px) rotate(0deg) scale(1.04);
  }
  80% {
    transform: translate(-1px, -1px) rotate(-2deg) scale(1.05);
  }
  100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
}




//.cart-modal{
//  --height:50%;
//  --border-radius:10px;
//  padding: 25px;
//}

//Estilos personalizados


.centrar_imagen{
  margin: auto;
  text-align: center;
}


ion-card{

  box-shadow: 0 3px 5px -1 rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12)!important;

}

.error-message {
  color: red;
  font-size: x-small;
}

.user-avatar{
  width: 100px;
  height: 100px;
  border-radius: 20px;
}


.pade-loader{
  top:  45%;
  left: 45%;
  position: absolute;
}


.text-primary{
  color: var( --ion-color-primary);
}

.small_text{
  font-size: x-small;
}

.no_padding_bottom{ padding-bottom: 0!important;}
.no_padding_top{ padding-top: 0!important;}


.bannerVertical .modal-wrapper {
  --height: 80%;
  --border-radius:10px;
  padding: 25px;
}

.calificar .modal-wrapper {
  --height: 40%;
  --border-radius:10px;
  padding: 25px;
}

.clases .modal-wrapper {
  --height: var(--heightModal);
  --width: var(--widthModal);
}


.text-sm-{
  font-size: 6px;
}

.hiddenCol{
  padding: 0px;
}

.qrClose {
  display: none;
}

.dont-display-dots {
  display: none;
}
youtube-player {
  width: 100%;
  display: block;
}
@media (max-width: 600px) {
  .qr .modal-wrapper {
    --height: 90%;
    --border-radius:10px;
    padding: 25px;

  }
  .calificar .modal-wrapper {
    --height: 70%;}

  .qrClose {
    display: block;
  }


}

.invisible-col {
  padding-top: 0;
  padding-bottom: 0;
}




